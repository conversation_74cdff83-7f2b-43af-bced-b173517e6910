﻿namespace BackOffice.ViewModels
{
    // VatReportViewModel.cs
    public class VatReportViewModel
    {
        public string VatCode { get; set; }
        public List<VatReportSubCategory> SubCategories { get; set; }
        public decimal TotalIncVat { get; set; }
        public decimal TotalExcVat { get; set; }
        public decimal TotalVatAmount { get; set; }
    }

    public class VatReportSubCategory
    {
        public string SubCategoryName { get; set; }
        public bool IsSpecial { get; set; }
        public decimal SalesIncVat { get; set; }
        public decimal SalesExcVat { get; set; }
        public decimal VatAmount { get; set; }
    }

    public class VatCodeTotal
    {
        public string VatCode { get; set; }
        public decimal TotalIncVat { get; set; }
        public decimal TotalExcVat { get; set; }
        public decimal TotalVatAmount { get; set; }
    }
}
