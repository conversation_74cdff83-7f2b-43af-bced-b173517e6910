@model List<BackOffice.ViewModels.VatReportViewModel>

<h2>VAT Report</h2>

@if (Model == null || !Model.Any())
{
    <div class="row">
        <div class="col-md-6">
            <form asp-action="Report" method="post">
                <div class="form-group">
                    <label for="startDate">Start Date</label>
                    <input type="date" id="startDate" name="startDate" class="form-control" required />
                </div>
                <div class="form-group">
                    <label for="endDate">End Date</label>
                    <input type="date" id="endDate" name="endDate" class="form-control" required />
                </div>
                <div class="form-group">
                    <button type="submit" class="btn btn-primary">Generate Report</button>
                </div>
            </form>
        </div>
    </div>
}
else
{
    <div class="company-info mb-4">
        <h3>@ViewBag.CompanyName</h3>
        <p><strong>VAT Number:</strong> @ViewBag.VatNumber</p>
        <p><strong>Store:</strong> @ViewBag.StoreName</p>
        <p><strong>Period:</strong> @ViewBag.StartDate.ToString("dd/MM/yyyy") - @ViewBag.EndDate.ToString("dd/MM/yyyy")</p>
    </div>

    @foreach (var vatGroup in Model)
    {
        <h3>VAT Code: @vatGroup.VatCode</h3>
        <table class="table table-bordered">
            <thead>
                <tr>
                    <th>SubCategory</th>
                    <th>Type</th>
                    <th>Sales Inc VAT</th>
                    <th>Sales Exc VAT</th>
                    <th>VAT</th>
                </tr>
            </thead>
            <tbody>
                @foreach (var subCat in vatGroup.SubCategories)
                {
                    <tr>
                        <td>@subCat.SubCategoryName</td>
                        <td>@(subCat.IsSpecial ? "Special Product" : "Regular Product")</td>
                        <td>@subCat.SalesIncVat.ToString("C", new System.Globalization.CultureInfo("en-GB"))</td>
                        <td>@subCat.SalesExcVat.ToString("C", new System.Globalization.CultureInfo("en-GB"))</td>
                        <td>@subCat.VatAmount.ToString("C", new System.Globalization.CultureInfo("en-GB"))</td>
                    </tr>
                }
                <tr class="table-active font-weight-bold">
                    <td colspan="2"><strong>Total</strong></td>
                    <td>@vatGroup.TotalIncVat.ToString("C", new System.Globalization.CultureInfo("en-GB"))</td>
                    <td>@vatGroup.TotalExcVat.ToString("C", new System.Globalization.CultureInfo("en-GB"))</td>
                    <td>@vatGroup.TotalVatAmount.ToString("C", new System.Globalization.CultureInfo("en-GB"))</td>
                </tr>
            </tbody>
        </table>
    }
    
    <h3 class="mt-5">VAT Summary</h3>
    <table class="table table-bordered">
        <thead>
            <tr>
                <th>VAT Code</th>
                <th>Sales Inc VAT</th>
                <th>Sales Exc VAT</th>
                <th>VAT</th>
            </tr>
        </thead>
        <tbody>
            @foreach (var vatTotal in ViewBag.VatCodeTotals)
            {
                <tr>
                    <td>@vatTotal.VatCode</td>
                    <td>@vatTotal.TotalIncVat.ToString("C", new System.Globalization.CultureInfo("en-GB"))</td>
                    <td>@vatTotal.TotalExcVat.ToString("C", new System.Globalization.CultureInfo("en-GB"))</td>
                    <td>@vatTotal.TotalVatAmount.ToString("C", new System.Globalization.CultureInfo("en-GB"))</td>
                </tr>
            }
            <tr class="table-active font-weight-bold">
                <td><strong>Grand Total</strong></td>
                <td>@ViewBag.GrandTotalIncVat.ToString("C", new System.Globalization.CultureInfo("en-GB"))</td>
                <td>@ViewBag.GrandTotalExcVat.ToString("C", new System.Globalization.CultureInfo("en-GB"))</td>
                <td>@ViewBag.GrandTotalVatAmount.ToString("C", new System.Globalization.CultureInfo("en-GB"))</td>
            </tr>
        </tbody>
    </table>
    
    <div class="mt-4">
        <a asp-action="Report" class="btn btn-primary">New Report</a>
    </div>
}
