﻿using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using POS.Core.Models;
using System;

namespace BackOffice.Controllers
{
    [Authorize(Roles = "BackOffice,Admin")]
    public class CashierController : Controller
    {
        private readonly Data _context;

        public CashierController(Data context)
        {
            _context = context;
        }

        // GET: Cashier
        public async Task<IActionResult> Index()
        {
            return View(await _context.Cashiers
            .Include(c => c.CashierStores)
            .ThenInclude(cs => cs.Store)
            .ToListAsync());

        }

        // GET: Cashier/Create
        public IActionResult Create()
        {
            ViewBag.Stores = _context.Stores.ToList();
            return View();
        }

        // POST: Cashier/Create
        [HttpPost]
        [ValidateAntiForgeryToken]
        public async Task<IActionResult> Create([Bind("Name,Password")] Cashier cashier, List<int> StoreIds)
        {
            ModelState.Remove("Stores");

            if (ModelState.IsValid)
            {
                if (StoreIds != null && StoreIds.Any())
                {
                    // Fetch the stores corresponding to the selected IDs
                    cashier.CashierStores = StoreIds
      .Select(storeId => new CashierStore
      {
          StoreId = storeId,
          CashierId = cashier.Id
      })
      .ToList();


                }


                cashier.Role = "Cashier";
                _context.Add(cashier);
                await _context.SaveChangesAsync();
                cashier.UserId = cashier.Id.ToString("D4");
                await _context.SaveChangesAsync();
                return RedirectToAction(nameof(Index));
            }

            ViewBag.Stores = _context.Stores.ToList();
            return View(cashier);
        }


        // GET: Cashier/Edit/5
        public async Task<IActionResult> Edit(int? id)
        {
            if (id == null)
            {
                return NotFound();
            }

            var cashier = await _context.Cashiers
    .Include(c => c.CashierStores)
         .ThenInclude(sc => sc.Store)
    .FirstOrDefaultAsync(m => m.Id == id);


            ViewBag.Stores = _context.Stores.ToList();

            if (cashier == null)
            {
                return NotFound();
            }

            return View(cashier);
        }

        // POST: Cashier/Edit/5
        [HttpPost]
        [ValidateAntiForgeryToken]
        public async Task<IActionResult> Edit(int id, [Bind("Id,Name,Password")] Cashier cashier, List<int> SelectedStoreIds)
        {
            if (id != cashier.Id)
            {
                return NotFound();
            }

            var dbCashier = _context.Cashiers
    .Include(c => c.CashierStores)
        .ThenInclude(sc => sc.Store)
    .FirstOrDefault(c => c.Id == id);

            dbCashier.Name = cashier.Name;
            dbCashier.Password = cashier.Password;


            if (SelectedStoreIds != null && SelectedStoreIds.Any())
            {
                // Fetch the stores corresponding to the selected IDs
                dbCashier.CashierStores.Clear();
                foreach (var storeId in SelectedStoreIds)
                {
                    dbCashier.CashierStores.Add(new CashierStore
                    {
                        CashierId = dbCashier.Id,
                        StoreId = storeId,
                         
                    });
                }
            }


            try
            {
                _context.Update(dbCashier);
                await _context.SaveChangesAsync();
            }
            catch (DbUpdateConcurrencyException)
            {
                if (!CashierExists(cashier.Id))
                {
                    return NotFound();
                }
                else
                {
                    throw;
                }
            }
            return RedirectToAction(nameof(Index));

        }

        // GET: Cashier/Delete/5
        public async Task<IActionResult> Delete(int? id)
        {
            if (id == null)
            {
                return NotFound();
            }

            var cashier = await _context.Cashiers
                .FirstOrDefaultAsync(m => m.Id == id);
            if (cashier == null)
            {
                return NotFound();
            }

            return View(cashier);
        }

        // POST: Cashier/Delete/5
        [ValidateAntiForgeryToken]
        public async Task<IActionResult> DeleteConfirmed(int id)
        {
            var cashier = await _context.Cashiers.FindAsync(id);
            _context.Cashiers.Remove(cashier);
            await _context.SaveChangesAsync();
            return RedirectToAction(nameof(Index));
        }

        private bool CashierExists(int id)
        {
            return _context.Cashiers.Any(e => e.Id == id);
        }
    }
}
