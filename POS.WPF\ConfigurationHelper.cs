﻿using Microsoft.Extensions.Configuration;
using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace POS.WPF
{
    public static class ConfigurationHelper
    {
        private static IConfiguration _configuration;

        static ConfigurationHelper()
        {
            // Build configuration
            _configuration = new ConfigurationBuilder()
                .SetBasePath(Directory.GetCurrentDirectory())
                .AddJsonFile("appsettings.json", optional: false, reloadOnChange: true)
                .Build();
        }

        public static string PrinterName => _configuration["printerName"];
        public static int PrinterCharLength => int.Parse(_configuration["pinterCharLength"]);

        public static string ScannerPort => _configuration["ScannerPort"];

        public static string LogoUrl => _configuration["LogoUrl"];

        public static string PosMachineId => _configuration["PosMachineId"];

        public static DateTime InitialDayStartTime => DateTime.Parse(_configuration["InitialDayStartTime"]);

        public static string PaymentTerminalPort => _configuration["PaymentTerminalPort"];

        public static int PaymentTerminalBaudRate => int.Parse(_configuration["PaymentTerminalBaudRate"]);

        public static bool HideButtons => bool.Parse(_configuration["HideButtons"]);

        public static string ScannerType => _configuration["ScannerType"];

        public static string Address => _configuration["Address"];

        public static string APIUrl => _configuration["APIUrl"];

    }
}
