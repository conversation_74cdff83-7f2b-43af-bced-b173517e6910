﻿@model IEnumerable<POS.Core.Models.ProductStore>

@foreach (var productStore in Model)
{
    <tr>
        <td>
            <input type="hidden" name="ProductStores[@productStore.ProductStoreId].ProductStoreId" value="@productStore.ProductStoreId" />
            @productStore.Product.Name
        </td>
        <td>
            @productStore.Product.SellingPrice.ToString("C")
        </td>
        <td>
            <input type="number" step="0.01" name="ProductStores[@productStore.ProductStoreId].StoreSpecificPrice"
                   value="@(productStore.StoreSpecificPrice.HasValue ? productStore.StoreSpecificPrice.Value.ToString("0.00") : "")"
                   class="form-control" placeholder="N/A" />
        </td>
        <td>
            <input type="number" name="ProductStores[@productStore.ProductStoreId].InventoryCount"
                   value="@productStore.InventoryCount"
                   class="form-control" />
        </td>
    </tr>
}
