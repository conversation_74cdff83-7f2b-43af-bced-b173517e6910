﻿@model POS.Core.Models.Company

@{
    ViewData["Title"] = "Delete";
}

<h1>Delete</h1>

<h3>Are you sure you want to delete this?</h3>
<div>
    <h4>Company</h4>
    <dl class="row">
        <dt class="col-sm-2">Name</dt>
        <dd class="col-sm-10">@Model.Name</dd>

        <dt class="col-sm-2">Address</dt>
        <dd class="col-sm-10">@Model.Address</dd>
    </dl>
</div>
<form asp-action="Delete">
    <input type="hidden" asp-for="Id" />
    <input type="submit" value="Delete" class="btn btn-danger" /> |
    <a asp-action="Index" class="btn btn-secondary">Cancel</a>
</form>
