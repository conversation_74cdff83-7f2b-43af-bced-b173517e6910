﻿@model POS.Core.Models.Brand

@{
    ViewData["Title"] = "Brand Details";
}

<h1>Brand Details</h1>

<div>
    <h4>Brand</h4>
    <hr />
    <dl class="row">
        <dt class="col-sm-2">Name</dt>
        <dd class="col-sm-10">@Model.Name</dd>
    </dl>
</div>

<div>
    <h4>Products</h4>
    <table class="table table-striped">
        <thead>
            <tr>
                <th>Name</th>
                <th>Price</th>
                <th>PLU</th>
            </tr>
        </thead>
        <tbody>
            @foreach (var product in Model.Products)
            {
                <tr>
                    <td>@product.Name</td>
                    <td>@product.PLU</td>
                </tr>
            }
        </tbody>
    </table>
</div>

<div>
    <h4>Stores</h4>
    <table class="table table-striped">
        <thead>
            <tr>
                <th>Store Name</th>
            </tr>
        </thead>
        <tbody>
            @foreach (var store in Model.Stores)
            {
                <tr>
                    <td>@store.StoreName</td>
                </tr>
            }
        </tbody>
    </table>
</div>

<div>
    <a asp-action="Edit" asp-route-id="@Model.Id" class="btn btn-primary">Edit</a>
    <a asp-action="Index" class="btn btn-secondary">Back to List</a>
</div>
