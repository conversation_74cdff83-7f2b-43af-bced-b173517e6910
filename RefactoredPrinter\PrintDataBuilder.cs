﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace RefactoredPrinter
{
    public class PrintDataBuilder
    {
        private PrintData _data = new PrintData();

        public PrintDataBuilder WithPaymentType(PaymentType type)
        {
            _data.PaymentType = type;
            return this;
        }

        public PrintData Build() => _data;
    }
}
