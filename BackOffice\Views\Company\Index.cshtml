﻿@model IEnumerable<POS.Core.Models.Company>

@{
    ViewData["Title"] = "Companies";
}

<h1>Companies</h1>
<p>
    <a asp-action="Create" class="btn btn-primary">Create New</a>
</p>
<table class="table">
    <thead>
        <tr>
            <th>Name</th>
            <th>Address</th>
            <th></th>
        </tr>
    </thead>
    <tbody>
        @foreach (var item in Model)
        {
                <tr>
                    <td>@item.Name</td>
                    <td>@item.Address</td>
                    <td>
                        <a asp-action="Edit" asp-route-id="@item.Id">Edit</a> |
                        <a asp-action="Delete" asp-route-id="@item.Id">Delete</a>
                    </td>
                </tr>
        }
    </tbody>
</table>
