﻿using POS.Core.Models;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace POS.WPF.Tests
{
    public class Db
    {
        public static void Meth()
        {
            using (var centralDb = new Data())
            using (var localDb = new LocalData.LocalContext())
            {
                var shiftList = new List<dynamic>();
                var okAmount = 0;
                var IdIssues = 0;
                var noSalesShifts = 0;
                var localrecordIdsWithoutRemoteId = new List<int>();

                foreach (var shift in localDb.Shifts.ToList())
                {
                    var localDbSalesCount = localDb.Sales.Where(s => s.ShiftId == shift.Id).Count();
                    var centralDbMappedId = localDb.IdMappings
                        .FirstOrDefault(i => i.LocalId == shift.Id && i.EntityType == "Shift")
                        ?.RemoteId ?? 0;

                    if (localDbSalesCount == 0)
                    {
                        noSalesShifts++;
                        continue;
                    }

                    if (shift.Id != 0 && centralDbMappedId == 0)
                    {
                        localrecordIdsWithoutRemoteId.Add(shift.Id);
                        continue;
                    }


                    if (shift.Id == 0 || centralDbMappedId == 0)
                    {
                        IdIssues++;
                        continue;
                    }





                    var centralDbSalesCount = centralDb.Sales.Where(s => s.ShiftId == centralDbMappedId).Count();

                    if (localDbSalesCount != centralDbSalesCount)
                    {
                        shiftList.Add(new
                        {
                            LocalShiftId = shift.Id,
                            RemoteShiftId = centralDbMappedId,
                            LocalCount = localDbSalesCount,
                            RemoteCount = centralDbSalesCount
                        });
                    }
                    else
                    {
                        okAmount++;
                    }
                }
                int x = 9;
            }
        }

        public static void Meth1()
        {
            using (var centralDb = new Data())
            using (var localDb = new LocalData.LocalContext())
            {
                foreach (var shift in localDb.Shifts.ToList())
                {
                    try
                    {
                        var localDbSalesCount = localDb.Sales.Where(s => s.ShiftId == shift.Id).Count();
                        var centralDbMappedId = localDb.IdMappings
                            .FirstOrDefault(i => i.LocalId == shift.Id && i.EntityType == "Shift")
                            ?.RemoteId ?? 0;

                        if (centralDbMappedId == 0)
                            continue;

                        var sales = centralDb.Sales
                        .Where(s => s.ShiftId == centralDbMappedId)
                        .ToList(); // force client-side LINQ for GroupBy/Skip

                        var duplicates = sales
                            .GroupBy(s => s.Barcode)
                            .SelectMany(g => g.OrderBy(s => s.SaleId).Skip(1))
                            .ToList();

                        if (duplicates.Any())
                        {
                            centralDb.Sales.RemoveRange(duplicates);
                            centralDb.SaveChanges();
                        }

                    }
                    catch (Exception ex)
                    {
                        Console.WriteLine($"Error processing shift {shift.Id}: {ex.Message}");
                    }

                }
            }
        }

        public static void Meth3()
        {
            var localDbPos3Sales = new List<Sale>();
            var centralDbPos3Sales = new List<Sale>();

            using (var centralDb = new Data())
            using (var localDb = new LocalData.LocalContext())
            {
                var shifts = localDb.Shifts.Where(s => s.PosMachineId == 3).ToList();

                foreach (var shift in shifts)
                {
                    var centralDbMappedId = localDb.IdMappings
                        .FirstOrDefault(i => i.LocalId == shift.Id && i.EntityType == "Shift")
                        ?.RemoteId ?? 0;

                    localDbPos3Sales.AddRange(localDb.Sales.Where(s => s.ShiftId == shift.Id).ToList());
                    centralDbPos3Sales.AddRange(centralDb.Sales.Where(s => s.ShiftId == centralDbMappedId).ToList());

                }
            }
        }
    }
}
