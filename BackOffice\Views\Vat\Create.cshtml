﻿@model POS.Core.Models.Vat

@{
    ViewData["Title"] = "Create";
}

<h1>Create</h1>

<form asp-action="Create">
    <div class="form-group">
        <label asp-for="Code" class="control-label"></label>
        <input asp-for="Code" class="form-control" />
        <span asp-validation-for="Code" class="text-danger"></span>
    </div>
    <div class="form-group">
        <label asp-for="Value" class="control-label"></label>
        <input asp-for="Value" class="form-control" />
        <span asp-validation-for="Value" class="text-danger"></span>
    </div>
    <div class="form-group">
        <input type="submit" value="Create" class="btn btn-primary" />
    </div>
</form>
<p>
    <a asp-action="Index">Back to List</a>
</p>
