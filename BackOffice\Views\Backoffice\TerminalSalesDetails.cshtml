@model IEnumerable<POS.Core.Models.Sale>

@{
    ViewData["Title"] = "Terminal Sales Details";
    var terminal = ViewBag.Terminal as POS.Core.Models.PosMachine;
}

<h2>Sales for Terminal #@terminal.Id</h2>

<div class="mb-3">
    <a asp-action="TerminalSales" class="btn btn-secondary">Back to Terminals</a>
</div>

<div class="card mb-4">
    <div class="card-header">
        <h5>Filter Options</h5>
    </div>
    <div class="card-body">
        <form id="filterForm" method="get">
            <input type="hidden" name="id" value="@terminal.Id" />
            <div class="row">
                <div class="col-md-4">
                    <div class="form-group">
                        <label for="startDate">Start Date</label>
                        <input type="date" id="startDate" name="startDate" class="form-control" />
                    </div>
                </div>
                <div class="col-md-4">
                    <div class="form-group">
                        <label for="endDate">End Date</label>
                        <input type="date" id="endDate" name="endDate" class="form-control" />
                    </div>
                </div>
                <div class="col-md-4 d-flex align-items-end">
                    <button type="submit" class="btn btn-primary">Apply Filter</button>
                </div>
            </div>
        </form>
    </div>
</div>

<table class="table table-striped">
    <thead>
        <tr>
            <th>Sale ID</th>
            <th>Date</th>
            <th>Total Value</th>
            <th>Status</th>
            <th>Details</th>
        </tr>
    </thead>
    <tbody>
        @foreach (var sale in Model)
        {
            <tr>
                <td>@sale.SaleId</td>
                <td>@sale.Date.ToString("yyyy-MM-dd HH:mm:ss")</td>
                <td>@sale.TotalValue.ToString("C")</td>
                <td>
                    @if (sale.IsOnHold)
                    {
                        <span class="badge bg-warning">On Hold</span>
                    }
                    else if (sale.IsDeleted)
                    {
                        <span class="badge bg-danger">Deleted</span>
                    }
                    else
                    {
                        <span class="badge bg-success">Completed</span>
                    }
                </td>
                <td>
                    <button class="btn btn-sm btn-primary" data-bs-toggle="modal" data-bs-target="#<EMAIL>">
                        View Details
                    </button>
                </td>
            </tr>
        }
    </tbody>
</table>

@foreach (var sale in Model)
{
    <div class="modal fade" id="<EMAIL>" tabindex="-1" aria-hidden="true">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">Sale Details #@sale.SaleId</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <p><strong>Date:</strong> @sale.Date.ToString("yyyy-MM-dd HH:mm:ss")</p>
                    <p><strong>Total Value:</strong> @sale.TotalValue.ToString("C")</p>
                    <p>
                        <strong>Status:</strong>
                        @if (sale.IsOnHold)
                        {
                            <span class="badge bg-warning">On Hold</span>
                        }
                        else if (sale.IsDeleted)
                        {
                            <span class="badge bg-danger">Deleted</span>
                        }
                        else
                        {
                            <span class="badge bg-success">Completed</span>
                        }
                    </p>
                    
                    @if (sale.ProductStoreSales != null && sale.ProductStoreSales.Any())
                    {
                        <h6 class="mt-3">Products</h6>
                        <table class="table table-sm">
                            <thead>
                                <tr>
                                    <th>Product</th>
                                    <th>Quantity</th>
                                    <th>Unit Price</th>
                                    <th>Total</th>
                                </tr>
                            </thead>
                            <tbody>
                                @foreach (var item in sale.ProductStoreSales)
                                {
                                    var productName = item.ProductStore?.Product?.Name ?? "Unknown";
                                    var unitPrice = item.ProductStore?.StoreSpecificPrice ?? 
                                                   (item.ProductStore?.Product?.SellingPrice ?? 0);
                                    <tr>
                                        <td>@productName</td>
                                        <td>@item.Quantity</td>
                                        <td>@unitPrice.ToString("C")</td>
                                        <td>@item.Total.ToString("C")</td>
                                    </tr>
                                }
                            </tbody>
                        </table>
                    }
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Close</button>
                </div>
            </div>
        </div>
    </div>
}


