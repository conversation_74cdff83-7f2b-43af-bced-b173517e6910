﻿@model POS.Core.Models.MorrisonOrder

<h2>Order Details</h2>

<p><strong>Order ID:</strong> @Model.GeneratedId</p>
<p><strong>Store:</strong> @Model.Store.StoreName</p>
<p><strong>Status:</strong> @Model.MorrisonOrderStatus</p>
<p><strong>Created:</strong> @Model.CreatedDate</p>
<p><strong>Expected Delivery:</strong> @Model.ExpectedDeliveryDate</p>

<h3>Order Items</h3>
<table class="table">
    <thead>
        <tr>
            <th>Barcode</th>
            <th>Quantity</th>
        </tr>
    </thead>
    <tbody>
        @foreach (var item in Model.OrderItems)
        {
            <tr>
                <td>@item.Barcode</td>
                <td>@item.Quantity</td>
            </tr>
        }
    </tbody>
</table>

<a asp-action="Edit" asp-route-id="@Model.Id" class="btn btn-warning">Edit</a>
<a asp-action="Index" class="btn btn-secondary">Back to List</a>
