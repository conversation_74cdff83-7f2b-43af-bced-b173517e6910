﻿@model List<POS.Core.Models.User>

@{
    ViewData["Title"] = "Users";
}

<h2>Users</h2>
<a asp-action="Create" class="btn btn-primary">Create New User</a>

<table class="table table-striped mt-3">
    <thead>
        <tr>
            <th>Name</th>
            <th>User ID</th>
            <th>Role</th>
            <th>IP Addresses</th>
            <th>Actions</th>
        </tr>
    </thead>
    <tbody>
        @foreach (var user in Model)
        {
            <tr>
                <td>@user.Name</td>
                <td>@user.UserId</td>
                <td>@(user is POS.Core.Models.Admin ? "Admin" : user is POS.Core.Models.Accountant ? "Accountant" : "BackOffice")</td>
                <td>
                    @if (user is POS.Core.Models.Accountant accountant)
                    {
                        @string.Join(", ", accountant.IpAddresses.Select(ip => ip.Address))
                    }
                    else if (user is POS.Core.Models.BackOffice backOffice)
                    {
                        @string.Join(", ", backOffice.IpAddresses.Select(ip => ip.Address))
                    }
                </td>
                <td>
                    <a asp-action="Edit" asp-route-id="@user.Id" class="btn btn-warning btn-sm">Edit</a>
                    <form asp-action="Delete" asp-route-id="@user.Id" method="post" class="d-inline">
                        <button type="submit" class="btn btn-danger btn-sm" onclick="return confirm('Are you sure?')">Delete</button>
                    </form>
                </td>
            </tr>
        }
    </tbody>
</table>
