﻿@using System.Globalization
@{
    ViewData["Title"] = "Daily Sales Report";
    var dates = ViewBag.Dates as List<DateTime>;
    var report = ViewBag.Report as IEnumerable<dynamic>;
}

<h2>Daily Sales Report</h2>

<form method="post" asp-action="DailySalesReport">
    <div class="form-group">
        <label for="startDate">Start Date:</label>
        <input type="date" id="startDate" name="startDate" class="form-control" required />
    </div>
    <div class="form-group">
        <label for="endDate">End Date:</label>
        <input type="date" id="endDate" name="endDate" class="form-control" required />
    </div>
    <button type="submit" class="btn btn-primary">Generate Report</button>
</form>

@if (dates != null && report != null)
{
    <table class="table table-bordered">
        <thead>
            <tr>
                <th>Category</th>
                @foreach (var date in dates)
                {
                    <th>@date.ToString("dd MMMM yyyy")</th>
                }
            </tr>
        </thead>
        <tbody>
            @foreach (var row in report)
            {
                <tr>
                    <td>@row.Category</td>
                    @foreach (var cell in row.Sales)
                    {
                        <td>@cell.ToString("C", new CultureInfo("en-GB"))</td>
                    }
                </tr>
            }
        </tbody>
    </table>
}
