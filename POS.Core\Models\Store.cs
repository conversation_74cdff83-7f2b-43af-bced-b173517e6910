using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace POS.Core.Models
{

    public class Store : BaseEntity
    {
        public int StoreId { get; set; }
        public string StoreName { get; set; }

        // Navigation Properties
        public ICollection<ProductStore> ProductStores { get; set; } = new List<ProductStore>();

        public ICollection<CashierStore> CashierStores { get; set; } = new List<CashierStore>();

        public int? CompanyId { get; set; }
        public Company? Company { get; set; }

        public Brand Brand { get; set; }
        public int? BrandId { get; set; }

        public string? VatNo { get; set; }

        public string? StoreIdForAPI { get; set; }

        public ICollection<PosMachine> PosMachines { get; set; } = new List<PosMachine>();

        public bool AllowNegativeInventory { get; set; }

        public int? MainPosMachineId { get; set; }

        public ICollection<StoreSubCategoryLimit> SubCategoryLimits { get; set; } = new List<StoreSubCategoryLimit>();
    }


}
