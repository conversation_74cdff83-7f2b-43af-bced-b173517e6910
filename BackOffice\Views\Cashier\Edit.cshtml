﻿@model POS.Core.Models.Cashier

@{
    ViewData["Title"] = "Edit Cashier";
}

<h1>Edit Cashier</h1>

<hr />

<form asp-action="Edit" method="post">
    <div asp-validation-summary="ModelOnly" class="text-danger"></div>
    <input type="hidden" asp-for="Id" />

    <div class="form-group">
        <label asp-for="Name" class="control-label"></label>
        <input asp-for="Name" class="form-control" />
        <span asp-validation-for="Name" class="text-danger"></span>
    </div>

    <div class="form-group">
        <label asp-for="Password" class="control-label"></label>
        <input asp-for="Password" class="form-control" />
        <span asp-validation-for="Password" class="text-danger"></span>
    </div>

    <div class="form-group">
        <label class="control-label">Stores</label>
        <select name="SelectedStoreIds" class="form-control" multiple="multiple" size="10">
            @foreach (var store in ViewBag.Stores)
            {
                var isSelected = Model.CashierStores.Any(cs => cs.StoreId == store.Id);
                <option value="@store.StoreId" selected="@isSelected">@store.StoreName</option>
            }
        </select>
    </div>

    <div class="form-group">
        <button type="submit" class="btn btn-primary">Save</button>
        <a asp-action="Index" class="btn btn-secondary">Back to List</a>
    </div>
</form>

@section Scripts {
    @{
        await Html.RenderPartialAsync("_ValidationScriptsPartial");
    }
}