﻿using ESCPOS_NET.Emitters;
using ESCPOS_NET.Utilities;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace POS.Printer
{
    public class PaidOutPrinter
    {
        public static void Print(string storeId, string name
           , string time, string amount, string cashierName, string serialNumber, string printerName)
        {

            // Create an EPSON emitter
            var emitter = new EPSON();

            // Build commands
            var commands = new List<byte>();

            // Initialize the printer
            commands.Add(0x1B); // ESC
            commands.Add(0x40); // @ (Initialize printer)

            // Mixed alignment line 1
            string leftText1 = $"Store {storeId}";
            string rightText1 = "";
            commands.AddRange(emitter.PrintLine(Helper.CreateMixedAlignmentLine(leftText1, rightText1, 40))); // 48 is the typical character width

            // Mixed alignment line 2
            string leftText2 = "Op Name";
            string rightText2 = cashierName;
            commands.AddRange(emitter.PrintLine(Helper.CreateMixedAlignmentLine(leftText2, rightText2, 40))); // 48 is the typical character width

            commands.AddRange(emitter.PrintLine("")); // Add a blank line
            commands.AddRange(emitter.PrintLine("Paid Out"));
            commands.AddRange(emitter.PrintLine(Helper.CreateMixedAlignmentLine("Serial Number", serialNumber, 40)));
            commands.AddRange(emitter.PrintLine(Helper.CreateMixedAlignmentLine("Name", name, 40)));

            commands.AddRange(emitter.PrintLine(Helper.CreateMixedAlignmentLine("Amount", amount, 40)));
            commands.AddRange(emitter.PrintLine(Helper.CreateMixedAlignmentLine("Time", time, 40)));
            commands.AddRange(emitter.PrintLine(""));

            // Add 3-5 line feeds to advance the paper to the cutter
            for (int i = 0; i < 3; i++)
            {
                commands.Add(0x0A); // Line feed
            }



            // Add cutter command (full cut)
            commands.Add(0x1D); // GS
            commands.Add(0x56); // V
            commands.Add(0x00); // Full cut

            // Get the raw byte array
            byte[] rawCommands = commands.ToArray();

            var ee = new EPSON();
            var cashDrawercommands = ByteSplicer.Combine(
                ee.Initialize(),
                ee.PrintLine(""), // feed paper to ensure printer is active
                new byte[] { 27, 112, 48, 55, 121 }, // try with adjusted pulse durations
                ee.PrintLine("")
            );

            RawPrinterHelper.SendBytesToPrinter(printerName, cashDrawercommands);


            // Send commands to a Windows Printer
            bool isPrinted = RawPrinterHelper.SendBytesToPrinter(printerName, rawCommands);

            if (isPrinted)
            {
                Console.WriteLine("Print job sent to the printer successfully.");
            }
            else
            {
                Console.WriteLine("Failed to send print job to the printer.");
            }

        }
    }
}
