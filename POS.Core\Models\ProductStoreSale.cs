using Microsoft.EntityFrameworkCore.Metadata;

namespace POS.Core.Models
{
    public class ProductStoreSale : BaseEntity
    {
        public int ProductStoreSaleId { get; set; }
        public int ProductStoreId { get; set; }
        public ProductStore ProductStore { get; set; }
        public int SaleId { get; set; }
        public Sale Sale { get; set; }
        public int Quantity { get; set; } // Quantity of products sold

        public bool PriceOverriden { get; set; }

        public bool StaffDiscount { get; set; }

        

        public bool IsPromotion { get; set; }

        public bool IsNonBrandPromotion { get; set; }

        public Promotion? Promotion { get; set; }

        public int? PromotionId { get; set; }

        public NonBrandPromotion? NonBrandPromotion { get; set; }

        public int? NonBrandPromotionId { get; set; }

        public decimal Total { get; set; } // unit price * quantity or overriden price
        public decimal StaffDiscountedAmount { get; set; }
        public decimal OverriddenDiscountAmount { get; set; }

       
      
    }

   

}
