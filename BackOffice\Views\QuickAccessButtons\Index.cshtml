@model IEnumerable<POS.Core.Models.QuickAccessButton>

@{
    ViewData["Title"] = "Quick Access Buttons";
}

<h1>Quick Access Buttons</h1>

<p>
    <a asp-action="Create" class="btn btn-primary">Create New</a>
</p>

<table class="table table-striped">
    <thead>
        <tr>
            <th>Button Text</th>
            <th>Product</th>
            <th>Store</th>
            <th>Actions</th>
        </tr>
    </thead>
    <tbody>
        @foreach (var item in Model)
        {
            <tr>
                <td>@item.ButtonText</td>
                <td>@item.Product.Name</td>
                <td>@item.Store.StoreName</td>
                <td>
                    <a asp-action="Edit" asp-route-id="@item.Id" class="btn btn-sm btn-warning">Edit</a> |
                    <a asp-action="Delete" asp-route-id="@item.Id" class="btn btn-sm btn-danger">Delete</a>
                </td>
            </tr>
        }
    </tbody>
</table>