﻿using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Mvc.Rendering;
using Microsoft.EntityFrameworkCore;
using POS.Core.Models;
using System.Security.Claims;

namespace BackOffice.Controllers
{
    [Authorize(Roles = "Admin,BackOffice")]
    public class SubCatagoryController : Controller
    {
        private readonly Data _context;

        public SubCatagoryController(Data context)
        {
            _context = context;
        }

        // GET: SubCatagory
        [Authorize(Roles = "Admin")]
        public async Task<IActionResult> Index()
        {
            var subCategories = _context.SubCatagories;
            return View(await subCategories.ToListAsync());
        }

        // GET: SubCatagory/Details/5
        public async Task<IActionResult> Details(int? id)
        {
            if (id == null)
                return NotFound();

            var subCatagory = await _context.SubCatagories
                .FirstOrDefaultAsync(m => m.Id == id);

            if (subCatagory == null)
                return NotFound();

            return View(subCatagory);
        }

        // GET: SubCatagory/Create
        public IActionResult Create()
        {
            ViewData["VatId"] = new SelectList(_context.Vats, "Id", "Code");
            return View();
        }

        // POST: SubCatagory/Create
        [HttpPost]
        [ValidateAntiForgeryToken]
        public async Task<IActionResult> Create([Bind("Id,Name,Catagory,StockType,VatId,CashierAlert")] SubCatagory subCatagory)
        {
                _context.Add(subCatagory);
                await _context.SaveChangesAsync();
                return RedirectToAction(nameof(Index));
        }

        // GET: SubCatagory/Edit/5
        public async Task<IActionResult> Edit(int? id)
        {
            if (id == null)
                return NotFound();

            var subCatagory = await _context.SubCatagories.FindAsync(id);
            if (subCatagory == null)
                return NotFound();

            ViewData["VatId"] = new SelectList(_context.Vats, "Id", "Code");
            return View(subCatagory);
        }

        // POST: SubCatagory/Edit/5
        [HttpPost]
        [ValidateAntiForgeryToken]
        public async Task<IActionResult> Edit(int id, [Bind("Id,Name,Catagory,StockType,VatId,CashierAlert")] SubCatagory subCatagory)
        {
            if (id != subCatagory.Id)
                return NotFound();

                try
                {
                    _context.Update(subCatagory);
                    await _context.SaveChangesAsync();
                }
                catch (DbUpdateConcurrencyException)
                {
                    if (!SubCatagoryExists(subCatagory.Id))
                        return NotFound();
                    else
                        throw;
                }
                return RedirectToAction(nameof(Index));
        }

        // GET: SubCatagory/Delete/5
        public async Task<IActionResult> Delete(int? id)
        {
            if (id == null)
                return NotFound();

            var subCatagory = await _context.SubCatagories
                .FirstOrDefaultAsync(m => m.Id == id);

            if (subCatagory == null)
                return NotFound();

            return View(subCatagory);
        }

        // POST: SubCatagory/Delete/5
        [HttpPost, ActionName("Delete")]
        [ValidateAntiForgeryToken]
        public async Task<IActionResult> DeleteConfirmed(int id)
        {
            var subCatagory = await _context.SubCatagories.FindAsync(id);
            if (subCatagory != null)
            {
                _context.SubCatagories.Remove(subCatagory);
                await _context.SaveChangesAsync();
            }
            return RedirectToAction(nameof(Index));
        }

        // GET: SubCatagory/ManageLimits/5
        [Authorize(Roles = "BackOffice")]
        public async Task<IActionResult> ManageLimits(int? id)
        {
            if (id == null)
                return NotFound();

            // Get the current user's store ID
            int? storeId = null;
            if (User.IsInRole("BackOffice"))
            {
                var userId = int.Parse(User.Claims.FirstOrDefault(c => c.Type == ClaimTypes.NameIdentifier)?.Value);
                var backOfficeUser = await _context.BackOffices.FindAsync(userId);
                storeId = backOfficeUser?.StoreId;
            }

            if (!storeId.HasValue)
                return Forbid();

            var subCatagory = await _context.SubCatagories
                .Include(s => s.StoreLimits)
                .ThenInclude(sl => sl.Store)
                .FirstOrDefaultAsync(m => m.Id == id);
            
            if (subCatagory == null)
                return NotFound();
            
            // Only get the store that the BackOffice user is associated with
            var store = await _context.Stores.FindAsync(storeId.Value);
            if (store == null)
                return NotFound();
            
            ViewBag.Stores = new List<Store> { store };
            
            return View(subCatagory);
        }

        // POST: SubCatagory/ManageLimits/5
        [HttpPost]
        [ValidateAntiForgeryToken]
        [Authorize(Roles = "BackOffice")]
        public async Task<IActionResult> ManageLimits(int id, IFormCollection form)
        {
            // Get the current user's store ID
            int? storeId = null;
            if (User.IsInRole("BackOffice"))
            {
                var userId = int.Parse(User.Claims.FirstOrDefault(c => c.Type == ClaimTypes.NameIdentifier)?.Value);
                var backOfficeUser = await _context.BackOffices.FindAsync(userId);
                storeId = backOfficeUser?.StoreId;
            }

            if (!storeId.HasValue)
                return Forbid();

            var subCatagory = await _context.SubCatagories
                .Include(s => s.StoreLimits)
                .FirstOrDefaultAsync(m => m.Id == id);
            
            if (subCatagory == null)
                return NotFound();
            
            // Only process the store that the BackOffice user is associated with
            string key = $"QuantityLimit_{storeId.Value}";
            if (form.ContainsKey(key) && int.TryParse(form[key], out int limit))
            {
                var existingLimit = subCatagory.StoreLimits
                    .FirstOrDefault(sl => sl.StoreId == storeId.Value);
                
                if (existingLimit != null)
                {
                    existingLimit.QuantityLimit = limit;
                }
                else
                {
                    subCatagory.StoreLimits.Add(new StoreSubCategoryLimit
                    {
                        StoreId = storeId.Value,
                        SubCategoryId = id,
                        QuantityLimit = limit
                    });
                }
            }
            
            await _context.SaveChangesAsync();
            return RedirectToAction("Index", "BackOffice");
        }

        // GET: SubCatagory/ManageQuantityLimits
        [HttpGet]
        [Authorize(Roles = "BackOffice,Admin")]
        public async Task<IActionResult> ManageQuantityLimits()
        {
            try
            {
                var subCategories = await _context.SubCatagories
                    .OrderBy(s => s.Name)
                    .ToListAsync();
                
                return View(subCategories);
            }
            catch (Exception ex)
            {
                // Log the exception
                return View(Enumerable.Empty<SubCatagory>());
            }
        }

        private bool SubCatagoryExists(int id)
        {
            return _context.SubCatagories.Any(e => e.Id == id);
        }
    }
}
