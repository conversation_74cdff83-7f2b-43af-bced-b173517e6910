# Soft Delete and Automatic Timestamp Implementation

## Status
Implemented

## Context
We need to track when entities are modified and implement soft deletion across the application.

## Decision
- All entities inherit from BaseEntity
- DbContext automatically handles:
  - LastModified timestamp updates
  - Soft deletion via IsDeleted flag
- Implementation is at DbContext level through SaveChanges override

## Consequences
### Positive
- Consistent handling across application
- No need for manual tracking
- Prevents accidental permanent deletion

### Negative
- Hidden behavior might surprise new developers
- Must remember to use Include() for soft-deleted items if needed

## Notes
- See POS.Core/Models/Data.cs for implementation
- All queries should consider IsDeleted flag