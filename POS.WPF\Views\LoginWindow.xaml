﻿<Window x:Class="POS.WPF.LoginWindow"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
        xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
        xmlns:local="clr-namespace:POS.WPF.Views"
        mc:Ignorable="d"
        Title="Login" WindowStartupLocation="CenterOwner" WindowState="Maximized">
    <Grid x:Name="MainGrid" Background="#004e7c">
        <!-- Watermark Image -->
        <Image x:Name="imgLogo" 
               Opacity="0.2"
               Stretch="Uniform"
               HorizontalAlignment="Center"
               VerticalAlignment="Center"
               Width="800"
               Height="800"/>
        <!-- Login Form -->
        <StackPanel Width="300" 
                    HorizontalAlignment="Right" 
                    VerticalAlignment="Center" 
                    Margin="0,0,50,0">
            <Label x:Name="labelSync"></Label>
            <TextBlock FontSize="18" Text="Username:" Margin="5" Foreground="White"/>
            <TextBox x:Name="txtUN" FontSize="18" Margin="5" Text="0004" GotFocus="GotFocus" />
            <TextBlock FontSize="18" Text="Password:" Margin="5" Foreground="White"/>
            <PasswordBox x:Name="txtPW" FontSize="18" Margin="5" Password="0004" GotFocus="GotFocus" />
            <TextBlock x:Name="syncStatusLabel" 
                      FontSize="14" 
                      Margin="5" 
                      Foreground="White" 
                      TextAlignment="Center"/>
            <Button x:Name="loginButton" 
                    FontSize="18" 
                    Content="Login" 
                    HorizontalAlignment="Right" 
                    Margin="5" 
                    Click="Button_Click" />
            <local:NumericKeypad Grid.Row="4" KeyPressed="NumericKeypad_KeyPressed"/>
        </StackPanel>
    </Grid>
</Window>