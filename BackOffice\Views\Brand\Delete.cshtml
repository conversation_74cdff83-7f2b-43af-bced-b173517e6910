﻿@model POS.Core.Models.Brand

@{
    ViewData["Title"] = "Delete Brand";
}

<h1>Delete Brand</h1>

<div>
    <h4>Are you sure you want to delete this brand?</h4>
    <dl class="row">
        <dt class="col-sm-2">Name</dt>
        <dd class="col-sm-10">@Model.Name</dd>
    </dl>
</div>

<form asp-action="DeleteConfirmed" method="post">
    <input type="hidden" asp-for="Id" />
    <button type="submit" class="btn btn-danger">Delete</button>
    <a asp-action="Index" class="btn btn-secondary">Back to List</a>
</form>
