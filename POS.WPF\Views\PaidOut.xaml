﻿<Window x:Class="POS.WPF.Views.PaidOut"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
        xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
        xmlns:local="clr-namespace:POS.WPF.Views"
        mc:Ignorable="d"
        Title="PaidOut" Height="450" Width="800">
    <Grid Margin="10" Background="#004e7c">
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto" />
            <RowDefinition Height="Auto" />
            <RowDefinition Height="Auto" />
            <RowDefinition Height="Auto" />
            <RowDefinition Height="Auto" />


        </Grid.RowDefinitions>
        <Grid.ColumnDefinitions>
            <ColumnDefinition Width="*" />
        </Grid.ColumnDefinitions>

        <Label FontSize="24" Grid.Row="0">Options</Label>
        <!-- ListBox for PaidOutOptions -->
        <ListBox FontSize="24" Grid.Row="1" ItemsSource="{Binding PaidOutOptions}" 
                 SelectedItem="{Binding SelectedPaidOutOption}" 
                 DisplayMemberPath="Option" />

        <!-- Amount TextBox -->
        <StackPanel  Grid.Row="2" Orientation="Horizontal" Margin="0,10,0,0">
            <TextBlock FontSize="24" Text="Amount: " VerticalAlignment="Center" />
            <TextBox x:Name="txtAmount" FontSize="24" Width="100" Text="{Binding Amount}" />
        </StackPanel>

        <!-- Button to submit -->
        <Button FontSize="24" Grid.Row="3" Content="Submit" Width="100" Margin="290,5,0,5" 
                HorizontalAlignment="Left" Click="SubmitButton_Click" />
        <Button FontSize="24" Grid.Row="3" Content="Cancel" Width="100" Margin="395,5,0,5" 
         HorizontalAlignment="Left" Click="Button_Click" />
        <local:NumericKeypad Grid.Row="4" KeyPressed="NumericKeypad_KeyPressed"/>
    </Grid>
</Window>