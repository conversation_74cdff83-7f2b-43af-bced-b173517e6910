public class AdminUserControllerIntegrationTests : IClassFixture<WebApplicationFactory<Program>>
{
    private readonly WebApplicationFactory<Program> _factory;
    private readonly Data _context;

    public AdminUserControllerIntegrationTests(WebApplicationFactory<Program> factory)
    {
        _factory = factory;
        _context = factory.Services.CreateScope()
            .ServiceProvider.GetRequiredService<Data>();
    }

    [Fact]
    public async Task CompleteUserLifecycle_CreatesEditsAndDeletesUser()
    {
        // Arrange
        var client = _factory.CreateClient();
        var createModel = new UserCreateViewModel
        {
            Name = "TestUser",
            Password = "password123",
            Role = "Admin"
        };

        // Act - Create
        var createResponse = await client.PostAsJsonAsync("/AdminUser/Create", createModel);
        var createdUser = await _context.Users.FirstOrDefaultAsync(u => u.Name == "TestUser");
        
        // Assert - Create
        Assert.NotNull(createdUser);
        Assert.Equal(createModel.Name, createdUser.Name);

        // Act - Edit
        var editModel = new UserEditViewModel
        {
            Id = createdUser.Id,
            Name = "UpdatedName",
            Role = "Admin"
        };
        await client.PostAsJsonAsync("/AdminUser/Edit", editModel);

        // Assert - Edit
        var editedUser = await _context.Users.FindAsync(createdUser.Id);
        Assert.Equal("UpdatedName", editedUser.Name);

        // Act - Delete
        await client.PostAsync($"/AdminUser/Delete/{createdUser.Id}", null);

        // Assert - Delete
        var deletedUser = await _context.Users.FindAsync(createdUser.Id);
        Assert.True(deletedUser.IsDeleted);
    }
}