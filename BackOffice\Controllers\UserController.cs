﻿using BackOffice.ViewModels;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using POS.Core.Models;
using System.Linq;
using System.Threading.Tasks;

namespace BackOffice.Controllers
{
    [Authorize(Roles = "Admin")]
    public class UserController : Controller
    {
        private readonly Data _context;

        public UserController(Data context)
        {
            _context = context;
            // Ensure database is created (optional, for testing purposes)
            _context.Database.EnsureCreated();
        }

        // GET: List Users
        public async Task<IActionResult> Index()
        {
            var users = await _context.Users.ToListAsync();
            return View(users);
        }

        // GET: Create User
        public IActionResult Create()
        {
            return View();
        }

        [HttpPost]
        [ValidateAntiForgeryToken]
        public async Task<IActionResult> Create(UserViewModel model)
        {
            if (ModelState.IsValid)
            {
                User user = null;

                switch (model.UserType)
                {
                    case "Accountant":
                        user = new Accountant
                        {
                            Name = model.Name,
                            Password = model.Password,
                            UserId = model.UserId
                        };
                        break;

                    case "BackOffice":
                        user = new POS.Core.Models.BackOffice
                        {
                            Name = model.Name,
                            Password = model.Password,
                            UserId = model.UserId
                        };
                        break;

                    case "Admin":
                        user = new Admin
                        {
                            Name = model.Name,
                            Password = model.Password,
                            UserId = model.UserId
                        };
                        break;

                    case "Cashier":
                        user = new Cashier
                        {
                            Name = model.Name,
                            Password = model.Password,
                            UserId = model.UserId
                        };
                        break;

                    default:
                        return BadRequest("Invalid user type.");
                }

                // Save the user first
                _context.Users.Add(user);
                await _context.SaveChangesAsync();

                // Add IP addresses after the user is saved
                if (user is Accountant accountant && model.IpAddresses != null)
                {
                    accountant.IpAddresses = model.IpAddresses.Select(ip => new IpAddress
                    {
                        Address = ip,
                        UserId = user.Id // Use the saved user's Id
                    }).ToList();
                }
                else if (user is POS.Core.Models.BackOffice backOffice && model.IpAddresses != null)
                {
                    backOffice.IpAddresses = model.IpAddresses.Select(ip => new IpAddress
                    {
                        Address = ip,
                        UserId = user.Id // Use the saved user's Id
                    }).ToList();
                }

                // Save changes again to persist the IP addresses
                await _context.SaveChangesAsync();

                return RedirectToAction(nameof(Index));
            }

            return View(model);
        }

        // GET: Edit User
        public async Task<IActionResult> Edit(int? id)
        {
            if (id == null)
            {
                return NotFound();
            }

            var user = await _context.Users.FindAsync(id);
            if (user == null)
            {
                return NotFound();
            }

            var viewModel = new UserViewModel
            {
                Id = user.Id,
                UserType = user.GetType().Name,
                UserId = user.UserId,
                Name = user.Name,
                Password = user.Password,
                IpAddresses = user is Accountant accountant
                    ? accountant.IpAddresses.Select(ip => ip.Address).ToList()
                    : user is POS.Core.Models.BackOffice backOffice
                        ? backOffice.IpAddresses.Select(ip => ip.Address).ToList()
                        : new List<string>()
            };

            return View(viewModel);
        }

        [HttpPost]
        [ValidateAntiForgeryToken]
        public async Task<IActionResult> Edit(int id, UserViewModel model)
        {
            if (id != model.Id)
            {
                return NotFound();
            }

            if (ModelState.IsValid)
            {
                try
                {
                    var user = await _context.Users.FindAsync(id);

                    if (user == null)
                    {
                        return NotFound();
                    }

                    user.Name = model.Name;
                    user.Password = model.Password;
                    user.UserId = model.UserId;

                    if (user is Accountant accountant)
                    {
                        // Remove existing IP addresses
                        _context.IpAddresses.RemoveRange(accountant.IpAddresses);

                        // Add new IP addresses
                        accountant.IpAddresses = model.IpAddresses.Select(ip => new IpAddress
                        {
                            Address = ip,
                            UserId = user.Id // Use the saved user's Id
                        }).ToList();
                    }
                    else if (user is POS.Core.Models.BackOffice backOffice)
                    {
                        // Remove existing IP addresses
                        _context.IpAddresses.RemoveRange(backOffice.IpAddresses);

                        // Add new IP addresses
                        backOffice.IpAddresses = model.IpAddresses.Select(ip => new IpAddress
                        {
                            Address = ip,
                            UserId = user.Id // Use the saved user's Id
                        }).ToList();
                    }

                    _context.Update(user);
                    await _context.SaveChangesAsync();
                }
                catch (DbUpdateConcurrencyException)
                {
                    if (!UserExists(model.Id))
                    {
                        return NotFound();
                    }
                    else
                    {
                        throw;
                    }
                }
                return RedirectToAction(nameof(Index));
            }
            return View(model);
        }

        // GET: Delete User
        public async Task<IActionResult> Delete(int? id)
        {
            if (id == null)
            {
                return NotFound();
            }

            var user = await _context.Users.FindAsync(id);
            if (user == null)
            {
                return NotFound();
            }

            return View(user);
        }

        // POST: Delete User
        [HttpPost, ActionName("Delete")]
        [ValidateAntiForgeryToken]
        public async Task<IActionResult> DeleteConfirmed(int id)
        {
            var user = await _context.Users.FindAsync(id);
            if (user != null)
            {
                _context.Users.Remove(user);
                await _context.SaveChangesAsync();
            }

            return RedirectToAction(nameof(Index));
        }

        private bool UserExists(int id)
        {
            return _context.Users.Any(e => e.Id == id);
        }
    }

}