﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Runtime.InteropServices;
using System.Text;
using System.Threading.Tasks;

namespace POS.Printer
{
    internal class RawPrinterHelper
    {
        // Structure and API declarations:
        [StructLayout(LayoutKind.Sequential, CharSet = CharSet.Unicode)]
        public class DOCINFOA
        {
            [MarshalAs(UnmanagedType.LPWStr)]
            public string pDocName;

            [MarshalAs(UnmanagedType.LPWStr)]
            public string pOutputFile;

            [MarshalAs(UnmanagedType.LPWStr)]
            public string pDataType;
        }

        [DllImport("winspool.drv", SetLastError = true, CharSet = CharSet.Unicode)]
        private static extern bool OpenPrinter(string printerName, out IntPtr printerHandle, IntPtr printerDefaults);

        [DllImport("winspool.drv", SetLastError = true, CharSet = CharSet.Unicode)]
        private static extern bool ClosePrinter(IntPtr printerHandle);

        [DllImport("winspool.drv", SetLastError = true, CharSet = CharSet.Unicode)]
        private static extern bool StartDocPrinter(IntPtr printerHandle, int level, [In] DOCINFOA docInfo);

        [DllImport("winspool.drv", SetLastError = true, CharSet = CharSet.Unicode)]
        private static extern bool EndDocPrinter(IntPtr printerHandle);

        [DllImport("winspool.drv", SetLastError = true, CharSet = CharSet.Unicode)]
        private static extern bool StartPagePrinter(IntPtr printerHandle);

        [DllImport("winspool.drv", SetLastError = true, CharSet = CharSet.Unicode)]
        private static extern bool EndPagePrinter(IntPtr printerHandle);

        [DllImport("winspool.drv", SetLastError = true, CharSet = CharSet.Unicode)]
        private static extern bool WritePrinter(IntPtr printerHandle, IntPtr buffer, int bufferLength, out int bytesWritten);

        /// <summary>
        /// Sends raw data to a printer.
        /// </summary>
        /// <param name="printerName">Name of the printer.</param>
        /// <param name="data">Byte array of raw data to send to the printer.</param>
        /// <returns>True if the data was successfully sent; otherwise, false.</returns>
        public static bool SendBytesToPrinter(string printerName, byte[] data)
        {
            IntPtr printerHandle = IntPtr.Zero;
            IntPtr unmanagedData = IntPtr.Zero;

            try
            {
                // Open the printer.
                if (!OpenPrinter(printerName, out printerHandle, IntPtr.Zero))
                {
                    return false;
                }

                // Prepare the document info.
                var docInfo = new DOCINFOA
                {
                    pDocName = "Raw Print Job",
                    pDataType = "RAW"
                };

                // Start a document.
                if (!StartDocPrinter(printerHandle, 1, docInfo))
                {
                    return false;
                }

                // Start a page.
                if (!StartPagePrinter(printerHandle))
                {
                    return false;
                }

                // Allocate unmanaged memory for the data and copy the data to it.
                unmanagedData = Marshal.AllocHGlobal(data.Length);
                Marshal.Copy(data, 0, unmanagedData, data.Length);

                // Write the data to the printer.
                if (!WritePrinter(printerHandle, unmanagedData, data.Length, out int bytesWritten))
                {
                    return false;
                }

                // End the page and the document.
                EndPagePrinter(printerHandle);
                EndDocPrinter(printerHandle);

                return true;
            }
            finally
            {
                // Free unmanaged memory and close the printer handle.
                if (unmanagedData != IntPtr.Zero)
                {
                    Marshal.FreeHGlobal(unmanagedData);
                }

                if (printerHandle != IntPtr.Zero)
                {
                    ClosePrinter(printerHandle);
                }
            }
        }

       
    }
}
 