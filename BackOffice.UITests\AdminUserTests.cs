public class AdminUserUITests : IClassFixture<PlaywrightFixture>
{
    private readonly IPlaywright _playwright;
    private readonly IBrowser _browser;

    public AdminUserUITests(PlaywrightFixture fixture)
    {
        _playwright = fixture.Playwright;
        _browser = fixture.Browser;
    }

    [Fact]
    public async Task CreateUser_ThroughUI_CreatesUserSuccessfully()
    {
        // Arrange
        await using var page = await _browser.NewPageAsync();
        await page.GotoAsync("https://localhost:5001/AdminUser/Create");
        await page.FillAsync("#Name", "TestUser");
        await page.FillAsync("#Password", "password123");
        await page.SelectOptionAsync("#Role", "Admin");

        // Act
        await page.ClickAsync("button[type=submit]");
        await page.WaitForNavigationAsync();

        // Assert
        Assert.Equal("https://localhost:5001/AdminUser/Index", page.Url);
        Assert.Contains("TestUser", await page.TextContentAsync("table"));
    }
}