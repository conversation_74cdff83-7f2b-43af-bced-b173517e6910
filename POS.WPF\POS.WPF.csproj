﻿<Project Sdk="Microsoft.NET.Sdk">

  <PropertyGroup>
    <OutputType>WinExe</OutputType>
    <TargetFramework>net8.0-windows7.0</TargetFramework>
    <ImplicitUsings>enable</ImplicitUsings>
    <UseWPF>true</UseWPF>
    <Configurations>Debug;Release;Pos1;Pos2;Pos3;Pos4</Configurations>
  </PropertyGroup>

  <ItemGroup>
    <Compile Remove="ICommand.cs" />
  </ItemGroup>

  <ItemGroup>
    <None Remove="C:\Users\<USER>\.nuget\packages\nlog.config\4.7.15\contentFiles\any\any\NLog.config" />
    <None Remove="Images\change.png" />
    <None Remove="Images\credit-card.png" />
    <None Remove="Images\delivery.png" />
    <None Remove="Images\discount.png" />
    <None Remove="Images\enter-key.png" />
    <None Remove="Images\EPOS LOGO.png" />
    <None Remove="Images\exchange.png" />
    <None Remove="Images\icons8-hot-food-64.png" />
    <None Remove="Images\icons8-log-out-24.png" />
    <None Remove="Images\icons8-newspaper-50.png" />
    <None Remove="Images\icons8-paid-30.png" />
    <None Remove="Images\icons8-paypoint-24.png" />
    <None Remove="Images\id.png" />
    <None Remove="Images\loyality_update.png" />
    <None Remove="Images\money-back.png" />
    <None Remove="Images\pause.png" />
    <None Remove="Images\power.png" />
    <None Remove="Images\price.png" />
    <None Remove="Images\question.png" />
    <None Remove="Images\reprint.png" />
    <None Remove="Images\return.png" />
    <None Remove="Images\shield.png" />
    <None Remove="Images\sign-out-option.png" />
    <None Remove="Images\third-party.png" />
    <None Remove="Images\to-do-list.png" />
    <None Remove="Images\total.png" />
    <None Remove="Images\void.png" />
  </ItemGroup>

  <ItemGroup>
    <ProjectReference Include="..\POS.Core\POS.Core.csproj" />
    <ProjectReference Include="..\POS.Printer\POS.Printer.csproj" />
    <ProjectReference Include="..\TimeManagement\TimeManagement.csproj" />
  </ItemGroup>

  <ItemGroup>
    <Resource Include="Images\change.png" />
    <Resource Include="Images\credit-card.png" />
    <Resource Include="Images\delivery.png">
      <CopyToOutputDirectory>Never</CopyToOutputDirectory>
    </Resource>
    <Resource Include="Images\discount.png" />
    <Resource Include="Images\exchange.png">
      <CopyToOutputDirectory>Never</CopyToOutputDirectory>
    </Resource>
    <Resource Include="Images\icons8-hot-food-64.png" />
    <Resource Include="Images\icons8-log-out-24.png" />
    <Resource Include="Images\icons8-newspaper-50.png" />
    <Resource Include="Images\icons8-paid-30.png" />
    <Resource Include="Images\icons8-paypoint-24.png" />
    <Resource Include="Images\id.png">
      <CopyToOutputDirectory>Never</CopyToOutputDirectory>
    </Resource>
    <Resource Include="Images\loyality_update.png">
      <CopyToOutputDirectory>Never</CopyToOutputDirectory>
    </Resource>
    <Resource Include="Images\money-back.png" />
    <Resource Include="Images\pause.png" />
    <Resource Include="Images\power.png" />
    <Resource Include="Images\price.png">
      <CopyToOutputDirectory>Never</CopyToOutputDirectory>
    </Resource>
    <Resource Include="Images\question.png" />
    <Resource Include="Images\reprint.png" />
    <Resource Include="Images\return.png">
      <CopyToOutputDirectory>Never</CopyToOutputDirectory>
    </Resource>
    <Resource Include="Images\shield.png" />
    <Resource Include="Images\sign-out-option.png" />
    <Resource Include="Images\third-party.png" />
    <Resource Include="Images\to-do-list.png" />
    <Resource Include="Images\total.png">
      <CopyToOutputDirectory>Never</CopyToOutputDirectory>
    </Resource>
    <Resource Include="Images\void.png">
      <CopyToOutputDirectory>Never</CopyToOutputDirectory>
    </Resource>
  </ItemGroup>

  <ItemGroup>
    <PackageReference Include="EPPlus" Version="7.6.1" />
    <PackageReference Include="InputSimulator" Version="1.0.4" />
    <PackageReference Include="Microsoft.EntityFrameworkCore.Design" Version="8.0.0">
      <PrivateAssets>all</PrivateAssets>
      <IncludeAssets>runtime; build; native; contentfiles; analyzers; buildtransitive</IncludeAssets>
    </PackageReference>
    <PackageReference Include="Microsoft.EntityFrameworkCore.Sqlite" Version="8.0.0" />
    <PackageReference Include="Microsoft.Extensions.Configuration" Version="9.0.1" />
    <PackageReference Include="Microsoft.Extensions.Configuration.Binder" Version="9.0.1" />
    <PackageReference Include="Microsoft.Extensions.Configuration.Json" Version="9.0.1" />
    <PackageReference Include="Microsoft.Extensions.DependencyInjection" Version="8.0.0" />
    <PackageReference Include="Microsoft.Extensions.Hosting" Version="8.0.0" />
    <PackageReference Include="NLog" Version="5.4.0" />
    <PackageReference Include="System.Drawing.Common" Version="9.0.0" />
  </ItemGroup>

  <ItemGroup>
    <None Update="appsettings.json">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </None>
    <None Update="errors.txt">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </None>
    <None Update="NLog.Config">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </None>
    <None Update="Settings.settings">
      <Generator>SettingsSingleFileGenerator</Generator>
      <LastGenOutput>Settings.Designer.cs</LastGenOutput>
    </None>
  </ItemGroup>

  <ItemGroup>
    <Folder Include="Model\" />
    <Folder Include="Mocks\" />
  </ItemGroup>

  <ItemGroup>
    <Reference Include="Integral.Library.GuardianClient">
      <HintPath>..\..\..\..\..\..\sagepay\guardian\bin\Integral.Library.GuardianClient.dll</HintPath>
    </Reference>
  </ItemGroup>

  <ItemGroup>
    <Resource Include="Images\enter-key.png" />
  </ItemGroup>

  <ItemGroup>
    <Resource Include="Images\EPOS LOGO.png" />
  </ItemGroup>

  <ItemGroup>
    <Compile Update="Resource1.Designer.cs">
      <DesignTime>True</DesignTime>
      <AutoGen>True</AutoGen>
      <DependentUpon>Resource1.resx</DependentUpon>
    </Compile>
    <Compile Update="Settings.Designer.cs">
      <DesignTimeSharedInput>True</DesignTimeSharedInput>
      <AutoGen>True</AutoGen>
      <DependentUpon>Settings.settings</DependentUpon>
    </Compile>
  </ItemGroup>

  <ItemGroup>
    <EmbeddedResource Update="Resource1.resx">
      <Generator>ResXFileCodeGenerator</Generator>
      <LastGenOutput>Resource1.Designer.cs</LastGenOutput>
    </EmbeddedResource>
  </ItemGroup>

</Project>
