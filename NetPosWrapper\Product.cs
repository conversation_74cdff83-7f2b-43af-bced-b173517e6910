﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace NetPosWrapper
{


    public class Product
    {
        public RecordType RecordType { get; set; }
        public string ProductCode { get; set; }
        public string Barcode { get; set; }
        public string CaseQuantity { get; set; }
        public string PurchasePrice { get; set; }
        public string RetailPrice { get; set; }
        public string VatCode { get; set; }
        public string Description { get; set; }
        public string ShortProductName { get; set; }

        public string Department { get; set; }

        public string Category { get; set; }
    }
}
