﻿@model POS.Core.Models.Brand

@{
    ViewData["Title"] = "Edit Brand";
}

<h1>Edit Brand</h1>

<form asp-action="Edit" enctype="multipart/form-data">
    <input type="hidden" asp-for="Id" />
    <div class="form-group">
        <label asp-for="Name" class="control-label"></label>
        <input asp-for="Name" class="form-control" />
    </div>
    <div class="form-group">
        <label asp-for="BrandLogo" class="control-label"></label>
        <input type="file" name="logoFile" class="form-control" accept=".png" />
    </div>
    @if (!string.IsNullOrEmpty(Model.BrandLogo))
    {
        <div class="form-group">
            <img src="~/images/BrandLogos/@Model.BrandLogo" alt="Brand Logo" width="150" />
        </div>
    }
    <button type="submit" class="btn btn-primary">Save</button>
    <a asp-action="Index" class="btn btn-secondary">Back to List</a>
</form>

