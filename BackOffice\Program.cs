using BackOffice;
using Microsoft.AspNetCore.Authentication.Cookies;
using Microsoft.EntityFrameworkCore;
using POS.Core.Models;
using BackOffice.Hubs;
using BackOffice.Configuration;
using BackOffice.Middleware;

var builder = WebApplication.CreateBuilder(args);

// Add services to the container.
builder.Services.AddControllersWithViews();
builder.Services.AddHttpClient();
builder.Services.AddDbContext<Data>();
builder.Services.AddScoped<ReportsService>();
builder.Services.AddHostedService<EmailBackgroundService>();
//builder.Services.AddHostedService<MorrisonOrderConfirmationService>();
builder.Services.AddScoped<IEmailService, EmailService>();
builder.Services.AddSignalR();

// Add API controller support
builder.Services.AddControllers().AddJsonOptions(options =>
{
    options.JsonSerializerOptions.PropertyNameCaseInsensitive = true;
    options.JsonSerializerOptions.AllowTrailingCommas = true;
    options.JsonSerializerOptions.ReadCommentHandling = System.Text.Json.JsonCommentHandling.Skip;
    options.JsonSerializerOptions.NumberHandling = System.Text.Json.Serialization.JsonNumberHandling.AllowReadingFromString;
});

builder.Services.AddAuthentication(CookieAuthenticationDefaults.AuthenticationScheme)
    .AddCookie(options =>
    {
        options.LoginPath = "/Auth/Login";
        options.AccessDeniedPath = "/Auth/AccessDenied";
    });

builder.Services.AddAuthorization();

builder.Services.Configure<MorrisonsPromotionsConfig>(
    builder.Configuration.GetSection("MorrisonsPromotions"));

builder.Services.Configure<MorrisonsApiConfig>(   //this is for products.
    builder.Configuration.GetSection("MorrisonsApi"));

var app = builder.Build();

// Configure the HTTP request pipeline.
if (!app.Environment.IsDevelopment())
{
    app.UseExceptionHandler("/Home/Error");
    app.UseHsts();
}

app.UseHttpsRedirection();
app.UseStaticFiles();

app.UseRouting();
app.UseAuthentication();
app.UseMiddleware<IpRestrictionMiddleware>();
app.UseAuthorization();

app.MapHub<SyncProgressHub>("/syncProgressHub");
app.MapControllerRoute(
    name: "default",
    pattern: "{controller=Home}/{action=Index}/{id?}");

// Add explicit API route mapping
app.MapControllers(); // This is crucial for API controllers to work

app.Run();
