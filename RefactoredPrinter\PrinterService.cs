﻿using RefactoredPrinter.Interfaces;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace RefactoredPrinter
{
    public class PrinterService
    {
        private readonly IPrinterEmitter _emitter;

        public PrinterService(IPrinterEmitter emitter)
        {
            _emitter = emitter;
        }

        public void Print(IPrintDocument document, string printerName)
        {
            var commands = document.GetPrintCommands(_emitter);
            RawPrinterHelper.SendBytesToPrinter(printerName, commands.ToArray());
        }
    }
}
