﻿using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Mvc.Rendering;
using Microsoft.EntityFrameworkCore;
using POS.Core.Models;
using TimeManagement;

namespace BackOffice.Controllers
{
    [Authorize(Roles = "Admin")]
    public class ProductsController : Controller
    {
        private readonly Data _context;

        public ProductsController(Data context)
        {
            _context = context;
        }

        // GET: Products
        public async Task<IActionResult> Index()
        {
            var products = _context.Products
                .Include(p => p.Brand)
                .Include(p => p.Division)
                .Include(p => p.Vat);
            return View(await products.ToListAsync());
        }

        // GET: Products/Create
        public IActionResult Create()
        {
            var brands = _context.Brands
       .Select(b => new { b.Id, b.Name })
       .ToList();



            brands.Insert(0, new { Id = 0, Name = "*No Brand*" });

            ViewData["BrandId"] = new SelectList(brands, "Id", "Name");
            ViewData["DivisionId"] = new SelectList(_context.Divisions, "Id", "Name");
            ViewData["VatId"] = new SelectList(_context.Vats, "Id", "Code");
            return View();
        }

        // POST: Products/Create
        [HttpPost]
        [ValidateAntiForgeryToken]
        public async Task<IActionResult> Create([Bind("Id,Name,Price,PLU,Barcode,Description,PurchasePrice,SellingPrice,PurchasePackSize,DivisionId,BrandId,VatId")] Product product)
        {
            ModelState.Remove(nameof(product.Brand));
            ModelState.Remove(nameof(product.Division));
            ModelState.Remove(nameof(product.Vat));

            if (product.BrandId == 0)
            {
                product.BrandId = null;
                product.Brand = null;

            }


            _context.Add(product);
            await _context.SaveChangesAsync();

            if (product.BrandId == null)
            {

                foreach (var store in _context.Stores)
                {
                    store.ProductStores.Add(new ProductStore()
                    {
                        InventoryCount = 0,
                        ProductId = product.Id,
                        StoreId = store.StoreId,
                    });
                }


                await _context.SaveChangesAsync();
            }
            else 
            {
                var brand = _context.Brands.Include(b => b.Stores).FirstOrDefault(b => b.Id == product.BrandId);
                foreach (var store in brand.Stores)
                {
                    store.ProductStores.Add(new ProductStore()
                    {
                        InventoryCount = 0,
                        ProductId = product.Id,
                        StoreId = store.StoreId,
                    });
                }


                await _context.SaveChangesAsync();
            }

            return RedirectToAction(nameof(Index));

        }

        // GET: Products/Edit/5
        public async Task<IActionResult> Edit(int? id)
        {
            if (id == null)
                return NotFound();

            var product = await _context.Products.FindAsync(id);
            if (product == null)
                return NotFound();

            ViewData["BrandId"] = new SelectList(_context.Brands, "Id", "Name", product.BrandId);
            ViewData["DivisionId"] = new SelectList(_context.SubCatagories, "Id", "Name", product.DivisionId);
            ViewData["VatId"] = new SelectList(_context.Vats, "Id", "Code", product.VatId);
            return View(product);
        }

        // POST: Products/Edit/5
        [HttpPost]
        [ValidateAntiForgeryToken]
        public async Task<IActionResult> Edit(int id, [Bind("Id,Name,Price,PLU,Barcode,Description,PurchasePrice,SellingPrice,PurchasePackSize,DivisionId,BrandId,VatId")] Product product)
        {
            if (id != product.Id)
                return NotFound();

            ModelState.Remove(nameof(product.Brand));
            ModelState.Remove(nameof(product.Division));
            ModelState.Remove(nameof(product.Vat));

            try
            {
                _context.Update(product);
                await _context.SaveChangesAsync();
            }
            catch (DbUpdateConcurrencyException)
            {
                if (!ProductExists(product.Id))
                    return NotFound();
                else
                    throw;
            }
            return RedirectToAction(nameof(Index));
        }

        // GET: Products/Delete/5
        public async Task<IActionResult> Delete(int? id)
        {
            if (id == null)
                return NotFound();

            var product = await _context.Products
                .Include(p => p.Brand)
                .Include(p => p.Division)
                .FirstOrDefaultAsync(m => m.Id == id);

            if (product == null)
                return NotFound();

            return View(product);
        }

        // POST: Products/Delete/5
        [HttpPost, ActionName("Delete")]
        [ValidateAntiForgeryToken]
        public async Task<IActionResult> DeleteConfirmed(int id)
        {
            var product = await _context.Products.FindAsync(id);
            _context.Products.Remove(product);
            await _context.SaveChangesAsync();
            return RedirectToAction(nameof(Index));
        }

        private bool ProductExists(int id)
        {
            return _context.Products.Any(e => e.Id == id);
        }
    }
}
