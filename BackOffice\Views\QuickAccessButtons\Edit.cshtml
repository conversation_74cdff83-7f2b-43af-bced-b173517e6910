@model POS.Core.Models.QuickAccessButton

@{
    ViewData["Title"] = "Edit Quick Access Button";
}

<h1>Edit Quick Access Button</h1>

<form asp-action="Edit">
    <input type="hidden" asp-for="Id" />
    
    <div class="form-group">
        <label asp-for="ButtonText" class="control-label">Button Text</label>
        <input asp-for="ButtonText" class="form-control" />
        <span asp-validation-for="ButtonText" class="text-danger"></span>
    </div>
    
    <div class="form-group">
        <label asp-for="ProductId" class="control-label">Product</label>
        <select asp-for="ProductId" class="form-control" asp-items="ViewBag.ProductId"></select>
        <span asp-validation-for="ProductId" class="text-danger"></span>
    </div>
    
    <div class="form-group">
        <label asp-for="StoreId" class="control-label">Store</label>
        <select asp-for="StoreId" class="form-control" asp-items="ViewBag.StoreId"></select>
        <span asp-validation-for="StoreId" class="text-danger"></span>
    </div>
    
    <div class="form-group mt-3">
        <input type="submit" value="Save" class="btn btn-primary" />
        <a asp-action="Index" class="btn btn-secondary">Back to List</a>
    </div>
</form>

@section Scripts {
    @{await Html.RenderPartialAsync("_ValidationScriptsPartial");}
}