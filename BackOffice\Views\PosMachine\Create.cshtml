﻿@model POS.Core.Models.PosMachine

@{
    ViewData["Title"] = "Create POS Machine";
}

<h2>Create POS Machine</h2>

<form asp-action="Create">
    

    <div class="form-group">
        <label>Store</label>
        <select asp-for="Store.StoreId" class="form-control">
            @foreach (var store in ViewBag.Stores)
            {
                <option value="@store.StoreId">@store.StoreName</option>
            }
        </select>
    </div>

    <button type="submit" class="btn btn-success">Save</button>
</form>
