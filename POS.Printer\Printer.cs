﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using ESCPOS_NET.Printers;
using ESCPOS_NET.Emitters;
using System.Threading.Tasks;
using Microsoft.VisualBasic;
using System.Dynamic;
using System.Reflection.Metadata;
using System.ComponentModel;
using ESCPOS_NET.Utilities;

//
namespace POS.Printer
{

    public enum ProductPriceReductionType
    {
        
        None,
        [Description("Staff Discount")]
        StaffDiscount,
        [Description("Price Override")]
        PriceOverride,
        Promotion,
        [Description("Non-Brand Promotion")]
        NonBrandPromotion
    }

    public record struct VatEntry(string VatCode,string VatRate, decimal ExVat, decimal Vat, decimal IncVat);
    public record struct Product(string ProductDescription, decimal Price, ProductPriceReductionType PriceReduction, decimal reductionAmount);

    public class PrintData
    {
        public string StoreId { get; set; }

        public string TransactionId { get; set; }

        public string CashierName { get; set; }

        public string Time { get; set; }

        public string Total { get; set; }

        public string Barcode { get; set; }

        public string PosMachineId { get; set; }


        public string VatNo { get; set; }

        public PaymentType PaymentType1 { get; set; }

        public PaymentType? PaymentType2 { get; set; }

        public decimal Type2Amount { get; set; }

        public string StoreName { get; set; }

        public List<string> CashierAlerts { get; set; }

        public List<VatEntry> VatEntries { get; set; }

        //for cash payements
        public string PaidAmount { get; set; }

        public string ChangeDue { get; set; }


        public List<Product> Products { get; set; }

        // New property for customer receipt from card terminal
        public List<string> CustomerReceipt { get; set; }

        // Constructor to enforce PaymentType initialization
        public PrintData(PaymentType paymentType1, PaymentType? paymentType2)
        {
            PaymentType1 = paymentType1;
            PaymentType2 = paymentType2;
            CustomerReceipt = new List<string>();
        }
    }


    public enum PaymentType
    {
        Cash = 1,
        Card = 2,
        Voucher = 3,
        [Description("PP Card")]
        PPCard = 4,
        Uber = 5,
        Deliveroo = 6
    }


    public class Printer
    {
        public static void Print(PrintData obj, string address, string printerName)
        {
            // Create an EPSON emitter
            var emitter = new EPSON();

            var commands = new List<byte>();

            // Initialize the printer
            commands.Add(0x1B); // ESC
            commands.Add(0x40); // @ (Initialize printer)

            // Build receipt content
            commands.AddRange(emitter.CenterAlign());
            address.Split(',').ToList().ForEach(x => commands.AddRange(emitter.PrintLine(x)));
            commands.AddRange(emitter.PrintLine("")); // Blank line

            // Add current date and time
            string currentDateTime = DateTime.Now.ToString("dd/MM/yyyy HH:mm:ss");
            commands.AddRange(emitter.PrintLine(currentDateTime));
            commands.AddRange(emitter.PrintLine("")); // Blank line after date/time

            // Add "Original" line with decoration
            commands.AddRange(emitter.PrintLine("==--==--==-- Original --==--==--=="));
            commands.AddRange(emitter.PrintLine("")); // Blank line for spacing

            // Mixed alignment line 1
            string leftText1 = $"Store {obj.StoreId} POS {obj.PosMachineId}";
            string rightText1 = $"Trans {obj.TransactionId}";
            commands.AddRange(emitter.PrintLine(Helper.CreateMixedAlignmentLine(leftText1, rightText1, 40))); // 48 is the typical character width

            // Mixed alignment line 2
            string leftText2 = "Op Name";
            string rightText2 = obj.CashierName;
            commands.AddRange(emitter.PrintLine(Helper.CreateMixedAlignmentLine(leftText2, rightText2, 40))); // 48 is the typical character width

            commands.AddRange(emitter.PrintLine("")); // Add a blank line




            foreach (var item in obj.Products)
            {
                commands.AddRange(emitter.PrintLine(Helper.CreateMixedAlignmentLine(item.ProductDescription, item.Price.ToString(), 40))); // 48 is the typical character width
                if(item.PriceReduction == ProductPriceReductionType.StaffDiscount || item.PriceReduction == ProductPriceReductionType.PriceOverride)
                {
                    commands.AddRange(emitter.PrintLine(Helper.CreateMixedAlignmentLine($"   {item.PriceReduction.GetDescription()}", $"-{item.Price.ToString()}", 40)));
                }
                else
                {
                   // commands.AddRange(emitter.PrintLine(Helper.CreateMixedAlignmentLine($"   **Promotion**", "", 40)));
                }
                commands.AddRange(emitter.PrintLine("----------------------------------------"));
            }
            commands.AddRange(emitter.PrintLine(Helper.CreateMixedAlignmentLine("Total", obj.Total, 40))); // 48 is the typical character width

            commands.AddRange(emitter.PrintLine("----------------------------------------"));
            if (obj.PaymentType1 == PaymentType.Cash)
            {
                commands.AddRange(emitter.PrintLine(Helper.CreateMixedAlignmentLine("Paid GBP", obj.PaidAmount, 40)));
                commands.AddRange(emitter.PrintLine(Helper.CreateMixedAlignmentLine("Change Due", obj.ChangeDue, 40)));

                if(obj.PaymentType2 != null)
                {

                    commands.AddRange(emitter.PrintLine("----------------------------------------"));
                    commands.AddRange(emitter.PrintLine(Helper.CreateMixedAlignmentLine($"{obj.PaymentType2} Payment", obj.Type2Amount.ToString(), 40)));
                }
            }
            else
            {
                commands.AddRange(emitter.PrintLine(Helper.CreateMixedAlignmentLine($"{obj.PaymentType1} Payment", obj.Total, 40)));
            }
                commands.AddRange(emitter.PrintLine("----------------------------------------"));

            commands.AddRange(emitter.PrintLine(""));
            commands.AddRange(emitter.PrintLine(Helper.CreateMixedAlignmentLine($"Shop - {obj.StoreName}","", 40)));
            commands.AddRange(emitter.PrintLine(Helper.CreateMixedAlignmentLine("Vat No.", obj.VatNo, 40)));

            commands.AddRange(emitter.PrintLine(Helper.CreateFourColumnLine("VAT Rate", "Ex.VAT", "VAT", "Inc.VAT", 40)));
            if(obj.VatEntries.Any(v => v.VatCode == "CODE 0"))
            commands.AddRange(emitter.PrintLine(Helper.CreateFourColumnLine("20%", obj.VatEntries.First(v => v.VatCode == "CODE 0").ExVat.ToString(), obj.VatEntries.First(v => v.VatCode == "CODE 0").Vat.ToString(), obj.VatEntries.First(v => v.VatCode == "CODE 0").IncVat.ToString(), 40)));
            if(obj.VatEntries.Any(v => v.VatCode == "CODE 1"))
            commands.AddRange(emitter.PrintLine(Helper.CreateFourColumnLine("5%", obj.VatEntries.First(v => v.VatCode == "CODE 1").ExVat.ToString(), obj.VatEntries.First(v => v.VatCode == "CODE 1").Vat.ToString(), obj.VatEntries.First(v => v.VatCode == "CODE 1").IncVat.ToString(), 40)));
            if (obj.VatEntries.Any(v => v.VatCode == "CODE 2"))
            commands.AddRange(emitter.PrintLine(Helper.CreateFourColumnLine("0%", obj.VatEntries.First(v => v.VatCode == "CODE 2").ExVat.ToString(), obj.VatEntries.First(v => v.VatCode == "CODE 2").Vat.ToString(), obj.VatEntries.First(v => v.VatCode == "CODE 2").IncVat.ToString(), 40)));
            if (obj.VatEntries.Any(v => v.VatCode == "EXCEMPT"))
            commands.AddRange(emitter.PrintLine(Helper.CreateFourColumnLine("0% (EXCEMPT)", obj.VatEntries.First(v => v.VatCode == "EXCEMPT").ExVat.ToString(), obj.VatEntries.First(v => v.VatCode == "EXCEMPT").Vat.ToString(), obj.VatEntries.First(v => v.VatCode == "EXCEMPT").IncVat.ToString(), 40)));

            commands.AddRange(emitter.PrintLine(""));
            commands.AddRange(emitter.LeftAlign());

            foreach(var alert in obj.CashierAlerts)
            {
                commands.AddRange(emitter.PrintLine(alert));
            }


            commands.AddRange(emitter.PrintLine(""));


            // Set barcode height and width
            commands.AddRange(new byte[] { 0x1D, 0x68, 100 }); // Height = 100
            commands.AddRange(new byte[] { 0x1D, 0x77, 2 });   // Width = 2

            // Try Code 128
            string code128Data = obj.Barcode;

            // Pad to even digits for Code 128 Subset C (if numeric)
            if (IsNumeric(code128Data) && code128Data.Length % 2 != 0)
            {
                code128Data = code128Data.PadLeft(code128Data.Length + 1, '0'); // Now 14 digits
            }

            commands.AddRange(new byte[] { 0x1D, 0x6B, 0x49 }); // Code 128
            commands.Add((byte)code128Data.Length);
            commands.AddRange(Encoding.ASCII.GetBytes(code128Data));
            commands.Add(0x00); // NUL terminator

            // Fallback to EAN-13 if Code 128 fails
            if (obj.Barcode.Length == 13 && IsNumeric(obj.Barcode))
            {
                string ean13Data = obj.Barcode.Substring(0, 12); // Trim to 12 digits
                commands.AddRange(new byte[] { 0x1D, 0x6B, 0x43 }); // EAN-13
                commands.Add((byte)ean13Data.Length);
                commands.AddRange(Encoding.ASCII.GetBytes(ean13Data));
            }


            


            // Add 3-5 line feeds to advance the paper to the cutter
            for (int i = 0; i < 3; i++)
            {
                commands.Add(0x0A); // Line feed
            }


            commands.AddRange(emitter.CenterAlign());
            // Add the customer receipt from the card terminal if available
            if (obj.CustomerReceipt != null && obj.CustomerReceipt.Count > 0 && obj.PaymentType1 == PaymentType.Card)
            {
                commands.AddRange(emitter.PrintLine(""));
                commands.AddRange(emitter.PrintLine("========== CARD PAYMENT RECEIPT =========="));
                commands.AddRange(emitter.PrintLine(""));
                
                // Print each line of the customer receipt
                foreach (var line in obj.CustomerReceipt)
                {
                    commands.AddRange(emitter.PrintLine(line));
                }
                
                commands.AddRange(emitter.PrintLine(""));
                commands.AddRange(emitter.PrintLine("========================================="));
                commands.AddRange(emitter.PrintLine(""));
            }


            // Add thank-you message
            commands.AddRange(emitter.PrintLine("Thank you for shopping with us."));
            commands.AddRange(emitter.PrintLine("Please Come Again"));
            commands.AddRange(emitter.PrintLine(""));

            // Add cutter command (full cut)
            commands.Add(0x1D); // GS
            commands.Add(0x56); // V
            commands.Add(0x00); // Full cut

            // Send raw commands to the printer
            byte[] rawCommands = commands.ToArray();

            if (obj.PaymentType1 == PaymentType.Cash)
            {
                var ee = new EPSON();
                var cashDrawercommands = ByteSplicer.Combine(
                    ee.Initialize(),
                    ee.PrintLine(""), // feed paper to ensure printer is active
                    new byte[] { 27, 112, 48, 55, 121 }, // try with adjusted pulse durations
                    ee.PrintLine("")
                );

                RawPrinterHelper.SendBytesToPrinter(printerName, cashDrawercommands);

            }


            bool isPrinted = RawPrinterHelper.SendBytesToPrinter(printerName, rawCommands);

            if (isPrinted)
            {
                Console.WriteLine("Print job sent to the printer successfully.");
            }
            else
            {
                Console.WriteLine("Failed to send print job to the printer.");
            }



        }

        public static bool IsNumeric(string input)
        {
            foreach (char c in input)
            {
                if (!char.IsDigit(c))
                    return false;
            }
            return true;
        }

        private static bool IsValidCode128(string barcode)
        {
            // Validate that the barcode contains only valid Code 128 characters
            return System.Text.RegularExpressions.Regex.IsMatch(barcode, @"^[ -~]+$");
        }




    }



}
