using System;
using System.Threading;
using System.Threading.Tasks;
using Microsoft.Extensions.Hosting;
using Microsoft.Extensions.Logging;

namespace POS.WPF.Services
{
    public class SyncBackgroundService : BackgroundService
    {
        private readonly ISyncService _syncService;
        private readonly ILogger<SyncBackgroundService> _logger;
        private readonly TimeSpan _interval = TimeSpan.FromSeconds(5);

        public SyncBackgroundService(
            ISyncService syncService,
            ILogger<SyncBackgroundService> logger)
        {
            _syncService = syncService;
            _logger = logger;
        }

        protected override async Task ExecuteAsync(CancellationToken stoppingToken)
        {
            while (!stoppingToken.IsCancellationRequested)
            {
                try
                {
                    _logger.LogInformation("Starting sync process at: {time}", DateTimeOffset.Now);
                    await _syncService.ProcessPendingSyncItems();
                    _logger.LogInformation("Completed sync process at: {time}", DateTimeOffset.Now);
                }
                catch (Exception ex)
                {
                    _logger.LogError(ex, "Error occurred while processing sync items");
                }

                await Task.Delay(_interval, stoppingToken);
            }
        }
    }
} 