using System.Runtime.CompilerServices;

namespace POS.Core.Models
{
    public class Product : BaseEntity
    {
        public int Id { get; set; }
        public string Name { get; set; }

        public string PLU { get; set; }

        public string Barcode { get; set; }

        public string Description { get; set; }

        public decimal PurchasePrice { get; set; }

        public decimal SellingPrice { get; set; }

        public decimal PurchasePackSize { get; set; }


        public Division Division { get; set; }

        public int DivisionId { get; set; }


        public Vat Vat { get; set; }

        public int VatId { get; set; }


        public Brand? Brand { get; set; }
        public int? BrandId { get; set; }

        /// <summary>
        /// This is for Hot foods , Paypoint and News Papers
        /// </summary>
        public bool SpecialProduct { get; set; }

        public Promotion? Promotion { get; set; }

        public int? PromotionId { get; set; }

        public NonBrandPromotion? NonBrandPromotion { get; set; }
        public int? NonBrandPromotionId { get; set; }

        // Helper method to check if product is eligible for non-brand promotion
        public bool IsEligibleForNonBrandPromotion()
        {
            return !SpecialProduct && BrandId == null;
        }

        public ICollection<ProductStore> ProductStores { get; set; } = new List<ProductStore>();

       

      
    }


}
