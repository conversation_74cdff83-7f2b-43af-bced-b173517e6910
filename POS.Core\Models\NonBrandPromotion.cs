using System;
using System.Collections.Generic;

namespace POS.Core.Models
{
    public class NonBrandPromotion : BaseEntity
    {
        public int Id { get; set; }
        public string Name { get; set; }
        public PromotionType Type { get; set; }
        public ICollection<Product> Products { get; set; } = new List<Product>();
        public DateTime StartTime { get; set; }
        public DateTime EndTime { get; set; }
        public decimal? PromotionPrice { get; set; }
        public int Quantity { get; set; }

        public BackOffice CreatedBy { get; set; }
        public int CreatedById { get; set; }
        public Store Store { get; set; }
        public int StoreId { get; set; }
    }
}