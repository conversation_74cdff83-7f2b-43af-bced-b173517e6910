﻿namespace RefactoredPrinter
{
    public class PrintData
    {
        public string StoreId { get; set; }

        public string TransactionId { get; set; }

        public string CashierName { get; set; }

        public string Time { get; set; }

        public string Total { get; set; }

        public string Barcode { get; set; }

        public string PosMachineId { get; set; }


        public string VatNo { get; set; }

        public PaymentType PaymentType { get; set; }

        public string StoreName { get; set; }


        public List<VatEntry> VatEntries { get; set; }

        //for cash payements
        public string PaidAmount { get; set; }

        public string ChangeDue { get; set; }


        public List<Product> Products { get; set; }

        // Constructor to enforce PaymentType initialization
        public PrintData(PaymentType paymentType)
        {
            PaymentType = paymentType;
        }
    }


    public enum PaymentType
    {
        Cash = 1,
        Card = 2,
        Voucher = 3
    }


}