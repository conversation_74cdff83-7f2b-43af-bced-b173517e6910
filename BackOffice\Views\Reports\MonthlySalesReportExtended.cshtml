﻿@using System.Globalization
@{
    ViewData["Title"] = "Monthly Sales Report Extended";
    var months = ViewBag.Months as List<DateTime>;
    var report = ViewBag.Report as IEnumerable<dynamic>;
}

<h2>Monthly Sales Report (Extended)</h2>

<form method="post" asp-action="MonthlySalesReportExtended">
    <div class="form-group">
        <label for="startDate">Start Date:</label>
        <input type="date" id="startDate" name="startDate" class="form-control" required />
    </div>
    <div class="form-group">
        <label for="endDate">End Date:</label>
        <input type="date" id="endDate" name="endDate" class="form-control" required />
    </div>
    <button type="submit" class="btn btn-primary">Generate Report</button>
</form>

@if (months != null && report != null)
{
    <table class="table table-bordered">
        <thead>
            <tr>
                <th>Category</th>
                <th>Product Name</th>
                @foreach (var month in months)
                {
                    <th>@month.ToString("MMMM yyyy")</th>
                }
            </tr>
        </thead>
        <tbody>
            @foreach (var category in report)
            {
                @foreach (var product in category.Products)
                {
                    <tr>
                        <td>@category.Category</td>
                        <td>@product.ProductName</td>
                        @foreach (var sales in product.MonthlySales)
                        {
                            <td>@sales.ToString("C", new CultureInfo("en-GB"))</td>
                        }
                    </tr>
                }
            }
        </tbody>
    </table>
}
