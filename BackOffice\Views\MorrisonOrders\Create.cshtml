﻿@model POS.Core.Models.MorrisonOrder

<h2>Create Order</h2>

<form asp-action="Create">
    <div class="form-group">
        <label>Store</label>
        <select asp-for="StoreId" class="form-control" asp-items="@(new SelectList(ViewBag.Stores, "Id", "Name"))">
            <option value="">Select Store</option>
        </select>
    </div>

    <div class="form-group">
        <label>Expected Delivery Date</label>
        <input asp-for="ExpectedDeliveryDate" class="form-control" type="date" />
    </div>

    <button type="submit" class="btn btn-success">Create</button>
</form>
