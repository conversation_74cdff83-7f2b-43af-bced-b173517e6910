﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace POS.WPF
{
    public interface ICommand
    {
        bool CanExecute(object parameter); // Determines whether the command can execute.

        void Execute(object parameter);    // Executes the command.

        event EventHandler CanExecuteChanged; // Occurs when changes affect whether the command should execute.
    }
}
