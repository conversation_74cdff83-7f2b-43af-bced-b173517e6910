﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace POS.Printer
{
    internal class Helper
    {
        public static string CreateMixedAlignmentLine(string leftText, string rightText, int totalWidth)
        {
            int maxLeftWidth = totalWidth / 2;
            int maxRightWidth = totalWidth - maxLeftWidth;

            leftText = Truncate(leftText, maxLeftWidth);
            rightText = Truncate(rightText, maxRightWidth);

            int spaceCount = totalWidth - (leftText.Length + rightText.Length);
            if (spaceCount < 1) spaceCount = 1;

            string spaces = new string(' ', spaceCount);
            return $"{leftText}{spaces}{rightText}";
        }

        public static string CreateFourColumnLine(string col1, string col2, string col3, string col4, int totalWidth)
        {
            int colWidth = totalWidth / 4;

            col1 = Truncate(col1, colWidth).PadRight(colWidth);
            col2 = Truncate(col2, colWidth).PadRight(colWidth);
            col3 = Truncate(col3, colWidth).PadLeft(colWidth);
            col4 = Truncate(col4, colWidth).PadLeft(colWidth);

            return col1 + col2 + col3 + col4;
        }

        public static string CreateFiveColumnLine(string col1, string col2, string col3, string col4, string col5, int totalWidth)
        {
            int colWidth = totalWidth / 5;

            col1 = Truncate(col1, colWidth).PadRight(colWidth);
            col2 = Truncate(col2, colWidth).PadRight(colWidth);
            col3 = Truncate(col3, colWidth).PadLeft(colWidth);
            col4 = Truncate(col4, colWidth).PadLeft(colWidth);
            col5 = Truncate(col5, colWidth).PadLeft(colWidth);

            return col1 + col2 + col3 + col4 + col5;
        }

        public static string CreateThreeColumnLine(string leftText, string middleText, string rightText, int totalWidth)
        {
            // Calculate column widths based on percentages
            int leftColWidth = (int)(totalWidth * 0.6); // 60% for the first column
            int middleColWidth = (int)(totalWidth * 0.2); // 20% for the second column
            int rightColWidth = totalWidth - (leftColWidth + middleColWidth); // Remaining 20% for the third column

            // Truncate and pad the text for each column
            leftText = Truncate(leftText, leftColWidth).PadRight(leftColWidth);
            middleText = Truncate(middleText, middleColWidth).PadRight(middleColWidth);
            rightText = Truncate(rightText, rightColWidth).PadLeft(rightColWidth);

            // Combine the columns into a single line
            return leftText + middleText + rightText;
        }

        private static string Truncate(string input, int maxLength)
        {
            if (string.IsNullOrEmpty(input) || input.Length <= maxLength)
            {
                return input;
            }
            return input.Substring(0, maxLength - 3) + "...";
        }
    }
}
