﻿using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using POS.Core.Models;

namespace BackOffice.Controllers
{
    [Authorize(Roles = "Admin")]
    public class PosMachineController : Controller
    {
        private readonly Data _context;

        public PosMachineController(Data context)
        {
            _context = context;
        }

        // GET: PosMachine
        public async Task<IActionResult> Index()
        {
            var posMachines = _context.PosMachines
                .Include(p => p.Store);
            return View(await posMachines.ToListAsync());
        }

        // GET: PosMachine/Create
        public IActionResult Create()
        {
            ViewBag.Cashiers = _context.Cashiers.ToList();
            ViewBag.Stores = _context.Stores.ToList();
            return View();
        }

        // POST: PosMachine/Create
        [HttpPost]
        [ValidateAntiForgeryToken]
        public async Task<IActionResult> Create(PosMachine posMachine)
        {
            posMachine.StoreId = posMachine.Store.StoreId;
            posMachine.Store = null;

            _context.Add(posMachine);
            await _context.SaveChangesAsync();
            return RedirectToAction(nameof(Index));
        }

        // GET: PosMachine/Edit/5
        public async Task<IActionResult> Edit(int id)
        {
            var posMachine = await _context.PosMachines.FindAsync(id);
            if (posMachine == null)
            {
                return NotFound();
            }
            ViewBag.Cashiers = _context.Cashiers.ToList();
            ViewBag.Stores = _context.Stores.ToList();
            return View(posMachine);
        }

        // POST: PosMachine/Edit/5
        [HttpPost]
        [ValidateAntiForgeryToken]
        public async Task<IActionResult> Edit(int id, PosMachine posMachine)
        {
            if (id != posMachine.Id)
            {
                return NotFound();
            }

            posMachine.StoreId = posMachine.Store.StoreId;
            posMachine.Store = null;

            _context.Update(posMachine);
            await _context.SaveChangesAsync();
            return RedirectToAction(nameof(Index));

        }

        // GET: PosMachine/Delete/5
        public async Task<IActionResult> Delete(int id)
        {
            var posMachine = await _context.PosMachines
                .Include(p => p.Store)
                .FirstOrDefaultAsync(m => m.Id == id);
            if (posMachine == null)
            {
                return NotFound();
            }

            return View(posMachine);
        }

        // POST: PosMachine/Delete/5
        [HttpPost, ActionName("Delete")]
        [ValidateAntiForgeryToken]
        public async Task<IActionResult> DeleteConfirmed(int id)
        {
            var posMachine = await _context.PosMachines.FindAsync(id);
            if (posMachine != null)
            {
                _context.PosMachines.Remove(posMachine);
                await _context.SaveChangesAsync();
            }
            return RedirectToAction(nameof(Index));
        }
    }
}
