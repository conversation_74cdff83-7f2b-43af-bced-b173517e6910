﻿@model POS.Core.Models.Company

@{
    ViewData["Title"] = "Create";
}

<h1>Create</h1>

<form asp-action="Edit">
    <div class="form-group">
        <label asp-for="Name" class="control-label"></label>
        <input asp-for="Name" class="form-control" />
        <span asp-validation-for="Name" class="text-danger"></span>
    </div>
    <div class="form-group">
        <label asp-for="Address" class="control-label"></label>
        <input asp-for="Address" class="form-control" />
        <span asp-validation-for="Address" class="text-danger"></span>
    </div>
    <div class="form-group">
        <input type="submit" value="Edit" class="btn btn-primary" />
    </div>
</form>
<p>
    <a asp-action="Index">Back to List</a>
</p>
