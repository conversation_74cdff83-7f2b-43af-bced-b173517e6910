using System;
using System.Threading.Tasks;
using POS.Core.Models;

namespace POS.WPF.Services
{
    public interface ISyncService
    {
        Task QueueForSync(string entityType, object entity, string operationType);
        Task ProcessPendingSyncItems();
        Task<IdMapping> GetRemoteId(string entityType, int localId);
        Task<IdMapping> CreateIdMapping(string entityType, int localId, int remoteId);
    }
} 