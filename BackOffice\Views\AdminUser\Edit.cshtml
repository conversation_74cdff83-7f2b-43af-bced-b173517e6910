﻿@model BackOffice.ViewModels.UserEditViewModel

<h2>Edit User</h2>

<form asp-action="Edit" method="post">
    <input type="hidden" asp-for="Id" />

    <div class="form-group">
        <label asp-for="Name"></label>
        <input asp-for="Name" class="form-control" required />
    </div>

    <div class="form-group">
        <label asp-for="UserId"></label>
        <input asp-for="UserId" class="form-control" required />
    </div>

    <input type="hidden" name="Role" value="@Model.Role" />

    @if (Model.Role == "Accountant" || Model.Role == "BackOffice")
    {
        <h4>IP Addresses</h4>
        <div id="ipInputs">
            @for (int i = 0; i < Model.IpAddresses.Count; i++)
            {
                <div class="ip-input-group mb-2">
                    <input type="text" name="IpAddresses[@i]" class="form-control" value="@Model.IpAddresses[i]">
                    <button type="button" class="btn btn-danger remove-ip">Remove</button>
                </div>
            }
        </div>
        <button type="button" id="addIp" class="btn btn-secondary">Add IP</button>
    }

    <button type="submit" class="btn btn-primary mt-3">Save</button>
</form>

<script>
    document.getElementById("addIp")?.addEventListener("click", function () {
        const ipInputsDiv = document.getElementById("ipInputs");
        const inputGroups = ipInputsDiv.getElementsByClassName("ip-input-group");
        const newIndex = inputGroups.length;

        const inputGroup = document.createElement("div");
        inputGroup.className = "ip-input-group mb-2";
        inputGroup.innerHTML = `
            <input type="text" name="IpAddresses[${newIndex}]" class="form-control">
            <button type="button" class="btn btn-danger remove-ip">Remove</button>
        `;

        ipInputsDiv.appendChild(inputGroup);
    });

    document.getElementById("ipInputs")?.addEventListener("click", function(e) {
        if (e.target.classList.contains("remove-ip")) {
            e.target.closest(".ip-input-group").remove();
            // Reindex remaining inputs
            const inputs = document.querySelectorAll("#ipInputs input");
            inputs.forEach((input, index) => {
                input.name = `IpAddresses[${index}]`;
            });
        }
    });
</script>

<style>
    .ip-input-group {
        display: flex;
        gap: 10px;
    }
</style>
