﻿using Microsoft.EntityFrameworkCore.Metadata.Internal;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Windows;
using System.Windows.Controls;
using System.Windows.Data;
using System.Windows.Documents;
using System.Windows.Input;
using System.Windows.Media;
using System.Windows.Media.Imaging;
using System.Windows.Shapes;
using TimeManagement;
using WindowsInput;
using WindowsInput.Native;

namespace POS.WPF.Views
{
    /// <summary>
    /// Interaction logic for CashBackWindow.xaml
    /// </summary>
    public partial class CashBackWindow : Window
    {
        private InputSimulator _inputSimulator;

        public decimal? CashBackAmount { get; private set; }  // Nullable decimal to store the amount
        public CashBackWindow()
        {
            InitializeComponent();
            Helpers.WindowStyleHelper.SetWindowStyle(this);
            _inputSimulator = new InputSimulator();
        }

        private void btnSet_Click(object sender, RoutedEventArgs e)
        {
            if (decimal.TryParse(txtCashBack.Text, out decimal amount))
            {
                CashBackAmount = amount;
                DialogResult = true;  // Close the window and return true
            }
            else
            {
                MessageBox.Show("Please enter a valid amount.", "Invalid Input", MessageBoxButton.OK, MessageBoxImage.Warning);
            }
        }

        private void btnIgnore_Click(object sender, RoutedEventArgs e)
        {
            CashBackAmount = null;   // User declined
            DialogResult = false;    // Close the window and return false
        }

        private void NumericKeypad_KeyPressed(object sender, string key)
        {
            txtCashBack.Focus();

            switch (key)
            {
                case "Backspace":
                    _inputSimulator.Keyboard.KeyPress(VirtualKeyCode.BACK);
                    break;
                case "%":
                    _inputSimulator.Keyboard.KeyPress(VirtualKeyCode.SHIFT, VirtualKeyCode.VK_5);
                    break;
                case "00":
                    _inputSimulator.Keyboard.KeyPress(VirtualKeyCode.VK_0);
                    _inputSimulator.Keyboard.KeyPress(VirtualKeyCode.VK_0);
                    break;
                case ".":
                    _inputSimulator.Keyboard.KeyPress(VirtualKeyCode.DECIMAL);
                    break;
                default:
                    if (int.TryParse(key, out var number))
                    {
                        var keyCode = (VirtualKeyCode)Enum.Parse(
                            typeof(VirtualKeyCode),
                            $"VK_{number}"
                        );
                        _inputSimulator.Keyboard.KeyPress(keyCode);
                    }
                    break;
            }
        }
    }
}
