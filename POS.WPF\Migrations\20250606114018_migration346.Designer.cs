﻿// <auto-generated />
using System;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Infrastructure;
using Microsoft.EntityFrameworkCore.Migrations;
using Microsoft.EntityFrameworkCore.Storage.ValueConversion;
using POS.WPF.LocalData;

#nullable disable

namespace POS.WPF.Migrations
{
    [DbContext(typeof(LocalContext))]
    [Migration("20250606114018_migration346")]
    partial class migration346
    {
        /// <inheritdoc />
        protected override void BuildTargetModel(ModelBuilder modelBuilder)
        {
#pragma warning disable 612, 618
            modelBuilder.HasAnnotation("ProductVersion", "8.0.0");

            modelBuilder.Entity("POS.Core.Models.Brand", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("INTEGER");

                    b.Property<string>("BrandLogo")
                        .HasColumnType("TEXT");

                    b.Property<bool>("IsDeleted")
                        .HasColumnType("INTEGER");

                    b.Property<DateTime?>("LastModified")
                        .HasColumnType("TEXT");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasColumnType("TEXT");

                    b.HasKey("Id");

                    b.ToTable("Brands");
                });

            modelBuilder.Entity("POS.Core.Models.CashierStore", b =>
                {
                    b.Property<int>("StoreId")
                        .HasColumnType("INTEGER");

                    b.Property<int>("CashierId")
                        .HasColumnType("INTEGER");

                    b.Property<bool>("IsDeleted")
                        .HasColumnType("INTEGER");

                    b.Property<DateTime?>("LastModified")
                        .HasColumnType("TEXT");

                    b.HasKey("StoreId", "CashierId");

                    b.HasIndex("CashierId");

                    b.ToTable("CashierStores");
                });

            modelBuilder.Entity("POS.Core.Models.Company", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("INTEGER");

                    b.Property<string>("Address")
                        .IsRequired()
                        .HasColumnType("TEXT");

                    b.Property<bool>("IsDeleted")
                        .HasColumnType("INTEGER");

                    b.Property<DateTime?>("LastModified")
                        .HasColumnType("TEXT");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasColumnType("TEXT");

                    b.HasKey("Id");

                    b.ToTable("Companies");
                });

            modelBuilder.Entity("POS.Core.Models.CustomerCopy", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("INTEGER");

                    b.Property<string>("AID")
                        .IsRequired()
                        .HasColumnType("TEXT");

                    b.Property<string>("ApplicationCryptogram")
                        .IsRequired()
                        .HasColumnType("TEXT");

                    b.Property<string>("AuthCode")
                        .IsRequired()
                        .HasColumnType("TEXT");

                    b.Property<string>("CardName")
                        .IsRequired()
                        .HasColumnType("TEXT");

                    b.Property<string>("CardType")
                        .IsRequired()
                        .HasColumnType("TEXT");

                    b.Property<string>("Currency")
                        .IsRequired()
                        .HasColumnType("TEXT");

                    b.Property<string>("EftReference")
                        .IsRequired()
                        .HasColumnType("TEXT");

                    b.Property<string>("EntryMode")
                        .IsRequired()
                        .HasColumnType("TEXT");

                    b.Property<string>("ExpiryDateMasked")
                        .IsRequired()
                        .HasColumnType("TEXT");

                    b.Property<string>("PANMasked")
                        .IsRequired()
                        .HasColumnType("TEXT");

                    b.Property<string>("Response")
                        .IsRequired()
                        .HasColumnType("TEXT");

                    b.Property<decimal>("SaleAmount")
                        .HasColumnType("TEXT");

                    b.Property<int>("SaleId")
                        .HasColumnType("INTEGER");

                    b.Property<string>("SequenceNumber")
                        .IsRequired()
                        .HasColumnType("TEXT");

                    b.Property<string>("TerminalId")
                        .IsRequired()
                        .HasColumnType("TEXT");

                    b.Property<DateTime>("TransactionDateTime")
                        .HasColumnType("TEXT");

                    b.Property<string>("VerificationMethod")
                        .IsRequired()
                        .HasColumnType("TEXT");

                    b.HasKey("Id");

                    b.ToTable("CustomerCopys");
                });

            modelBuilder.Entity("POS.Core.Models.DeletedSaleItem", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("INTEGER");

                    b.Property<DateTime>("DeletedTime")
                        .HasColumnType("TEXT");

                    b.Property<bool>("IsDeleted")
                        .HasColumnType("INTEGER");

                    b.Property<DateTime?>("LastModified")
                        .HasColumnType("TEXT");

                    b.Property<string>("PLU")
                        .IsRequired()
                        .HasColumnType("TEXT");

                    b.Property<int>("PosId")
                        .HasColumnType("INTEGER");

                    b.Property<int>("Qty")
                        .HasColumnType("INTEGER");

                    b.Property<int>("StoreId")
                        .HasColumnType("INTEGER");

                    b.Property<decimal>("Total")
                        .HasColumnType("TEXT");

                    b.HasKey("Id");

                    b.ToTable("DeletedSaleItems");
                });

            modelBuilder.Entity("POS.Core.Models.Division", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("INTEGER");

                    b.Property<bool>("IsDeleted")
                        .HasColumnType("INTEGER");

                    b.Property<DateTime?>("LastModified")
                        .HasColumnType("TEXT");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasColumnType("TEXT");

                    b.Property<int>("SubCatagoryId")
                        .HasColumnType("INTEGER");

                    b.HasKey("Id");

                    b.HasIndex("SubCatagoryId");

                    b.ToTable("Divisions");
                });

            modelBuilder.Entity("POS.Core.Models.EmailSubscription", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("INTEGER");

                    b.Property<string>("Email")
                        .IsRequired()
                        .HasColumnType("TEXT");

                    b.Property<bool>("IsDeleted")
                        .HasColumnType("INTEGER");

                    b.Property<DateTime?>("LastModified")
                        .HasColumnType("TEXT");

                    b.HasKey("Id");

                    b.ToTable("EmailSubscriptions");
                });

            modelBuilder.Entity("POS.Core.Models.IdMapping", b =>
                {
                    b.Property<string>("EntityType")
                        .HasColumnType("TEXT");

                    b.Property<int>("LocalId")
                        .HasColumnType("INTEGER");

                    b.Property<bool>("IsDeleted")
                        .HasColumnType("INTEGER");

                    b.Property<DateTime?>("LastModified")
                        .HasColumnType("TEXT");

                    b.Property<DateTime>("MappedAt")
                        .HasColumnType("TEXT");

                    b.Property<int>("RemoteId")
                        .HasColumnType("INTEGER");

                    b.HasKey("EntityType", "LocalId");

                    b.HasIndex("EntityType", "LocalId")
                        .IsUnique();

                    b.ToTable("IdMappings");
                });

            modelBuilder.Entity("POS.Core.Models.IpAddress", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("INTEGER");

                    b.Property<int?>("AccountantId")
                        .HasColumnType("INTEGER");

                    b.Property<string>("Address")
                        .IsRequired()
                        .HasColumnType("TEXT");

                    b.Property<int?>("BackOfficeId")
                        .HasColumnType("INTEGER");

                    b.Property<bool>("IsDeleted")
                        .HasColumnType("INTEGER");

                    b.Property<DateTime?>("LastModified")
                        .HasColumnType("TEXT");

                    b.Property<int>("UserId")
                        .HasColumnType("INTEGER");

                    b.HasKey("Id");

                    b.HasIndex("AccountantId");

                    b.HasIndex("BackOfficeId");

                    b.HasIndex("UserId");

                    b.ToTable("IpAddresses");
                });

            modelBuilder.Entity("POS.Core.Models.MorrisonOrder", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("INTEGER");

                    b.Property<DateTime>("CreatedDate")
                        .HasColumnType("TEXT");

                    b.Property<DateTime>("ExpectedDeliveryDate")
                        .HasColumnType("TEXT");

                    b.Property<string>("GeneratedId")
                        .IsRequired()
                        .HasColumnType("TEXT");

                    b.Property<bool>("IsDeleted")
                        .HasColumnType("INTEGER");

                    b.Property<DateTime?>("LastModified")
                        .HasColumnType("TEXT");

                    b.Property<int>("MorrisonOrderStatus")
                        .HasColumnType("INTEGER");

                    b.Property<int>("StoreId")
                        .HasColumnType("INTEGER");

                    b.HasKey("Id");

                    b.HasIndex("StoreId");

                    b.ToTable("MorrisonOrders");
                });

            modelBuilder.Entity("POS.Core.Models.MorrisonsOrderItem", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("INTEGER");

                    b.Property<string>("Barcode")
                        .IsRequired()
                        .HasColumnType("TEXT");

                    b.Property<bool>("IsDeleted")
                        .HasColumnType("INTEGER");

                    b.Property<DateTime?>("LastModified")
                        .HasColumnType("TEXT");

                    b.Property<int>("MorrisonOrderId")
                        .HasColumnType("INTEGER");

                    b.Property<int>("Quantity")
                        .HasColumnType("INTEGER");

                    b.HasKey("Id");

                    b.HasIndex("MorrisonOrderId");

                    b.ToTable("MorrisonsOrderItems");
                });

            modelBuilder.Entity("POS.Core.Models.NextOrderProduct", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("INTEGER");

                    b.Property<string>("Barcode")
                        .IsRequired()
                        .HasColumnType("TEXT");

                    b.Property<bool>("IsDeleted")
                        .HasColumnType("INTEGER");

                    b.Property<DateTime?>("LastModified")
                        .HasColumnType("TEXT");

                    b.Property<int>("Quantity")
                        .HasColumnType("INTEGER");

                    b.Property<int>("StoreId")
                        .HasColumnType("INTEGER");

                    b.HasKey("Id");

                    b.HasIndex("StoreId");

                    b.ToTable("NextOrderProducts");
                });

            modelBuilder.Entity("POS.Core.Models.NightEmail", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("INTEGER");

                    b.Property<bool>("Enabled")
                        .HasColumnType("INTEGER");

                    b.Property<bool>("IsDeleted")
                        .HasColumnType("INTEGER");

                    b.Property<DateTime?>("LastModified")
                        .HasColumnType("TEXT");

                    b.HasKey("Id");

                    b.ToTable("NightEmails");
                });

            modelBuilder.Entity("POS.Core.Models.NonBrandPromotion", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("INTEGER");

                    b.Property<int>("CreatedById")
                        .HasColumnType("INTEGER");

                    b.Property<DateTime>("EndTime")
                        .HasColumnType("TEXT");

                    b.Property<bool>("IsDeleted")
                        .HasColumnType("INTEGER");

                    b.Property<DateTime?>("LastModified")
                        .HasColumnType("TEXT");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasColumnType("TEXT");

                    b.Property<decimal?>("PromotionPrice")
                        .HasColumnType("TEXT");

                    b.Property<int>("Quantity")
                        .HasColumnType("INTEGER");

                    b.Property<DateTime>("StartTime")
                        .HasColumnType("TEXT");

                    b.Property<int>("StoreId")
                        .HasColumnType("INTEGER");

                    b.Property<int>("Type")
                        .HasColumnType("INTEGER");

                    b.HasKey("Id");

                    b.HasIndex("CreatedById");

                    b.HasIndex("StoreId");

                    b.ToTable("NonBrandPromotions");
                });

            modelBuilder.Entity("POS.Core.Models.PaidOut", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("INTEGER");

                    b.Property<decimal>("Amount")
                        .HasColumnType("TEXT");

                    b.Property<bool>("IsDeleted")
                        .HasColumnType("INTEGER");

                    b.Property<DateTime?>("LastModified")
                        .HasColumnType("TEXT");

                    b.Property<int?>("PaidOutOptionId")
                        .HasColumnType("INTEGER");

                    b.Property<int>("ShiftId")
                        .HasColumnType("INTEGER");

                    b.Property<int?>("StoreId")
                        .HasColumnType("INTEGER");

                    b.Property<DateTime>("Time")
                        .HasColumnType("TEXT");

                    b.HasKey("Id");

                    b.HasIndex("PaidOutOptionId");

                    b.HasIndex("ShiftId");

                    b.HasIndex("StoreId");

                    b.ToTable("PaidOuts");
                });

            modelBuilder.Entity("POS.Core.Models.PaidOutOption", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("INTEGER");

                    b.Property<bool>("IsDeleted")
                        .HasColumnType("INTEGER");

                    b.Property<DateTime?>("LastModified")
                        .HasColumnType("TEXT");

                    b.Property<string>("Option")
                        .IsRequired()
                        .HasColumnType("TEXT");

                    b.HasKey("Id");

                    b.ToTable("PaidOutOptions");
                });

            modelBuilder.Entity("POS.Core.Models.PaymentMethod", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("INTEGER");

                    b.Property<bool>("IsDeleted")
                        .HasColumnType("INTEGER");

                    b.Property<DateTime?>("LastModified")
                        .HasColumnType("TEXT");

                    b.Property<string>("Method")
                        .IsRequired()
                        .HasColumnType("TEXT");

                    b.HasKey("Id");

                    b.ToTable("PaymentMethods");
                });

            modelBuilder.Entity("POS.Core.Models.PendingSyncItem", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("INTEGER");

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("TEXT");

                    b.Property<int>("EntityId")
                        .HasColumnType("INTEGER");

                    b.Property<string>("EntityType")
                        .IsRequired()
                        .HasColumnType("TEXT");

                    b.Property<bool>("IsSynced")
                        .HasColumnType("INTEGER");

                    b.Property<string>("OperationType")
                        .IsRequired()
                        .HasColumnType("TEXT");

                    b.Property<int>("RetryCount")
                        .HasColumnType("INTEGER");

                    b.Property<string>("SyncError")
                        .IsRequired()
                        .HasColumnType("TEXT");

                    b.Property<DateTime?>("SyncedAt")
                        .HasColumnType("TEXT");

                    b.HasKey("Id");

                    b.ToTable("PendingSyncItems");
                });

            modelBuilder.Entity("POS.Core.Models.PosMachine", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("INTEGER");

                    b.Property<bool>("IsDeleted")
                        .HasColumnType("INTEGER");

                    b.Property<DateTime?>("LastModified")
                        .HasColumnType("TEXT");

                    b.Property<int>("StoreId")
                        .HasColumnType("INTEGER");

                    b.HasKey("Id");

                    b.HasIndex("StoreId");

                    b.ToTable("PosMachines");
                });

            modelBuilder.Entity("POS.Core.Models.Product", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("INTEGER");

                    b.Property<string>("Barcode")
                        .IsRequired()
                        .HasColumnType("TEXT");

                    b.Property<int?>("BrandId")
                        .HasColumnType("INTEGER");

                    b.Property<string>("Description")
                        .IsRequired()
                        .HasColumnType("TEXT");

                    b.Property<int>("DivisionId")
                        .HasColumnType("INTEGER");

                    b.Property<bool>("IsDeleted")
                        .HasColumnType("INTEGER");

                    b.Property<DateTime?>("LastModified")
                        .HasColumnType("TEXT");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasColumnType("TEXT");

                    b.Property<int?>("NonBrandPromotionId")
                        .HasColumnType("INTEGER");

                    b.Property<string>("PLU")
                        .IsRequired()
                        .HasColumnType("TEXT");

                    b.Property<int?>("PromotionId")
                        .HasColumnType("INTEGER");

                    b.Property<decimal>("PurchasePackSize")
                        .HasColumnType("TEXT");

                    b.Property<decimal>("PurchasePrice")
                        .HasColumnType("TEXT");

                    b.Property<decimal>("SellingPrice")
                        .HasColumnType("TEXT");

                    b.Property<bool>("SpecialProduct")
                        .HasColumnType("INTEGER");

                    b.Property<int>("VatId")
                        .HasColumnType("INTEGER");

                    b.HasKey("Id");

                    b.HasIndex("BrandId");

                    b.HasIndex("DivisionId");

                    b.HasIndex("NonBrandPromotionId");

                    b.HasIndex("PromotionId");

                    b.HasIndex("VatId");

                    b.ToTable("Products");
                });

            modelBuilder.Entity("POS.Core.Models.ProductStore", b =>
                {
                    b.Property<int>("ProductStoreId")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("INTEGER");

                    b.Property<int>("InventoryCount")
                        .HasColumnType("INTEGER");

                    b.Property<bool>("IsDeleted")
                        .HasColumnType("INTEGER");

                    b.Property<DateTime?>("LastModified")
                        .HasColumnType("TEXT");

                    b.Property<int>("ProductId")
                        .HasColumnType("INTEGER");

                    b.Property<int>("StoreId")
                        .HasColumnType("INTEGER");

                    b.Property<decimal?>("StoreSpecificPrice")
                        .HasColumnType("TEXT");

                    b.HasKey("ProductStoreId");

                    b.HasIndex("ProductId");

                    b.HasIndex("StoreId", "ProductId")
                        .IsUnique();

                    b.ToTable("ProductStores");
                });

            modelBuilder.Entity("POS.Core.Models.ProductStoreSale", b =>
                {
                    b.Property<int>("ProductStoreSaleId")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("INTEGER");

                    b.Property<bool>("IsDeleted")
                        .HasColumnType("INTEGER");

                    b.Property<bool>("IsNonBrandPromotion")
                        .HasColumnType("INTEGER");

                    b.Property<bool>("IsPromotion")
                        .HasColumnType("INTEGER");

                    b.Property<DateTime?>("LastModified")
                        .HasColumnType("TEXT");

                    b.Property<int?>("NonBrandPromotionId")
                        .HasColumnType("INTEGER");

                    b.Property<decimal>("OverriddenDiscountAmount")
                        .HasColumnType("TEXT");

                    b.Property<bool>("PriceOverriden")
                        .HasColumnType("INTEGER");

                    b.Property<int>("ProductStoreId")
                        .HasColumnType("INTEGER");

                    b.Property<int?>("PromotionId")
                        .HasColumnType("INTEGER");

                    b.Property<int>("Quantity")
                        .HasColumnType("INTEGER");

                    b.Property<int>("SaleId")
                        .HasColumnType("INTEGER");

                    b.Property<bool>("StaffDiscount")
                        .HasColumnType("INTEGER");

                    b.Property<decimal>("StaffDiscountedAmount")
                        .HasColumnType("TEXT");

                    b.Property<decimal>("Total")
                        .HasColumnType("TEXT");

                    b.HasKey("ProductStoreSaleId");

                    b.HasIndex("NonBrandPromotionId");

                    b.HasIndex("ProductStoreId");

                    b.HasIndex("PromotionId");

                    b.HasIndex("SaleId");

                    b.ToTable("ProductStoreSales");
                });

            modelBuilder.Entity("POS.Core.Models.Promotion", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("INTEGER");

                    b.Property<DateTime>("EndTime")
                        .HasColumnType("TEXT");

                    b.Property<string>("Identifire")
                        .IsRequired()
                        .HasColumnType("TEXT");

                    b.Property<bool>("IsDeleted")
                        .HasColumnType("INTEGER");

                    b.Property<DateTime?>("LastModified")
                        .HasColumnType("TEXT");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasColumnType("TEXT");

                    b.Property<decimal?>("PromotionPrice")
                        .HasColumnType("TEXT");

                    b.Property<int>("Quantity")
                        .HasColumnType("INTEGER");

                    b.Property<DateTime>("StartTime")
                        .HasColumnType("TEXT");

                    b.Property<int>("Type")
                        .HasColumnType("INTEGER");

                    b.HasKey("Id");

                    b.ToTable("Promotions");
                });

            modelBuilder.Entity("POS.Core.Models.QuickAccessButton", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("INTEGER");

                    b.Property<string>("ButtonText")
                        .IsRequired()
                        .HasColumnType("TEXT");

                    b.Property<int>("ProductId")
                        .HasColumnType("INTEGER");

                    b.Property<int>("StoreId")
                        .HasColumnType("INTEGER");

                    b.HasKey("Id");

                    b.HasIndex("ProductId");

                    b.HasIndex("StoreId");

                    b.ToTable("QuickAccessButtons");
                });

            modelBuilder.Entity("POS.Core.Models.Refund", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("INTEGER");

                    b.Property<decimal>("Amount")
                        .HasColumnType("TEXT");

                    b.Property<DateTime>("Date")
                        .HasColumnType("TEXT");

                    b.Property<bool>("IsDeleted")
                        .HasColumnType("INTEGER");

                    b.Property<DateTime?>("LastModified")
                        .HasColumnType("TEXT");

                    b.Property<int>("ProductStoreSaleId")
                        .HasColumnType("INTEGER");

                    b.Property<int>("Quantity")
                        .HasColumnType("INTEGER");

                    b.HasKey("Id");

                    b.HasIndex("ProductStoreSaleId");

                    b.ToTable("Refunds");
                });

            modelBuilder.Entity("POS.Core.Models.SafeDrop", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("INTEGER");

                    b.Property<decimal>("Amount")
                        .HasColumnType("TEXT");

                    b.Property<bool>("IsDeleted")
                        .HasColumnType("INTEGER");

                    b.Property<DateTime?>("LastModified")
                        .HasColumnType("TEXT");

                    b.Property<int>("StoreId")
                        .HasColumnType("INTEGER");

                    b.Property<DateTime>("Time")
                        .HasColumnType("TEXT");

                    b.Property<int>("shiftId")
                        .HasColumnType("INTEGER");

                    b.HasKey("Id");

                    b.HasIndex("StoreId");

                    b.HasIndex("shiftId");

                    b.ToTable("SafeDrops");
                });

            modelBuilder.Entity("POS.Core.Models.Sale", b =>
                {
                    b.Property<int>("SaleId")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("INTEGER");

                    b.Property<string>("Barcode")
                        .HasColumnType("TEXT");

                    b.Property<decimal?>("CashTendered")
                        .HasColumnType("TEXT");

                    b.Property<decimal>("Cashback")
                        .HasColumnType("TEXT");

                    b.Property<DateTime>("Date")
                        .HasColumnType("TEXT");

                    b.Property<bool>("IsDeleted")
                        .HasColumnType("INTEGER");

                    b.Property<bool>("IsOnHold")
                        .HasColumnType("INTEGER");

                    b.Property<DateTime?>("LastModified")
                        .HasColumnType("TEXT");

                    b.Property<int>("ShiftId")
                        .HasColumnType("INTEGER");

                    b.Property<decimal>("TotalValue")
                        .HasColumnType("TEXT");

                    b.HasKey("SaleId");

                    b.HasIndex("ShiftId");

                    b.ToTable("Sales");
                });

            modelBuilder.Entity("POS.Core.Models.SalePaymentMethod2", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("INTEGER");

                    b.Property<decimal>("Amount")
                        .HasColumnType("TEXT");

                    b.Property<bool>("IsDeleted")
                        .HasColumnType("INTEGER");

                    b.Property<DateTime?>("LastModified")
                        .HasColumnType("TEXT");

                    b.Property<int>("PaymentMethodId")
                        .HasColumnType("INTEGER");

                    b.Property<int>("SaleId")
                        .HasColumnType("INTEGER");

                    b.HasKey("Id");

                    b.HasIndex("PaymentMethodId");

                    b.HasIndex("SaleId", "PaymentMethodId")
                        .IsUnique();

                    b.ToTable("SalePaymentMethods");
                });

            modelBuilder.Entity("POS.Core.Models.Shift", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("INTEGER");

                    b.Property<int?>("CashierId")
                        .HasColumnType("INTEGER");

                    b.Property<DateTime>("EndTime")
                        .HasColumnType("TEXT");

                    b.Property<bool>("InProgress")
                        .HasColumnType("INTEGER");

                    b.Property<bool>("IsDeleted")
                        .HasColumnType("INTEGER");

                    b.Property<DateTime?>("LastModified")
                        .HasColumnType("TEXT");

                    b.Property<int?>("PosMachineId")
                        .HasColumnType("INTEGER");

                    b.Property<DateTime>("StartTime")
                        .HasColumnType("TEXT");

                    b.Property<int?>("StoreDayId")
                        .HasColumnType("INTEGER");

                    b.HasKey("Id");

                    b.HasIndex("CashierId");

                    b.HasIndex("PosMachineId");

                    b.HasIndex("StoreDayId");

                    b.ToTable("Shifts");
                });

            modelBuilder.Entity("POS.Core.Models.SpecialProductSale", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("INTEGER");

                    b.Property<decimal>("Amount")
                        .HasColumnType("TEXT");

                    b.Property<bool>("IsDeleted")
                        .HasColumnType("INTEGER");

                    b.Property<DateTime?>("LastModified")
                        .HasColumnType("TEXT");

                    b.Property<int>("ProductId")
                        .HasColumnType("INTEGER");

                    b.Property<int>("SaleId")
                        .HasColumnType("INTEGER");

                    b.HasKey("Id");

                    b.HasIndex("ProductId");

                    b.HasIndex("SaleId");

                    b.ToTable("SpecialProductSales");
                });

            modelBuilder.Entity("POS.Core.Models.Store", b =>
                {
                    b.Property<int>("StoreId")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("INTEGER");

                    b.Property<bool>("AllowNegativeInventory")
                        .HasColumnType("INTEGER");

                    b.Property<int?>("BrandId")
                        .HasColumnType("INTEGER");

                    b.Property<int?>("CompanyId")
                        .HasColumnType("INTEGER");

                    b.Property<bool>("IsDeleted")
                        .HasColumnType("INTEGER");

                    b.Property<DateTime?>("LastModified")
                        .HasColumnType("TEXT");

                    b.Property<int?>("MainPosMachineId")
                        .HasColumnType("INTEGER");

                    b.Property<string>("StoreIdForAPI")
                        .HasColumnType("TEXT");

                    b.Property<string>("StoreName")
                        .IsRequired()
                        .HasColumnType("TEXT");

                    b.Property<string>("VatNo")
                        .HasColumnType("TEXT");

                    b.HasKey("StoreId");

                    b.HasIndex("BrandId");

                    b.HasIndex("CompanyId");

                    b.ToTable("Stores");
                });

            modelBuilder.Entity("POS.Core.Models.StoreDay", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("INTEGER");

                    b.Property<DateTime>("EndTime")
                        .HasColumnType("TEXT");

                    b.Property<bool>("InProgress")
                        .HasColumnType("INTEGER");

                    b.Property<bool>("IsDeleted")
                        .HasColumnType("INTEGER");

                    b.Property<DateTime?>("LastModified")
                        .HasColumnType("TEXT");

                    b.Property<DateTime>("StartTime")
                        .HasColumnType("TEXT");

                    b.Property<int>("StoreId")
                        .HasColumnType("INTEGER");

                    b.HasKey("Id");

                    b.HasIndex("StoreId");

                    b.ToTable("StoreDays");
                });

            modelBuilder.Entity("POS.Core.Models.StoreSubCategoryLimit", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("INTEGER");

                    b.Property<bool>("IsDeleted")
                        .HasColumnType("INTEGER");

                    b.Property<DateTime?>("LastModified")
                        .HasColumnType("TEXT");

                    b.Property<int>("QuantityLimit")
                        .HasColumnType("INTEGER");

                    b.Property<int>("StoreId")
                        .HasColumnType("INTEGER");

                    b.Property<int>("SubCategoryId")
                        .HasColumnType("INTEGER");

                    b.HasKey("Id");

                    b.HasIndex("StoreId");

                    b.HasIndex("SubCategoryId");

                    b.ToTable("StoreSubCategoryLimits");
                });

            modelBuilder.Entity("POS.Core.Models.SubCatagory", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("INTEGER");

                    b.Property<string>("CashierAlert")
                        .HasColumnType("TEXT");

                    b.Property<int>("Catagory")
                        .HasColumnType("INTEGER");

                    b.Property<bool>("IsDeleted")
                        .HasColumnType("INTEGER");

                    b.Property<DateTime?>("LastModified")
                        .HasColumnType("TEXT");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasColumnType("TEXT");

                    b.Property<int>("StockType")
                        .HasColumnType("INTEGER");

                    b.HasKey("Id");

                    b.ToTable("SubCatagories");
                });

            modelBuilder.Entity("POS.Core.Models.SyncedTime", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("INTEGER");

                    b.Property<DateTime>("Time")
                        .HasColumnType("TEXT");

                    b.HasKey("Id");

                    b.ToTable("SyncedTimes");
                });

            modelBuilder.Entity("POS.Core.Models.User", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("INTEGER");

                    b.Property<string>("Discriminator")
                        .IsRequired()
                        .HasMaxLength(13)
                        .HasColumnType("TEXT");

                    b.Property<bool>("IsDeleted")
                        .HasColumnType("INTEGER");

                    b.Property<DateTime?>("LastModified")
                        .HasColumnType("TEXT");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasColumnType("TEXT");

                    b.Property<string>("Password")
                        .IsRequired()
                        .HasColumnType("TEXT");

                    b.Property<string>("Role")
                        .HasColumnType("TEXT");

                    b.Property<string>("UserId")
                        .HasColumnType("TEXT");

                    b.HasKey("Id");

                    b.ToTable("Users");

                    b.HasDiscriminator<string>("Discriminator").HasValue("User");

                    b.UseTphMappingStrategy();
                });

            modelBuilder.Entity("POS.Core.Models.Vat", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("INTEGER");

                    b.Property<string>("Code")
                        .IsRequired()
                        .HasColumnType("TEXT");

                    b.Property<bool>("IsDeleted")
                        .HasColumnType("INTEGER");

                    b.Property<DateTime?>("LastModified")
                        .HasColumnType("TEXT");

                    b.Property<string>("Rate")
                        .HasColumnType("TEXT");

                    b.Property<decimal>("Value")
                        .HasColumnType("TEXT");

                    b.HasKey("Id");

                    b.ToTable("Vats");
                });

            modelBuilder.Entity("POS.Core.Models.Void", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("INTEGER");

                    b.Property<bool>("IsDeleted")
                        .HasColumnType("INTEGER");

                    b.Property<DateTime?>("LastModified")
                        .HasColumnType("TEXT");

                    b.Property<int>("PosId")
                        .HasColumnType("INTEGER");

                    b.Property<int>("StoreId")
                        .HasColumnType("INTEGER");

                    b.Property<DateTime>("VoidTime")
                        .HasColumnType("TEXT");

                    b.HasKey("Id");

                    b.ToTable("Voids");
                });

            modelBuilder.Entity("POS.Core.Models.VoidSaleItem", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("INTEGER");

                    b.Property<bool>("IsDeleted")
                        .HasColumnType("INTEGER");

                    b.Property<DateTime?>("LastModified")
                        .HasColumnType("TEXT");

                    b.Property<string>("PLU")
                        .IsRequired()
                        .HasColumnType("TEXT");

                    b.Property<int>("Qty")
                        .HasColumnType("INTEGER");

                    b.Property<decimal>("Total")
                        .HasColumnType("TEXT");

                    b.Property<int?>("VoidId")
                        .HasColumnType("INTEGER");

                    b.HasKey("Id");

                    b.HasIndex("VoidId");

                    b.ToTable("VoidSaleItems");
                });

            modelBuilder.Entity("POS.Core.Models.Accountant", b =>
                {
                    b.HasBaseType("POS.Core.Models.User");

                    b.HasDiscriminator().HasValue("Accountant");
                });

            modelBuilder.Entity("POS.Core.Models.Admin", b =>
                {
                    b.HasBaseType("POS.Core.Models.User");

                    b.HasDiscriminator().HasValue("Admin");
                });

            modelBuilder.Entity("POS.Core.Models.BackOffice", b =>
                {
                    b.HasBaseType("POS.Core.Models.User");

                    b.Property<int?>("StoreId")
                        .HasColumnType("INTEGER");

                    b.HasIndex("StoreId");

                    b.HasDiscriminator().HasValue("BackOffice");
                });

            modelBuilder.Entity("POS.Core.Models.Cashier", b =>
                {
                    b.HasBaseType("POS.Core.Models.User");

                    b.HasDiscriminator().HasValue("Cashier");
                });

            modelBuilder.Entity("POS.Core.Models.CashierStore", b =>
                {
                    b.HasOne("POS.Core.Models.Cashier", "Cashier")
                        .WithMany("CashierStores")
                        .HasForeignKey("CashierId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("POS.Core.Models.Store", "Store")
                        .WithMany("CashierStores")
                        .HasForeignKey("StoreId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("Cashier");

                    b.Navigation("Store");
                });

            modelBuilder.Entity("POS.Core.Models.Division", b =>
                {
                    b.HasOne("POS.Core.Models.SubCatagory", "SubCatagory")
                        .WithMany("Divisions")
                        .HasForeignKey("SubCatagoryId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("SubCatagory");
                });

            modelBuilder.Entity("POS.Core.Models.IpAddress", b =>
                {
                    b.HasOne("POS.Core.Models.Accountant", null)
                        .WithMany("IpAddresses")
                        .HasForeignKey("AccountantId");

                    b.HasOne("POS.Core.Models.BackOffice", null)
                        .WithMany("IpAddresses")
                        .HasForeignKey("BackOfficeId");

                    b.HasOne("POS.Core.Models.User", "User")
                        .WithMany()
                        .HasForeignKey("UserId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("User");
                });

            modelBuilder.Entity("POS.Core.Models.MorrisonOrder", b =>
                {
                    b.HasOne("POS.Core.Models.Store", "Store")
                        .WithMany()
                        .HasForeignKey("StoreId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("Store");
                });

            modelBuilder.Entity("POS.Core.Models.MorrisonsOrderItem", b =>
                {
                    b.HasOne("POS.Core.Models.MorrisonOrder", "MorrisonOrder")
                        .WithMany("OrderItems")
                        .HasForeignKey("MorrisonOrderId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("MorrisonOrder");
                });

            modelBuilder.Entity("POS.Core.Models.NextOrderProduct", b =>
                {
                    b.HasOne("POS.Core.Models.Store", "Store")
                        .WithMany()
                        .HasForeignKey("StoreId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("Store");
                });

            modelBuilder.Entity("POS.Core.Models.NonBrandPromotion", b =>
                {
                    b.HasOne("POS.Core.Models.BackOffice", "CreatedBy")
                        .WithMany()
                        .HasForeignKey("CreatedById")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("POS.Core.Models.Store", "Store")
                        .WithMany()
                        .HasForeignKey("StoreId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("CreatedBy");

                    b.Navigation("Store");
                });

            modelBuilder.Entity("POS.Core.Models.PaidOut", b =>
                {
                    b.HasOne("POS.Core.Models.PaidOutOption", "PaidOutOption")
                        .WithMany()
                        .HasForeignKey("PaidOutOptionId");

                    b.HasOne("POS.Core.Models.Shift", "Shift")
                        .WithMany()
                        .HasForeignKey("ShiftId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("POS.Core.Models.Store", "Store")
                        .WithMany()
                        .HasForeignKey("StoreId");

                    b.Navigation("PaidOutOption");

                    b.Navigation("Shift");

                    b.Navigation("Store");
                });

            modelBuilder.Entity("POS.Core.Models.PosMachine", b =>
                {
                    b.HasOne("POS.Core.Models.Store", "Store")
                        .WithMany("PosMachines")
                        .HasForeignKey("StoreId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("Store");
                });

            modelBuilder.Entity("POS.Core.Models.Product", b =>
                {
                    b.HasOne("POS.Core.Models.Brand", "Brand")
                        .WithMany("Products")
                        .HasForeignKey("BrandId");

                    b.HasOne("POS.Core.Models.Division", "Division")
                        .WithMany("Products")
                        .HasForeignKey("DivisionId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("POS.Core.Models.NonBrandPromotion", "NonBrandPromotion")
                        .WithMany("Products")
                        .HasForeignKey("NonBrandPromotionId");

                    b.HasOne("POS.Core.Models.Promotion", "Promotion")
                        .WithMany("Products")
                        .HasForeignKey("PromotionId");

                    b.HasOne("POS.Core.Models.Vat", "Vat")
                        .WithMany("Products")
                        .HasForeignKey("VatId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("Brand");

                    b.Navigation("Division");

                    b.Navigation("NonBrandPromotion");

                    b.Navigation("Promotion");

                    b.Navigation("Vat");
                });

            modelBuilder.Entity("POS.Core.Models.ProductStore", b =>
                {
                    b.HasOne("POS.Core.Models.Product", "Product")
                        .WithMany("ProductStores")
                        .HasForeignKey("ProductId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("POS.Core.Models.Store", "Store")
                        .WithMany("ProductStores")
                        .HasForeignKey("StoreId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("Product");

                    b.Navigation("Store");
                });

            modelBuilder.Entity("POS.Core.Models.ProductStoreSale", b =>
                {
                    b.HasOne("POS.Core.Models.NonBrandPromotion", "NonBrandPromotion")
                        .WithMany()
                        .HasForeignKey("NonBrandPromotionId");

                    b.HasOne("POS.Core.Models.ProductStore", "ProductStore")
                        .WithMany("ProductStoreSales")
                        .HasForeignKey("ProductStoreId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("POS.Core.Models.Promotion", "Promotion")
                        .WithMany()
                        .HasForeignKey("PromotionId");

                    b.HasOne("POS.Core.Models.Sale", "Sale")
                        .WithMany("ProductStoreSales")
                        .HasForeignKey("SaleId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("NonBrandPromotion");

                    b.Navigation("ProductStore");

                    b.Navigation("Promotion");

                    b.Navigation("Sale");
                });

            modelBuilder.Entity("POS.Core.Models.QuickAccessButton", b =>
                {
                    b.HasOne("POS.Core.Models.Product", "Product")
                        .WithMany()
                        .HasForeignKey("ProductId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("POS.Core.Models.Store", "Store")
                        .WithMany()
                        .HasForeignKey("StoreId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("Product");

                    b.Navigation("Store");
                });

            modelBuilder.Entity("POS.Core.Models.Refund", b =>
                {
                    b.HasOne("POS.Core.Models.ProductStoreSale", "ProductStoreSale")
                        .WithMany()
                        .HasForeignKey("ProductStoreSaleId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("ProductStoreSale");
                });

            modelBuilder.Entity("POS.Core.Models.SafeDrop", b =>
                {
                    b.HasOne("POS.Core.Models.Store", "Store")
                        .WithMany()
                        .HasForeignKey("StoreId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("POS.Core.Models.Shift", "Shift")
                        .WithMany()
                        .HasForeignKey("shiftId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("Shift");

                    b.Navigation("Store");
                });

            modelBuilder.Entity("POS.Core.Models.Sale", b =>
                {
                    b.HasOne("POS.Core.Models.Shift", "Shift")
                        .WithMany()
                        .HasForeignKey("ShiftId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("Shift");
                });

            modelBuilder.Entity("POS.Core.Models.SalePaymentMethod2", b =>
                {
                    b.HasOne("POS.Core.Models.PaymentMethod", "PaymentMethod")
                        .WithMany("SalePaymentMethods")
                        .HasForeignKey("PaymentMethodId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("POS.Core.Models.Sale", "Sale")
                        .WithMany("SalePaymentMethods")
                        .HasForeignKey("SaleId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("PaymentMethod");

                    b.Navigation("Sale");
                });

            modelBuilder.Entity("POS.Core.Models.Shift", b =>
                {
                    b.HasOne("POS.Core.Models.Cashier", "Cashier")
                        .WithMany()
                        .HasForeignKey("CashierId");

                    b.HasOne("POS.Core.Models.PosMachine", "PosMachine")
                        .WithMany()
                        .HasForeignKey("PosMachineId");

                    b.HasOne("POS.Core.Models.StoreDay", "StoreDay")
                        .WithMany("Shifts")
                        .HasForeignKey("StoreDayId");

                    b.Navigation("Cashier");

                    b.Navigation("PosMachine");

                    b.Navigation("StoreDay");
                });

            modelBuilder.Entity("POS.Core.Models.SpecialProductSale", b =>
                {
                    b.HasOne("POS.Core.Models.Product", "Product")
                        .WithMany()
                        .HasForeignKey("ProductId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("POS.Core.Models.Sale", "Sale")
                        .WithMany("SpecialProductSales")
                        .HasForeignKey("SaleId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("Product");

                    b.Navigation("Sale");
                });

            modelBuilder.Entity("POS.Core.Models.Store", b =>
                {
                    b.HasOne("POS.Core.Models.Brand", "Brand")
                        .WithMany("Stores")
                        .HasForeignKey("BrandId");

                    b.HasOne("POS.Core.Models.Company", "Company")
                        .WithMany("Stores")
                        .HasForeignKey("CompanyId");

                    b.Navigation("Brand");

                    b.Navigation("Company");
                });

            modelBuilder.Entity("POS.Core.Models.StoreDay", b =>
                {
                    b.HasOne("POS.Core.Models.Store", "Store")
                        .WithMany()
                        .HasForeignKey("StoreId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("Store");
                });

            modelBuilder.Entity("POS.Core.Models.StoreSubCategoryLimit", b =>
                {
                    b.HasOne("POS.Core.Models.Store", "Store")
                        .WithMany("SubCategoryLimits")
                        .HasForeignKey("StoreId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("POS.Core.Models.SubCatagory", "SubCategory")
                        .WithMany("StoreLimits")
                        .HasForeignKey("SubCategoryId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("Store");

                    b.Navigation("SubCategory");
                });

            modelBuilder.Entity("POS.Core.Models.VoidSaleItem", b =>
                {
                    b.HasOne("POS.Core.Models.Void", null)
                        .WithMany("VoidSaleItems")
                        .HasForeignKey("VoidId");
                });

            modelBuilder.Entity("POS.Core.Models.BackOffice", b =>
                {
                    b.HasOne("POS.Core.Models.Store", "Store")
                        .WithMany()
                        .HasForeignKey("StoreId");

                    b.Navigation("Store");
                });

            modelBuilder.Entity("POS.Core.Models.Brand", b =>
                {
                    b.Navigation("Products");

                    b.Navigation("Stores");
                });

            modelBuilder.Entity("POS.Core.Models.Company", b =>
                {
                    b.Navigation("Stores");
                });

            modelBuilder.Entity("POS.Core.Models.Division", b =>
                {
                    b.Navigation("Products");
                });

            modelBuilder.Entity("POS.Core.Models.MorrisonOrder", b =>
                {
                    b.Navigation("OrderItems");
                });

            modelBuilder.Entity("POS.Core.Models.NonBrandPromotion", b =>
                {
                    b.Navigation("Products");
                });

            modelBuilder.Entity("POS.Core.Models.PaymentMethod", b =>
                {
                    b.Navigation("SalePaymentMethods");
                });

            modelBuilder.Entity("POS.Core.Models.Product", b =>
                {
                    b.Navigation("ProductStores");
                });

            modelBuilder.Entity("POS.Core.Models.ProductStore", b =>
                {
                    b.Navigation("ProductStoreSales");
                });

            modelBuilder.Entity("POS.Core.Models.Promotion", b =>
                {
                    b.Navigation("Products");
                });

            modelBuilder.Entity("POS.Core.Models.Sale", b =>
                {
                    b.Navigation("ProductStoreSales");

                    b.Navigation("SalePaymentMethods");

                    b.Navigation("SpecialProductSales");
                });

            modelBuilder.Entity("POS.Core.Models.Store", b =>
                {
                    b.Navigation("CashierStores");

                    b.Navigation("PosMachines");

                    b.Navigation("ProductStores");

                    b.Navigation("SubCategoryLimits");
                });

            modelBuilder.Entity("POS.Core.Models.StoreDay", b =>
                {
                    b.Navigation("Shifts");
                });

            modelBuilder.Entity("POS.Core.Models.SubCatagory", b =>
                {
                    b.Navigation("Divisions");

                    b.Navigation("StoreLimits");
                });

            modelBuilder.Entity("POS.Core.Models.Vat", b =>
                {
                    b.Navigation("Products");
                });

            modelBuilder.Entity("POS.Core.Models.Void", b =>
                {
                    b.Navigation("VoidSaleItems");
                });

            modelBuilder.Entity("POS.Core.Models.Accountant", b =>
                {
                    b.Navigation("IpAddresses");
                });

            modelBuilder.Entity("POS.Core.Models.BackOffice", b =>
                {
                    b.Navigation("IpAddresses");
                });

            modelBuilder.Entity("POS.Core.Models.Cashier", b =>
                {
                    b.Navigation("CashierStores");
                });
#pragma warning restore 612, 618
        }
    }
}
