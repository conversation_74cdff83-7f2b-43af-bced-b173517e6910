﻿using System;
using System.Collections.Generic;
using System.IO.Ports;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace POS.WPF
{
    public class BarcodeScannerService : IBarcodeScannerService
    {
        private SerialPort _serialPort;
        public event Action<string> BarcodeScanned;

        public BarcodeScannerService(string port)
        {
            _serialPort = new SerialPort(port, 9600, Parity.None, 8, StopBits.One);
            _serialPort.DataReceived += SerialPort_DataReceived;
        }

        private void SerialPort_DataReceived(object sender, SerialDataReceivedEventArgs e)
        {
            var data = _serialPort.ReadExisting().Trim();
            BarcodeScanned?.Invoke(data);
        }

        public void StartListening() => _serialPort.Open();
        public void StopListening() => _serialPort.Close();
    }
}
