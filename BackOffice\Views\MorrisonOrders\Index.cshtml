﻿@model IEnumerable<POS.Core.Models.MorrisonOrder>

@{
    ViewData["Title"] = "Orders";
}

<h2>Orders</h2>
<a asp-action="Create" class="btn btn-primary">Create New Order</a>

<table class="table">
    <thead>
        <tr>
            <th>Order ID</th>
            <th>Store</th>
            <th>Status</th>
            <th>Created Date</th>
            <th>Expected Delivery</th>
            <th>Actions</th>
        </tr>
    </thead>
    <tbody>
        @foreach (var order in Model)
        {
            <tr>
                <td>@order.GeneratedId</td>
                <td>@order.Store.StoreName</td>
                <td>@order.MorrisonOrderStatus</td>
                <td>@order.CreatedDate.ToShortDateString()</td>
                <td>@order.ExpectedDeliveryDate.ToShortDateString()</td>
                <td>
                    <a asp-action="Edit" asp-route-id="@order.Id" class="btn btn-warning btn-sm">Edit</a>
                    <a asp-action="Details" asp-route-id="@order.Id" class="btn btn-info btn-sm">Details</a>
                    <a asp-action="Delete" asp-route-id="@order.Id" class="btn btn-danger btn-sm">Delete</a>
                </td>
            </tr>
        }
    </tbody>
</table>
