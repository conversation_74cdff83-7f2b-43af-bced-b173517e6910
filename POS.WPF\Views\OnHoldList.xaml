﻿<Window x:Class="POS.WPF.Views.OnHoldList"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
        xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
        xmlns:local="clr-namespace:POS.WPF.Views"
        mc:Ignorable="d"
        Title="OnHoldList" Height="450" Width="800" Background="#004e7c" FontSize="24">

    <Grid Margin="10">
        <Grid.RowDefinitions>
            <RowDefinition Height="*" />
            <!-- Scrollable sale buttons -->
            <RowDefinition Height="Auto" />
            <!-- Cancel button -->
        </Grid.RowDefinitions>

        <!-- Scrollable stack of sale buttons -->
        <ScrollViewer VerticalScrollBarVisibility="Auto" Grid.Row="0">
            <StackPanel x:Name="SalesStackPanel" />
        </ScrollViewer>

        <!-- Fixed Cancel button -->
        <Button Content="Cancel"
                Grid.Row="1"
                Margin="0,10,0,0"
                Padding="5"
                Width="120"
                HorizontalAlignment="Right"
                Click="CancelButton_Click" />
    </Grid>
</Window>
