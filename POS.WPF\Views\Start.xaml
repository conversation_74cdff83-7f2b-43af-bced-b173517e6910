﻿<Window x:Class="POS.WPF.Start"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
        xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
        xmlns:local="clr-namespace:POS.WPF"
        mc:Ignorable="d"
        Title="Start" Height="450" Width="800">
    <Grid Margin="10">
        <!-- Define rows and columns -->
        <Grid.RowDefinitions>
			<RowDefinition Height="100px" />
            <RowDefinition Height="Auto" />
            <RowDefinition Height="Auto" />
            <RowDefinition Height="Auto" />
        </Grid.RowDefinitions>
        <Grid.ColumnDefinitions>
            <ColumnDefinition Width="Auto" />
            <ColumnDefinition Width="Auto" />
            <ColumnDefinition Width="Auto" />
        </Grid.ColumnDefinitions>


        <StackPanel Grid.Row="0" Grid.Column="0" Grid.ColumnSpan="3" Orientation="Horizontal"><Label>User:</Label>
            <Label x:Name="UserName"></Label>
            <Button>LogOut</Button></StackPanel>
		
        <Button Content="Open Login Window"
                Width="150" Height="40"
                HorizontalAlignment="Center"
                VerticalAlignment="Center"
                 Grid.Row="1" Grid.Column="0" Click="Button_Click_1"/>
        <Button x:Name="NewSale" IsEnabled="False" Grid.Row="1" Grid.Column="1" Content="New Sale" Margin="5" Width="100" Height="50" Click="Button_Click_2"/>
        <Button x:Name="Config" IsEnabled="False" Grid.Row="1" Grid.Column="2" Content="Config" Margin="5" Width="100" Height="50" Click="Config_Click"/>
        <Button IsEnabled="False" Grid.Row="2" Grid.Column="0" Content="Button 4" Margin="5" Width="100" Height="50" RenderTransformOrigin="0.5,0.5" />
        <Button IsEnabled="False" Grid.Row="2" Grid.Column="1" Content="Button 5" Margin="5" Width="100" Height="50"/>
        <Button IsEnabled="False" Grid.Row="2" Grid.Column="2" Content="Button 6" Margin="5" Width="100" Height="50"/>
    </Grid>
</Window>
