﻿using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using POS.Core.Models;
using System.Security.Claims;

namespace BackOffice.Controllers
{
    // Remove the controller-level authorization
    // [Authorize(Roles = "Admin")]
    public class StoresController : Controller
    {
        private readonly Data _context;

        public StoresController(Data context)
        {
            _context = context;
        }

        // GET: Stores
        [Authorize(Roles = "Admin")]
        public async Task<IActionResult> Index()
        {
            var stores = await _context.Stores
                .Include(s => s.Brand)
                .Include(s => s.Company)
                .Include(s => s.CashierStores)
                    .ThenInclude(sc => sc.Cashier)
                .ToListAsync();

            return View(stores);
        }

        // GET: Stores/Details/5
        [Authorize(Roles = "Admin")]
        public async Task<IActionResult> Details(int? id)
        {
            if (id == null) return NotFound();

            var store = await _context.Stores
     .Include(s => s.CashierStores) 
         .ThenInclude(sc => sc.Cashier)
     .FirstOrDefaultAsync(m => m.StoreId == id);

            if (store == null) return NotFound();

            return View(store);
        }

        // GET: Stores/Create
        [Authorize(Roles = "Admin")]
        public IActionResult Create()
        {
            try
            {
                ViewBag.Cashiers = _context.Cashiers.ToList();
                ViewBag.Companies = _context.Companies.ToList();
                ViewBag.Brands = _context.Brands.ToList();

                return View();
            }
            catch (Exception ex)
            {
                return null;
            }
        }

        // POST: Stores/Create
        [HttpPost]
        [ValidateAntiForgeryToken]
        [Authorize(Roles = "Admin")]
        public async Task<IActionResult> Create(Store store)
        {

            try
            {
                if (string.IsNullOrEmpty(store.StoreName))
                {
                    ModelState.AddModelError("StoreName", "Store name is required");
                    return View(store);
                }
                if (_context.Stores.Any(s => s.StoreName == store.StoreName))
                {
                    ModelState.AddModelError("StoreName", "Store name already exists");
                    return View(store);
                }

                // Ensure StoreIdForAPI is unique if needed
                if (!string.IsNullOrEmpty(store.StoreIdForAPI) && _context.Stores.Any(s => s.StoreIdForAPI == store.StoreIdForAPI))
                {
                    ModelState.AddModelError("StoreIdForAPI", "API Store ID must be unique");
                    return View(store);
                }

                var products = _context.Products.Where(p => p.BrandId == store.BrandId).ToList();

                _context.Add(store);
                await _context.SaveChangesAsync();

                var productStores = products.Select(product => new ProductStore
                {
                    ProductId = product.Id,
                    StoreId = store.StoreId,
                    InventoryCount = 0,
                    StoreSpecificPrice = product.SellingPrice,
                }).ToList();

                _context.AddRange(productStores);
                await _context.SaveChangesAsync();

                return RedirectToAction(nameof(Index));
            }
            catch (Exception ex)
            {
                return null;
            }
        }



        // GET: Stores/Edit/5
        [Authorize(Roles = "Admin")]
        public async Task<IActionResult> Edit(int? id)
        {
            try
            {
                if (id == null) return NotFound();

                ViewBag.Divisions = _context.Divisions.ToList();

                // Filter to only include product stores for the first division
                var firstDivision = ((List<Division>)ViewBag.Divisions).FirstOrDefault();


                var store = await _context.Stores
                    .Include(s => s.PosMachines)
                    .Include(s => s.ProductStores.Where(ps => ps.Product.DivisionId == firstDivision.Id))
                        .ThenInclude(ps => ps.Product)
                    .FirstOrDefaultAsync(s => s.StoreId == id);



                if (store == null) return NotFound();

                ViewBag.Cashiers = _context.Cashiers.ToList();
                ViewBag.Companies = _context.Companies.ToList();
                ViewBag.Brands = _context.Brands.ToList();

                if (firstDivision != null)
                {
                    store.ProductStores = store.ProductStores
                        .Where(ps => ps.Product.DivisionId == firstDivision.Id)
                        .ToList();
                }

                return View(store);
            }
            catch (Exception ex)
            {
                return Content(ex.Message);
            }
        }

        [HttpGet]
        [Authorize(Roles = "Admin,BackOffice")]
        public async Task<IActionResult> GetProductsByDivision(int storeId, int divisionId)
        {
            var productStores = await _context.ProductStores
                .Include(ps => ps.Product)
                .Where(ps => ps.StoreId == storeId && ps.Product.DivisionId == divisionId)
                .ToListAsync();

            return PartialView("_ProductStoresPartial", productStores);
        }

        [HttpGet]
        [Authorize(Roles = "BackOffice")]
        public async Task<IActionResult> GetProductsByDivisionForPrices(int storeId, int divisionId)
        {
            // Verify the current user has access to this store
            var userId = int.Parse(User.Claims.FirstOrDefault(c => c.Type == ClaimTypes.NameIdentifier)?.Value);
            var backOffice = _context.BackOffices.Find(userId);
            
            if (backOffice.StoreId != storeId)
            {
                return Forbid();
            }
            
            var productStores = await _context.ProductStores
                .Include(ps => ps.Product)
                .Where(ps => ps.StoreId == storeId && ps.Product.DivisionId == divisionId)
                .ToListAsync();

            return PartialView("_ProductStoresPricesPartial", productStores);
        }




        [HttpPost]
        [ValidateAntiForgeryToken]
        [Authorize(Roles = "Admin")]
        public async Task<IActionResult> Edit(int id, Store store, IFormCollection form)
        {
            if (id != store.StoreId) return NotFound();

            try
            {
                // Debug: Log form keys
                System.Diagnostics.Debug.WriteLine($"Form keys: {string.Join(", ", form.Keys)}");

                // Ensure StoreIdForAPI is unique if needed
                if (!string.IsNullOrEmpty(store.StoreIdForAPI) && _context.Stores.Any(s => s.StoreIdForAPI == store.StoreIdForAPI && s.StoreId != id))
                {
                    ModelState.AddModelError("StoreIdForAPI", "API Store ID must be unique");
                    return View(store);
                }

                var existingStore = await _context.Stores.FindAsync(id);
                if (existingStore == null) return NotFound();

                existingStore.StoreName = store.StoreName;
                existingStore.BrandId = store.BrandId;
                existingStore.CompanyId = store.CompanyId;
                existingStore.AllowNegativeInventory = store.AllowNegativeInventory;
                existingStore.StoreIdForAPI = store.StoreIdForAPI;
                existingStore.MainPosMachineId = store.MainPosMachineId;

                _context.Update(existingStore);

                // Extract product store data from form
                var productStoreIds = form.Keys
                    .Where(k => k.StartsWith("ProductStores[") && k.EndsWith("].ProductStoreId"))
                    .Select(k => form[k].ToString())
                    .Where(v => !string.IsNullOrEmpty(v))
                    .Select(int.Parse)
                    .ToList();

                System.Diagnostics.Debug.WriteLine($"Found {productStoreIds.Count} product store IDs in form");

                foreach (var productStoreId in productStoreIds)
                {
                    var existingProductStore = await _context.ProductStores.FindAsync(productStoreId);
                    if (existingProductStore != null && existingProductStore.StoreId == id)
                    {
                        // Get the store specific price from form
                        var priceKey = $"ProductStores[{productStoreId}].StoreSpecificPrice";
                        var inventoryKey = $"ProductStores[{productStoreId}].InventoryCount";

                        System.Diagnostics.Debug.WriteLine($"Looking for keys: {priceKey}, {inventoryKey}");

                        if (form.ContainsKey(priceKey) && !string.IsNullOrWhiteSpace(form[priceKey]))
                        {
                            decimal.TryParse(form[priceKey], out decimal price);
                            existingProductStore.StoreSpecificPrice = price;
                            System.Diagnostics.Debug.WriteLine($"Setting price to {price}");
                        }
                        else
                        {
                            existingProductStore.StoreSpecificPrice = null;
                            System.Diagnostics.Debug.WriteLine("Setting price to null");
                        }

                        if (form.ContainsKey(inventoryKey) && !string.IsNullOrWhiteSpace(form[inventoryKey]))
                        {
                            int.TryParse(form[inventoryKey], out int inventory);
                            existingProductStore.InventoryCount = inventory;
                            System.Diagnostics.Debug.WriteLine($"Setting inventory to {inventory}");
                        }

                        _context.Update(existingProductStore);
                    }
                }

                await _context.SaveChangesAsync();
            }
            catch (Exception ex)
            {
                // Log any exceptions
                System.Diagnostics.Debug.WriteLine($"Exception in Edit: {ex.Message}");
                System.Diagnostics.Debug.WriteLine(ex.StackTrace);
                
                if (ex is DbUpdateConcurrencyException && !_context.Stores.Any(e => e.StoreId == id))
                    return NotFound();
                    
                throw;
            }

            return RedirectToAction(nameof(Index));
        }



        // GET: Stores/Delete/5
        [Authorize(Roles = "Admin")]
        public async Task<IActionResult> Delete(int? id)
        {
            if (id == null) return NotFound();

            var store = await _context.Stores
     .Include(s => s.CashierStores)
         .ThenInclude(sc => sc.Cashier) // load Cashier entity
     .FirstOrDefaultAsync(m => m.StoreId == id);


            if (store == null) return NotFound();

            return View(store);
        }

        // POST: Stores/Delete/5
        [HttpPost, ActionName("Delete")]
        [ValidateAntiForgeryToken]
        [Authorize(Roles = "Admin")]
        public async Task<IActionResult> DeleteConfirmed(int id)
        {
            var store = await _context.Stores.FindAsync(id);
            _context.Stores.Remove(store);
            await _context.SaveChangesAsync();
            return RedirectToAction(nameof(Index));
        }

        // GET: Stores/EditBackOffice
        [Authorize(Roles = "BackOffice")]
        public async Task<IActionResult> EditBackOffice()
        {
            try
            {
                // Get the current user's ID and find their associated store
                var userId = int.Parse(User.Claims.FirstOrDefault(c => c.Type == ClaimTypes.NameIdentifier)?.Value);
                var backOffice = _context.BackOffices.Find(userId);
                var storeId = backOffice.StoreId;

                ViewBag.Divisions = _context.Divisions.ToList();
                var firstDivision = ((List<Division>)ViewBag.Divisions).FirstOrDefault();

                var store = await _context.Stores
                    .Include(s => s.PosMachines)
                    .Include(s => s.ProductStores.Where(ps => ps.Product.DivisionId == firstDivision.Id))
                        .ThenInclude(ps => ps.Product)
                    .Include(s => s.Brand)
                    .Include(s => s.Company)
                    .FirstOrDefaultAsync(s => s.StoreId == storeId);

                if (store == null) return NotFound();

                if (firstDivision != null)
                {
                    store.ProductStores = store.ProductStores
                        .Where(ps => ps.Product.DivisionId == firstDivision.Id)
                        .ToList();
                }

                return View(store);
            }
            catch (Exception ex)
            {
                return Content(ex.Message);
            }
        }

        // POST: Stores/EditBackOffice
        [HttpPost]
        [ValidateAntiForgeryToken]
        [Authorize(Roles = "BackOffice")]
        public async Task<IActionResult> EditBackOffice(Store store, IFormCollection form)
        {
            try
            {
                // Get the current user's store ID
                var userId = int.Parse(User.Claims.FirstOrDefault(c => c.Type == ClaimTypes.NameIdentifier)?.Value);
                var backOffice = _context.BackOffices.Find(userId);
                var storeId = backOffice.StoreId;

                if (storeId != store.StoreId) return NotFound();

                // Debug: Log all form keys and values
                foreach (var key in form.Keys)
                {
                    System.Diagnostics.Debug.WriteLine($"Form key: {key}, Value: {form[key]}");
                }

                var existingStore = await _context.Stores.FindAsync(storeId);
                if (existingStore == null) return NotFound();

                // Only update the allowed fields
                existingStore.AllowNegativeInventory = store.AllowNegativeInventory;
                _context.Update(existingStore);

                // Process each product store entry
                foreach (var key in form.Keys.Where(k => k.Contains("StoreSpecificPrice")))
                {
                    // Extract the ProductStoreId from the key name
                    // Format is "ProductStores[123].StoreSpecificPrice"
                    var match = System.Text.RegularExpressions.Regex.Match(key, @"ProductStores\[(\d+)\]");
                    if (match.Success && int.TryParse(match.Groups[1].Value, out int productStoreId))
                    {
                        System.Diagnostics.Debug.WriteLine($"Processing ProductStore ID: {productStoreId}");
                        
                        var existingProductStore = await _context.ProductStores.FindAsync(productStoreId);
                        if (existingProductStore != null && existingProductStore.StoreId == storeId)
                        {
                            var priceValue = form[key].ToString();
                            System.Diagnostics.Debug.WriteLine($"Price value from form: '{priceValue}'");
                            
                            // Update store specific price
                            if (!string.IsNullOrWhiteSpace(priceValue) && decimal.TryParse(priceValue, out decimal price))
                            {
                                System.Diagnostics.Debug.WriteLine($"Updating price from {existingProductStore.StoreSpecificPrice} to {price}");
                                existingProductStore.StoreSpecificPrice = price;
                            }
                            else
                            {
                                System.Diagnostics.Debug.WriteLine($"Setting price to null (empty or invalid value: '{priceValue}')");
                                existingProductStore.StoreSpecificPrice = null;
                            }
                            
                            // Update inventory count if present
                            var inventoryKey = $"ProductStores[{productStoreId}].InventoryCount";
                            if (form.ContainsKey(inventoryKey) && int.TryParse(form[inventoryKey], out int inventory))
                            {
                                System.Diagnostics.Debug.WriteLine($"Updating inventory from {existingProductStore.InventoryCount} to {inventory}");
                                existingProductStore.InventoryCount = inventory;
                            }
                            
                            _context.Update(existingProductStore);
                            System.Diagnostics.Debug.WriteLine($"ProductStore {productStoreId} updated in context");
                        }
                    }
                }

                // Save all changes
                var changes = await _context.SaveChangesAsync();
                System.Diagnostics.Debug.WriteLine($"SaveChangesAsync completed with {changes} changes");
                
                return RedirectToAction("Index", "BackOffice");
            }
            catch (Exception ex)
            {
                // Log any exceptions
                System.Diagnostics.Debug.WriteLine($"Exception in EditBackOffice: {ex.Message}");
                System.Diagnostics.Debug.WriteLine(ex.StackTrace);
                throw;
            }
        }

        // Add this new action for bulk price editing
        [Authorize(Roles = "BackOffice")]
        public async Task<IActionResult> EditPrices()
        {
            try
            {
                // Get the current user's store ID
                var userId = int.Parse(User.Claims.FirstOrDefault(c => c.Type == ClaimTypes.NameIdentifier)?.Value);
                var backOffice = _context.BackOffices.Find(userId);
                var storeId = backOffice.StoreId;

                ViewBag.Divisions = _context.Divisions.ToList();
                var firstDivision = ((List<Division>)ViewBag.Divisions).FirstOrDefault();

                var store = await _context.Stores
                    .Include(s => s.ProductStores.Where(ps => ps.Product.DivisionId == firstDivision.Id))
                        .ThenInclude(ps => ps.Product)
                    .FirstOrDefaultAsync(s => s.StoreId == storeId);

                if (store == null) return NotFound();

                return View(store);
            }
            catch (Exception ex)
            {
                return Content(ex.Message);
            }
        }

        [HttpPost]
        [ValidateAntiForgeryToken]
        [Authorize(Roles = "BackOffice")]
        public async Task<IActionResult> SavePrices(IFormCollection form)
        {
            try
            {
                // Get the current user's store ID
                var userId = int.Parse(User.Claims.FirstOrDefault(c => c.Type == ClaimTypes.NameIdentifier)?.Value);
                var backOffice = _context.BackOffices.Find(userId);
                var storeId = backOffice.StoreId;

                // Debug: Log form keys and values
                var formDebug = string.Join(", ", form.Keys.Select(k => $"{k}: {form[k]}"));
                System.Diagnostics.Debug.WriteLine($"Form data: {formDebug}");

                // Process form data manually
                var productStoreIds = form.Where(f => f.Key == "ProductStoreId").SelectMany(f => f.Value).ToList();
                var storeSpecificPrices = form.Where(f => f.Key == "StoreSpecificPrice").SelectMany(f => f.Value).ToList();

                // Debug: Log extracted values
                System.Diagnostics.Debug.WriteLine($"ProductStoreIds count: {productStoreIds.Count}");
                System.Diagnostics.Debug.WriteLine($"StoreSpecificPrices count: {storeSpecificPrices.Count}");

                // Update product stores prices
                int updatedCount = 0;
                for (int i = 0; i < productStoreIds.Count; i++)
                {
                    if (int.TryParse(productStoreIds[i], out int productStoreId))
                    {
                        var existingProductStore = await _context.ProductStores.FindAsync(productStoreId);
                        if (existingProductStore != null && existingProductStore.StoreId == storeId)
                        {
                            decimal? oldPrice = existingProductStore.StoreSpecificPrice;
                            
                            // Parse the price value
                            if (!string.IsNullOrWhiteSpace(storeSpecificPrices[i]) && 
                                decimal.TryParse(storeSpecificPrices[i], out decimal price))
                            {
                                existingProductStore.StoreSpecificPrice = price;
                            }
                            else
                            {
                                existingProductStore.StoreSpecificPrice = null;
                            }
                            
                            _context.Update(existingProductStore);
                            updatedCount++;
                            
                            // Debug: Log price changes
                            System.Diagnostics.Debug.WriteLine($"ProductStore {productStoreId}: {oldPrice} -> {existingProductStore.StoreSpecificPrice}");
                        }
                    }
                }

                await _context.SaveChangesAsync();
                TempData["SuccessMessage"] = $"Prices updated successfully. Updated {updatedCount} products.";
                return RedirectToAction("EditPrices");
            }
            catch (Exception ex)
            {
                TempData["ErrorMessage"] = "Error updating prices: " + ex.Message;
                System.Diagnostics.Debug.WriteLine($"Exception: {ex}");
                return RedirectToAction("EditPrices");
            }
        }
    }
}
