﻿@{
    ViewData["Title"] = "BackOffice Dashboard";
}

<a style="width: 100%;margin-bottom: 0.5rem" asp-controller="NextOrder" asp-action="Index" class="btn btn-primary btn-block">Next Order</a>
<a style="width: 100%;margin-bottom: 0.5rem" asp-controller="MorrisonOrders" asp-action="Index" class="btn btn-primary btn-block">Previous Orders</a>
<a style="width: 100%;margin-bottom: 0.5rem" asp-controller="NonBrandPromotion" asp-action="Index" class="btn btn-primary btn-block">Non Brand Promotion</a>
<a style="width: 100%;margin-bottom: 0.5rem" asp-controller="Reports" asp-action="DailySalesReport" class="btn btn-primary btn-block">Daily Sales Report</a>
<a style="width: 100%;margin-bottom: 0.5rem" asp-controller="Reports" asp-action="MonthlySaleReport" class="btn btn-primary btn-block">Monthly Sales Report</a>
<a style="width: 100%;margin-bottom: 0.5rem" asp-controller="Reports" asp-action="MonthlySalesReportExtended" class="btn btn-primary btn-block">Monthly Sales Report Extended</a>
<a style="width: 100%;margin-bottom: 0.5rem" asp-controller="Reports" asp-action="DailySalesReportExtended" class="btn btn-primary btn-block">Daily Sales Report Extended</a>
<a style="width: 100%;margin-bottom: 0.5rem" asp-controller="Reports" asp-action="MonthlyProductQuantityReport" class="btn btn-primary btn-block">Monthly Product Quantity Report</a>
<a style="width: 100%;margin-bottom: 0.5rem" asp-controller="Vat" asp-action="Report" class="btn btn-primary btn-block">Vat Report</a>

<a style="width: 100%;margin-bottom: 0.5rem" asp-controller="Print" asp-action="SelectProduct" class="btn btn-primary btn-block">Print Labels</a>
<a style="width: 100%;margin-bottom: 0.5rem" asp-controller="Stores" asp-action="EditPrices" class="btn btn-success">Edit Prices</a>
<a style="width: 100%;margin-bottom: 0.5rem" asp-controller="SubCatagory" asp-action="ManageQuantityLimits" class="btn btn-success btn-block">Manage Product Quantity Limits</a>

<div class="container mt-4">
    <div class="row mt-4">
        <div class="col-md-4 mb-3">
            <div class="card">
                <div class="card-body">
                    <h5 class="card-title">Transaction Management</h5>
                    <ul class="list-group list-group-flush">
                        <li class="list-group-item">
                            <a asp-action="OnHoldTransactions">View On-Hold Transactions</a>
                        </li>
                        <li class="list-group-item">
                            <a asp-action="DeletedSales">View Deleted Sales</a>
                        </li>
                        <li class="list-group-item">
                            <a asp-action="VoidSales">View Void Sales</a>
                        </li>
                    </ul>
                </div>
            </div>
        </div>

        <div class="col-md-4 mb-3">
            <div class="card">
                <div class="card-body">
                    <h5 class="card-title">Terminal Management</h5>
                    <ul class="list-group list-group-flush">
                        <li class="list-group-item">
                            <a asp-action="TerminalSales">View Terminal Sales</a>
                        </li>
                    </ul>
                </div>
            </div>
        </div>
    </div>
</div>
