﻿using RefactoredPrinter.Interfaces;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
namespace RefactoredPrinter
{
    public class ThermalPrinterFormatting : IFormattingService
    {
        // Implement formatting specific to thermal printers
        public string CreateFourColumnLine()
        {
            throw new NotImplementedException();
        }

        public string CreateMixedAlignmentLine(string left, string right, int width)
        {
            throw new NotImplementedException();
        }
    }
}
