﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace POS.Core.Models
{
    public abstract class BaseEntity
    {
        /// <summary>
        /// Indicates if this entity has been soft-deleted.
        /// This is automatically managed by the Data context's SaveChanges methods.
        /// </summary>
        public bool IsDeleted { get; set; } = false;

        /// <summary>
        /// Timestamp of the last modification to this entity.
        /// This is automatically updated by the Data context's SaveChanges methods.
        /// </summary>
        public DateTime? LastModified { get; set; }
    }
}
