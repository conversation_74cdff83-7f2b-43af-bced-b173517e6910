using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace POS.Core.Models
{
    public class Division : BaseEntity
    {
        public int Id { get; set; }
        public string Name { get; set; }
        public SubCatagory SubCatagory { get; set; }

        public int SubCatagoryId { get; set; }


        public ICollection<Product> Products { get; set; } = new List<Product>(); // Navigation Property <==>


       

      
    }

}
