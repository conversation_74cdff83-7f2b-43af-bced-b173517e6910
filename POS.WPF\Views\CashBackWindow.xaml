﻿<Window x:Class="POS.WPF.Views.CashBackWindow"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
xmlns:local="clr-namespace:POS.WPF.Views"
mc:Ignorable="d"
Title="Cash Back" Height="450" Width="800"
WindowStartupLocation="CenterScreen">
    <!-- Match main window background -->
    <Grid Margin="20">
        <StackPanel 
            VerticalAlignment="Center" 
            HorizontalAlignment="Center"
            Width="300">

            <!-- Label -->
            <TextBlock 
                Text="Enter Cash Back Amount:"
                FontSize="16"
                Foreground="White"
                Margin="0 0 0 10"/>

            <!-- Styled TextBox -->
            <TextBox 
                Name="txtCashBack"
                Width="300"
                Height="50"
                FontSize="24"
                Background="#0a688d"
                Foreground="White"
                HorizontalContentAlignment="Center"
                VerticalContentAlignment="Center"
                Margin="0 0 0 20"/>

            <!-- Button Panel -->
            <StackPanel 
                Orientation="Horizontal"
                HorizontalAlignment="Right">
                <Button 
                    Name="btnSet"
                    Content="SET"
                    Width="100"
                    Height="40"
                    FontSize="16"
                    Background="#004e7c"
                    Foreground="White"
                    Margin="0 0 10 0"
                    Click="btnSet_Click"/>
                <Button 
                    Name="btnIgnore"
                    Content="IGNORE"
                    Width="100"
                    Height="40"
                    FontSize="16"
                    Background="#004e7c"
                    Foreground="White"
                    Click="btnIgnore_Click"/>
                
            </StackPanel>
            <!-- Numeric Keypad -->
            <local:NumericKeypad Grid.Row="1"
             Margin="10"
             Background="#0a688d"
             KeyPressed="NumericKeypad_KeyPressed"/>
        </StackPanel>

        

    </Grid>
</Window>