using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace TimeManagement
{
    public class DisplayTimeProvider
    {
        private static TimeZoneInfo _currentTimeZone = TimeZoneInfo.Utc;
        private static bool _useCustomTime = false;
        private static DateTime? _customTime = null;

        public static DateTime Now
        {
            get
            {
                if (_useCustomTime && _customTime.HasValue)
                {
                    return _customTime.Value;
                }
                return TimeZoneInfo.ConvertTime(DateTime.UtcNow, _currentTimeZone);
            }
        }

        public static DateTime ConvertToDisplayTime(DateTime utcTime)
        {
            return TimeZoneInfo.ConvertTime(utcTime, _currentTimeZone);
        }

        public static DateTime ConvertToUtc(DateTime localTime)
        {
            return TimeZoneInfo.ConvertTimeToUtc(localTime, _currentTimeZone);
        }

        public static void SetTimeZone(string timeZoneId)
        {
            try
            {
                _currentTimeZone = TimeZoneInfo.FindSystemTimeZoneById(timeZoneId);
            }
            catch (TimeZoneNotFoundException)
            {
                // Fallback to local timezone if the specified timezone is not found
                _currentTimeZone = TimeZoneInfo.Local;
            }
        }

        public static void SetCustomTime(DateTime time)
        {
            _customTime = time;
            _useCustomTime = true;
        }

        public static void ResetToSystemTime()
        {
            _useCustomTime = false;
            _customTime = null;
        }

        public static string[] GetAvailableTimeZones()
        {
            return TimeZoneInfo.GetSystemTimeZones()
                .Select(tz => tz.Id)
                .ToArray();
        }
    }
} 