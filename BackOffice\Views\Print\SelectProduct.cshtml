﻿@{
    ViewData["Title"] = "Select Product";
}

<div class="container mt-4">
    <h3>Select Products for Labels</h3>

    @if (TempData["Success"] != null)
    {
        <div class="alert alert-success">@TempData["Success"]</div>
    }
    @if (TempData["Error"] != null)
    {
        <div class="alert alert-danger">@TempData["Error"]</div>
    }

    <div class="mb-3">
        <a href="@Url.Action("UploadCsv", "Print")" class="btn btn-info">Upload CSV with Barcodes</a>
    </div>

    <form method="post" action="@Url.Action("ProcessSelectedProducts", "Print")">
        <div class="row">
            <div class="col-md-6">
                <div class="form-group">
                    <label for="brandDropdown">Select Brand:</label>
                    <select id="brandDropdown" class="form-control">
                        <option value="">-- Select a Brand --</option>
                        @foreach (var brand in ViewBag.Brands)
                        {
                            <option value="@brand.Id">@brand.Name</option>
                        }
                    </select>
                </div>

                <div class="form-group mt-3">
                    <label for="productDropdown">Select Product:</label>
                    <select id="productDropdown" class="form-control" disabled>
                        <option value="">-- Select a Product --</option>
                    </select>
                </div>

                <button type="button" id="addProductButton" class="btn btn-success mt-3" disabled>Add Product</button>
            </div>

            <div class="col-md-6">
                <div class="form-group">
                    <label for="selectedProducts">Selected Products:</label>
                    <select id="selectedProducts" name="SelectedProductIds" multiple class="form-control" size="10" style="min-height: 200px;"></select>
                </div>
                
                <button type="button" id="removeProductButton" class="btn btn-danger mt-2" disabled>Remove Selected</button>
            </div>
        </div>

        <div class="mt-4">
            <button type="submit" class="btn btn-primary" id="submitBtn" disabled>Generate Labels</button>
        </div>
    </form>
</div>

<script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
<script>
    $(document).ready(function () {
        // When a brand is selected, load the products
        $('#brandDropdown').change(function () {
            var brandId = $(this).val();
            var productDropdown = $('#productDropdown');

            productDropdown.empty().append('<option value="">-- Select a Product --</option>').prop('disabled', true);
            $('#addProductButton').prop('disabled', true);

            if (brandId) {
                $.getJSON('/Print/GetProductsByBrand', { brandId: brandId }, function (data) {
                    $.each(data, function (index, product) {
                        productDropdown.append($('<option>', {
                            value: product.id,
                            text: product.name
                        }));
                    });
                    productDropdown.prop('disabled', false);
                });
            }
        });

        // Enable/disable the Add button based on product selection
        $('#productDropdown').change(function () {
            var selectedProduct = $(this).val();
            $('#addProductButton').prop('disabled', !selectedProduct);
        });

        // Add product to the selected products list
        $('#addProductButton').click(function () {
            var productId = $('#productDropdown').val();
            var productName = $('#productDropdown option:selected').text();
            
            if (productId && productName) {
                // Check if already in the list to avoid duplicates
                if ($('#selectedProducts option[value="' + productId + '"]').length === 0) {
                    $('#selectedProducts').append($('<option>', {
                        value: productId,
                        text: productName
                    }));
                    
                    // Enable the submit button if we have at least one product
                    $('#submitBtn').prop('disabled', false);
                }
            }
        });

        // Enable/disable the Remove button based on selection
        $('#selectedProducts').change(function() {
            $('#removeProductButton').prop('disabled', !$(this).val());
        });

        // Remove selected product from the list
        $('#removeProductButton').click(function() {
            $('#selectedProducts option:selected').remove();
            
            // Disable submit button if no products are selected
            if ($('#selectedProducts option').length === 0) {
                $('#submitBtn').prop('disabled', true);
            }
        });

        // Make sure all selected products are marked as selected when submitting
        $('form').submit(function() {
            $('#selectedProducts option').prop('selected', true);
            return $('#selectedProducts option').length > 0;
        });
    });
</script>
