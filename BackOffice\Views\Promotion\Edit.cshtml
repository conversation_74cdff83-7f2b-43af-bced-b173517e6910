﻿@model POS.Core.Models.Promotion
@{
    ViewData["Title"] = "Edit Promotion";
}

<h2>Edit Promotion</h2>

<form asp-action="Edit" method="post">
    <input type="hidden" asp-for="Id" />

    <div class="form-group">
        <label asp-for="Name"></label>
        <input asp-for="Name" class="form-control" />
    </div>

    <div class="form-group">
        <label asp-for="Identifire"></label>
        <input asp-for="Identifire" class="form-control" />
    </div>

    <div class="form-group">
        <label asp-for="Type"></label>
        <select asp-for="Type" class="form-control" asp-items="Html.GetEnumSelectList<POS.Core.Models.PromotionType>()"></select>
    </div>

    <div class="form-group">
        <label asp-for="PromotionPrice"></label>
        <input asp-for="PromotionPrice" class="form-control" />
    </div>

    <div class="form-group">
        <label asp-for="Quantity"></label>
        <input asp-for="Quantity" class="form-control" />
    </div>

    <div class="form-group">
        <label asp-for="StartTime"></label>
        <input asp-for="StartTime" type="datetime-local" class="form-control" />
    </div>

    <div class="form-group">
        <label asp-for="EndTime"></label>
        <input asp-for="EndTime" type="datetime-local" class="form-control" />
    </div>

    <div class="form-group">
        <label for="productSearch">Search Products</label>
        <input id="productSearch" type="text" class="form-control" placeholder="Type to search products..." />
    </div>

    <div class="form-group">
        <label for="selectedProductsList">Selected Products</label>
        <div class="selected-products-container">
            <ul id="selectedProductsList" class="list-group"></ul>
        </div>
        <input type="hidden" id="productSelect" name="selectedProducts" />
    </div>

    <button type="submit" class="btn btn-primary">Save</button>
    <a asp-action="Index" class="btn btn-secondary">Cancel</a>
</form>

@section Scripts {
    <script src="https://cdn.jsdelivr.net/npm/jquery-ui@1.13.2/dist/jquery-ui.min.js"></script>
    <link href="https://cdn.jsdelivr.net/npm/jquery-ui@1.13.2/dist/themes/base/jquery-ui.min.css" rel="stylesheet" />
    <style>
        .selected-products-container {
            max-height: 200px;
            overflow-y: auto;
            border: 1px solid #ced4da;
            border-radius: 0.25rem;
            padding: 0.5rem;
            margin-bottom: 1rem;
        }
        .product-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 0.25rem;
        }
        .remove-product {
            cursor: pointer;
            color: #dc3545;
        }
    </style>
    <script>
        $(document).ready(function () {
            // Store selected products
            var selectedProducts = [];
            
            // Initialize autocomplete
            $("#productSearch").autocomplete({
                source: function(request, response) {
                    $.ajax({
                        url: '/Promotion/SearchProducts', // Updated endpoint
                        dataType: 'json',
                        data: { term: request.term },
                        success: function(data) {
                            response($.map(data, function(item) {
                                return {
                                    label: item.text,
                                    value: item.text,
                                    id: item.id
                                };
                            }));
                        }
                    });
                },
                minLength: 2,
                select: function(event, ui) {
                    // Prevent adding duplicate products
                    if (!selectedProducts.some(p => p.id === ui.item.id)) {
                        // Add to selected products array
                        selectedProducts.push({
                            id: ui.item.id,
                            text: ui.item.label
                        });
                        
                        // Add to visual list
                        $("#selectedProductsList").append(
                            `<li class="list-group-item product-item" data-id="${ui.item.id}">
                                ${ui.item.label}
                                <span class="remove-product">&times;</span>
                            </li>`
                        );
                        
                        // Update hidden input
                        updateHiddenInput();
                    }
                    
                    // Clear the search field
                    setTimeout(function() {
                        $("#productSearch").val("");
                    }, 0);
                    
                    return false;
                }
            });
            
            // Remove product when X is clicked
            $(document).on("click", ".remove-product", function() {
                var productId = $(this).parent().data("id");
                console.log("Removing product with ID: " + productId);
                
                // Remove from array
                selectedProducts = selectedProducts.filter(p => p.id !== productId);
                
                // Remove from visual list
                $(this).parent().remove();
                
                // Update hidden input
                updateHiddenInput();
                console.log("Updated selectedProducts array:", selectedProducts);
                console.log("Hidden input value after removal:", $("#productSelect").val());
            });
            
            // Update the hidden input with selected product IDs
            function updateHiddenInput() {
                var ids = selectedProducts.map(p => p.id);
                $("#productSelect").val(ids.join(','));
            }
            
            // If editing, pre-load selected items
            const initialSelectedProducts = @Html.Raw(Json.Serialize(ViewBag.SelectedProductList ?? new List<object>()));
            initialSelectedProducts.forEach(function (item) {
                if (!selectedProducts.some(p => p.id === item.id)) {
                    // Add to selected products array
                    selectedProducts.push({
                        id: item.id,
                        text: item.text
                    });
                    
                    // Add to visual list
                    $("#selectedProductsList").append(
                        `<li class="list-group-item product-item" data-id="${item.id}">
                            ${item.text}
                            <span class="remove-product">&times;</span>
                        </li>`
                    );
                }
            });
            
            // Initialize hidden input
            updateHiddenInput();
        });
    </script>
}
