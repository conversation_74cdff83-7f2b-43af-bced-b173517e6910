using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace POS.Core.Models
{
    public class MorrisonOrder : BaseEntity
    {
        public int Id { get; set; }

        public string GeneratedId { get; set; }

        public List<MorrisonsOrderItem> OrderItems { get; set; } = new List<MorrisonsOrderItem>();

        public DateTime CreatedDate  { get; set; }

        public DateTime ExpectedDeliveryDate { get; set; }

        public Store Store { get; set; }

        public int StoreId { get; set; }

        public MorrisonOrderStatus MorrisonOrderStatus { get; set; }

       
      
    }


    public enum MorrisonOrderStatus
    {
        Created,
        Confirmed,
        Delivered
    }
   

}
