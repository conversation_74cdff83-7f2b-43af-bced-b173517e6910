﻿using POS.BackOffice.ViewModels;
using Microsoft.AspNetCore.Mvc;
using POS.Core.Models;
using Microsoft.AspNetCore.Authorization;

namespace BackOffice.Controllers
{
    [Authorize(Roles = "BackOffice,Admin,Accountant")]
    public class CategoryReportController : Controller
    {

        private readonly Data _context;

        public CategoryReportController(Data context)
        {
            _context = context;
        }

        public IActionResult Index()
        {
            CategoryReportViewModel viewModel = new CategoryReportViewModel();
            viewModel.SubCatagories = _context.SubCatagories.ToList();


            return View(viewModel);
        }

        [HttpPost]
        public IActionResult Submit(CategoryReportViewModel viewModel)
        {
            return null;
        }
    }
}
