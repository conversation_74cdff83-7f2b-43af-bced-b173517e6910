﻿using System;
using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace POS.Core.Migrations
{
    /// <inheritdoc />
    public partial class migration9 : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropColumn(
                name: "QuantityLimit",
                table: "SubCatagories");

            migrationBuilder.CreateTable(
                name: "StoreSubCategoryLimits",
                columns: table => new
                {
                    Id = table.Column<int>(type: "int", nullable: false)
                        .Annotation("SqlServer:Identity", "1, 1"),
                    StoreId = table.Column<int>(type: "int", nullable: false),
                    SubCategoryId = table.Column<int>(type: "int", nullable: false),
                    QuantityLimit = table.Column<int>(type: "int", nullable: false),
                    IsDeleted = table.Column<bool>(type: "bit", nullable: false),
                    LastModified = table.Column<DateTime>(type: "datetime2", nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_StoreSubCategoryLimits", x => x.Id);
                    table.ForeignKey(
                        name: "FK_StoreSubCategoryLimits_Stores_StoreId",
                        column: x => x.StoreId,
                        principalTable: "Stores",
                        principalColumn: "StoreId",
                        onDelete: ReferentialAction.Cascade);
                    table.ForeignKey(
                        name: "FK_StoreSubCategoryLimits_SubCatagories_SubCategoryId",
                        column: x => x.SubCategoryId,
                        principalTable: "SubCatagories",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Cascade);
                });

            migrationBuilder.CreateIndex(
                name: "IX_StoreSubCategoryLimits_StoreId",
                table: "StoreSubCategoryLimits",
                column: "StoreId");

            migrationBuilder.CreateIndex(
                name: "IX_StoreSubCategoryLimits_SubCategoryId",
                table: "StoreSubCategoryLimits",
                column: "SubCategoryId");
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropTable(
                name: "StoreSubCategoryLimits");

            migrationBuilder.AddColumn<int>(
                name: "QuantityLimit",
                table: "SubCatagories",
                type: "int",
                nullable: true);
        }
    }
}
