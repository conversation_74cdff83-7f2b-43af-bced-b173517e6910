@model POS.Core.Models.NonBrandPromotion

@{
    ViewData["Title"] = "Edit Non-Brand Promotion";
}

<h2>Edit Non-Brand Promotion</h2>

<form asp-action="Edit">
    <input type="hidden" asp-for="Id" />
    <input type="hidden" asp-for="CreatedById" />
    <input type="hidden" asp-for="StoreId" />

    <div class="row">
        <div class="col-md-6">
            <div class="form-group">
                <label asp-for="Name" class="control-label"></label>
                <input asp-for="Name" class="form-control" />
                <span asp-validation-for="Name" class="text-danger"></span>
            </div>

            <div class="form-group">
                <label asp-for="Type" class="control-label"></label>
                <select asp-for="Type" asp-items="Html.GetEnumSelectList<POS.Core.Models.PromotionType>()" class="form-control">
                    <option value="">Select Type</option>
                </select>
                <span asp-validation-for="Type" class="text-danger"></span>
            </div>

            <div class="form-group">
                <label asp-for="StartTime" class="control-label"></label>
                <input asp-for="StartTime" class="form-control" type="datetime-local" />
                <span asp-validation-for="StartTime" class="text-danger"></span>
            </div>

            <div class="form-group">
                <label asp-for="EndTime" class="control-label"></label>
                <input asp-for="EndTime" class="form-control" type="datetime-local" />
                <span asp-validation-for="EndTime" class="text-danger"></span>
            </div>

            <div class="form-group">
                <label asp-for="PromotionPrice" class="control-label"></label>
                <input asp-for="PromotionPrice" class="form-control" />
                <span asp-validation-for="PromotionPrice" class="text-danger"></span>
            </div>

            <div class="form-group">
                <label asp-for="Quantity" class="control-label"></label>
                <input asp-for="Quantity" class="form-control" />
                <span asp-validation-for="Quantity" class="text-danger"></span>
            </div>

            <div class="form-group">
                <label for="productPlus">Product PLUs</label>
                <textarea id="productPlus" name="productPlus" class="form-control" rows="4" 
                          placeholder="Enter PLUs separated by commas, spaces, or new lines"></textarea>
                <small class="form-text text-muted">Enter multiple PLUs separated by commas, spaces, or new lines</small>
            </div>

            <!-- Hidden field to store the original select for compatibility -->
            <div class="form-group d-none">
                <select id="selectedProducts" name="selectedProducts" class="form-control" multiple="multiple">
                    @foreach (var product in ViewBag.Products)
                    {
                        <option value="@product.Value">@product.Text</option>
                    }
                </select>
            </div>

            <div class="form-group mt-3">
                <input type="submit" value="Save" class="btn btn-primary" />
                <a asp-action="Index" class="btn btn-secondary">Back to List</a>
            </div>
        </div>
    </div>
</form>

@section Scripts {
    @{await Html.RenderPartialAsync("_ValidationScriptsPartial");}
    <script src="https://cdn.jsdelivr.net/npm/select2@4.1.0-rc.0/dist/js/select2.min.js"></script>
    <link href="https://cdn.jsdelivr.net/npm/select2@4.1.0-rc.0/dist/css/select2.min.css" rel="stylesheet" />
    <script>
        $(document).ready(function() {
            // Initialize the hidden select2 for compatibility
            try {
                $('#selectedProducts').select2({
                    placeholder: "Select products",
                    width: '100%'
                });
            } catch (e) {
                console.error("Error initializing select2:", e);
                // Continue without select2 if it fails
            }
            
            // PLU lookup and processing
            var pluToProductMap = {};
            var productIdToPluMap = {};
            
            // Populate the PLU to product ID mapping
            @foreach (var item in ViewBag.Products)
            {
                <text>
                var pluText = "@item.Text";
                var plu = pluText.split(" - ")[0].trim();
                pluToProductMap[plu] = "@item.Value";
                productIdToPluMap["@item.Value"] = plu;
                </text>
            }
            
            // Set initial selected values
            var selectedProducts = @Html.Raw(Json.Serialize(ViewBag.SelectedProductList));
            
            // Use a Set to ensure unique values
            var uniqueIds = new Set();
            selectedProducts.forEach(function(item) {
                uniqueIds.add(item.id.toString());
            });
            
            var selectedValues = Array.from(uniqueIds);
            
            // Set the selected values in the hidden select
            $('#selectedProducts').val(selectedValues);
            
            // Populate the textarea with the PLUs of selected products
            // Use a Set to ensure unique PLUs
            var uniquePlus = new Set();
            selectedValues.forEach(function(id) {
                var plu = productIdToPluMap[id];
                if (plu) {
                    uniquePlus.add(plu);
                }
            });
            
            var selectedPlus = Array.from(uniquePlus).join(', ');
            $('#productPlus').val(selectedPlus);
            
            // Process form submission
            $('form').on('submit', function(e) {
                e.preventDefault();
                
                // Get PLUs from textarea
                var pluText = $('#productPlus').val();
                var plus = pluText.split(/[\s,]+/).filter(function(plu) {
                    return plu.trim() !== '';
                });
                
                // Use a Set to ensure unique PLUs
                var uniquePlus = new Set(plus);
                plus = Array.from(uniquePlus);
                
                // Clear previous selections
                $('#selectedProducts').val(null);
                
                // Add products based on PLUs
                var foundProducts = [];
                var notFoundPlus = [];
                
                plus.forEach(function(plu) {
                    if (pluToProductMap[plu]) {
                        foundProducts.push(pluToProductMap[plu]);
                    } else {
                        notFoundPlus.push(plu);
                    }
                });
                
                // Set the selected products
                $('#selectedProducts').val(foundProducts);
                
                // Show warning for PLUs not found
                if (notFoundPlus.length > 0) {
                    if (!confirm('The following PLUs were not found: ' + notFoundPlus.join(', ') + '\n\nDo you want to continue anyway?')) {
                        return;
                    }
                }
                
                // Submit the form
                this.submit();
            });
        });
    </script>
}
