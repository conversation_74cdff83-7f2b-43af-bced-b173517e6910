﻿@model BackOffice.ViewModels.UserViewModel

<h2>Edit User</h2>

<form asp-action="Edit" method="post">
    <input type="hidden" asp-for="Id" />

    <div>
        <label asp-for="UserType">User Type:</label>
        <input asp-for="UserType" readonly />
    </div>

    <div>
        <label asp-for="UserId">User ID:</label>
        <input asp-for="UserId" />
    </div>

    <div>
        <label asp-for="Name">Name:</label>
        <input asp-for="Name" />
    </div>

    <div>
        <label asp-for="Password">Password:</label>
        <input asp-for="Password" type="password" />
    </div>

    <div>
        <label>IP Addresses (comma-separated):</label>
        <input asp-for="IpAddresses" />
    </div>

    <button type="submit">Save</button>
</form>