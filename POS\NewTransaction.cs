﻿using POS.Models;
using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Windows.Forms;

namespace POS
{
    public partial class NewTransaction : Form
    {
        public Main _ParentForm { get; set; }
        private Transaction _currentTransaction = new Transaction();


        public NewTransaction(Main parentForm)
        {
            InitializeComponent();
            _ParentForm = parentForm;
        }

        private void NewTransaction_Load(object sender, EventArgs e)
        {

        }

        private void AddProduct(int productId, string productName, decimal price, int quantity)
        {
            var existingItem = _currentTransaction.Items.FirstOrDefault(item => item.ProductId == productId);
            if (existingItem != null)
            {
                existingItem.Quantity += quantity;
            }
            else
            {
                _currentTransaction.Items.Add(new TransactionItem
                {
                    ProductId = productId,
                    ProductName = productName,
                    Price = price,
                    Quantity = quantity
                });
            }
            UpdateTransactionSummary();
        }

        private void UpdateTransactionSummary()
        {
            dataGridView1.DataSource = null;
            dataGridView1.DataSource = _currentTransaction.Items;
            textBox1.Text = $"{_currentTransaction.GrandTotal:C}";
        }

        private void ResetTransaction()
        {
            _currentTransaction = new Transaction();
            UpdateTransactionSummary();
        }


        //plu enter
        private void button18_Click(object sender, EventArgs e)
        {
            var plu = textBox2.Text;


            using (var context = new POS.Core.Models.Data())
            {
                var product = context.Products.FirstOrDefault(p => p.PLU == plu);

                if (product != null)
                {
                    AddProduct(product.Id, product.Name, product.Price, 1);
                }
            }
        }
    }
}
