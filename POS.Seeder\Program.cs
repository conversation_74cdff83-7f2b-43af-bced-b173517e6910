﻿using POS.Core.Models;
using POS.Core;
using Microsoft.EntityFrameworkCore;

Console.WriteLine("Starting database seeding process...");

try
{
    using (var context = new Data())
    {
        Console.WriteLine("Database connection established.");
        DatabaseSeeder.SeedData(context);
        Console.WriteLine("Database seeding completed successfully!");
    }
}
catch (Exception ex)
{
    Console.WriteLine($"Error during database seeding: {ex.Message}");
    Console.WriteLine(ex.StackTrace);
}

Console.WriteLine("Press any key to exit...");
Console.ReadKey();
