﻿using NetPosWrapper;
using System.ComponentModel;
using System.Diagnostics;
using System.Reflection.Metadata.Ecma335;
using POS.Core;
using POS.Core.Models;
using Product = POS.Core.Models.Product;
using System.Configuration;
using NLog;
using NLog.Config;
using System.Text.RegularExpressions;
using System.Globalization;

try
{
    //while (true)
    //{
    //    try
    //    {
    //        //temp
    //        using (Data context = new Data())
    //        {
    //            var x =  context.Products.Where(p => p.Division.SubCatagory.Name == "Mothers Day Flowers").Count();
    //            int weqweqwe = 0;
    //            var y = context.Products.Where(p => p.Division.Name == "Mixed Mothers Day Flowers").ToList();
    //            int qweqwe = 0;
    //            var z = context.Products.Where(p => p.Division.SubCatagory.Name == "Cut Flowers").Count();
    //            int asdas = 0;
    //            var zz = context.Products.Where(p => p.Division.Name == "Cut Flowers").Count();
    //            var sdfsdf = 0;
    //        }

    //    }
    //    catch (Exception ex) { }
    //}

    // Retrieve configuration values
    string? MonoSslPath = ConfigurationManager.AppSettings["MonoSslPath"]
        ?? throw new Exception("MonoSslPath is null");
    string? ClientInPath = ConfigurationManager.AppSettings["ClientInPath"]
        ?? throw new Exception("ClientInPath is null");
    string? BrandName = ConfigurationManager.AppSettings["BrandName"]
        ?? throw new Exception("BrandName is null");


    // Dictionary 1: Departments (Department number -> Department name)
    Dictionary<int, string> departments = new Dictionary<int, string>
{
    { 1, "RETAIL GROCERY" },
    { 2, "CHILLED" },
    { 3, "CATERING GROCERY" },
    { 4, "FROZEN FOOD" },
    { 5, "CONFECTIONERY" },
    { 6, "CIGARETTES" },
    { 7, "WINES SPIRITS BEERS" },
    { 8, "MEAT" },
    { 9, "FRUIT & VEG" },
    { 10, "NON-FOOD" }
};

    // Dictionary 2: Categories (Code -> Category name)
    Dictionary<int, string> categories = new Dictionary<int, string>
{
    // DEPARTMENT 01: RETAIL GROCERY
    { 1, "RETAIL BEVERAGES" },
    { 2, "RETAIL SUGAR/SYRUP" },
    { 3, "RETAIL BISCUITS" },
    { 4, "GIFT HAMPERS" },
    { 5, "RETAIL PRESERVES & SPREADS" },
    { 6, "SOFT DRINKS - ON THE GO" },
    { 7, "SOFT DRINKS - TAKE HOME" },
    { 8, "RETAIL AMB/FRUIT/DESSERT/MIL" },
    { 9, "RETAIL CANNED VEGETABLES" },
    { 10, "MAKRO SALES DUMP CODE - PRD" },
    { 11, "RETAIL AMB/MEAT/FISH/RDY MEA" },
    { 12, "CRISPS/SNACKS" },
    { 13, "BABY PRODUCTS" },
    { 14, "RETAIL SOUPS" },
    { 15, "RETAIL BREAKFAST CEREALS" },
    { 16, "RETAIL DRY VEG CEREAL PASTA" },
    { 17, "RETAIL BAKING INGREDIENTS" },
    { 18, "RETAIL COOKING SAUCE/PASTE" },
    { 19, "RETAIL PICKLE/SCE/CONDIMENT" },
    { 20, "RETAIL SAVOURY INGREDIENTS" },
    { 23, "ETHNIC" },
    { 24, "RETAIL LAUNDRY/WUL/CLEANING" },
    { 26, "PAPER PRODUCTS" },
    { 28, "HEALTH & BEAUTY" },
    { 29, "PET PRODUCTS" },

    // DEPARTMENT 02: CHILLED
    { 30, "BUTTERS/SPREADS & FATS" },
    { 31, "CHEESE" },
    { 32, "MILKS, CREAMS & JUICES" },
    { 34, "COOKED DELI MEATS" },
    { 35, "BREAD AND CAKES" },
    { 36, "PASTRY/BREAD/PASTA" },
    { 37, "YOGHURTS & DESSERTS" },

    // DEPARTMENT 03: CATERING GROCERY
    { 38, "CATER BEVERAGES" },
    { 39, "CATERING SUGAR/SYRUP" },
    { 40, "CATER SOFT DRINKS/JUICE" },
    { 41, "CATER CANNED VEGETABLES/FISH" },
    { 42, "CATER SOUPS" },
    { 43, "CATER LAUNDRY" },
    { 44, "CATER AMBIENT FRUIT/DESSERTS" },
    { 45, "CATER MEAT/READY MEALS" },
    { 46, "CATER PIE FILLING/PRESERVES" },
    { 47, "CATER BISCUITS" },
    { 48, "CATER BREAKFAST CEREALS" },
    { 49, "CATER BAKING INGREDIENTS" },
    { 50, "CATER COOKING SAUCE/PASTE" },
    { 51, "CATER PICKLE/SAUCE/CONDIMENT" },
    { 52, "CATER SAVOURY INGREDIENTS" },
    { 53, "CATER HOUSEHOLD CLEANERS" },
    { 54, "CATER DRY VEG/CEREALS/PASTA" },
    { 55, "CATER DESSERT MILK WHITENERS" },

    // DEPARTMENT 04: FROZEN FOOD
    { 56, "FROZEN VEGETABLES" },
    { 57, "FROZEN MEAT/POULTRY" },
    { 58, "FROZEN READY MEALS" },
    { 59, "FROZEN PASTRY PRODUCTS" },
    { 60, "FROZEN FISH" },
    { 61, "ICE CREAM" },
    { 62, "FROZEN DESSERTS" },

    // DEPARTMENT 05: CONFECTIONERY
    { 63, "CHOCOLATE ON THE GO" },
    { 64, "CHOCOLATE TAKE HOME" },
    { 65, "MINTS/GUMS & MEDICATED" },
    { 66, "SPECIAL OCCASION" },
    { 67, "CATERING" },
    { 68, "SUGAR ON THE GO" },
    { 69, "SUGAR TAKE HOME" },
    { 71, "SEASONAL" },

    // DEPARTMENT 06: CIGARETTES
    { 72, "CIGARETTES" },
    { 73, "CIGARS/OTHER TOBACCOS" },
    { 74, "TOBACCO REQUISITES" },

    // DEPARTMENT 07: WINES SPIRITS BEERS
    { 75, "WINE" },
    { 76, "FORTIFIED/VERMOUTHS/APERITIF" },
    { 77, "ALCOHOLIC RTDS" },
    { 78, "SPIRITS" },
    { 79, "BEERS" },
    { 80, "CIDER" },

    // DEPARTMENT 08: MEAT
    { 81, "BEEF" },
    { 82, "PORK" },
    { 83, "BACON" },
    { 84, "LAMB" },
    { 85, "POULTRY" },
    { 86, "MEAT/GAME/FISH/EGGS" },
    { 87, "FRESH FISH" },

    // DEPARTMENT 09: FRUIT & VEG
    { 88, "FRESH FRUIT" },
    { 89, "FRESH VEGETABLES" },

    // DEPARTMENT 10: NON-FOOD
    { 90, "FOOD AND DRINK DISPOSABLES" },
    { 91, "COOKING AND KITCHEN EQUIPME" },
    { 92, "TABLE TOP & BAR PRODUCTS" },
    { 93, "GENERAL MAINTENANCE & WORK" },
    { 94, "WORK PLACE & JANITORIAL" },
    { 95, "HYGIENE SYSTEMS" },
    { 96, "RETAILER RESALE" },
    { 97, "OFFICE SUPPLIES & STATIONERY" },
    { 98, "ACCOMODATIONS & FURNISHINGS" },
    { 99, "OUTDOOR, LEISURE & SEASONAL" }
};



    // Execute external process once
    using (Process process = new Process())
    {
        process.StartInfo.FileName = MonoSslPath;
        process.Start();
        process.WaitForExit();
        int exitCode = process.ExitCode;
        Console.WriteLine($"The process exited with code: {exitCode}");
    }

    // Load product files and parse products
    string[] extensions = { "*.erf", "*.eup" };
    string[] allFiles = extensions.SelectMany(ext => Directory.GetFiles(ClientInPath, ext))
                                  .Select(Path.GetFileName)
                                  .ToArray();
    List<NetPosWrapper.Product> products = new List<NetPosWrapper.Product>();
    foreach (var file in allFiles)
    {
        products.AddRange(ParseProducts.Parse(Path.Combine(ClientInPath, file)));
    }

    using (var context = new POS.Core.Models.Data())
    {
        // Preload common data that will be reused



        var brand = context.Brands.First(b => b.Name == BrandName);
        var storesForBrand = context.Stores.Where(s => s.BrandId == brand.Id).ToList();

        // Cache existing sub-categories and divisions in dictionaries keyed by name.
        // Using navigation properties in new divisions prevents the need to call SaveChanges immediately.
        var subCategories = context.SubCatagories.ToDictionary(s => s.Name, s => s);
        var divisions = context.Divisions.ToDictionary(d => d.Name, d => d);

        // Preload existing products keyed by barcode for quick lookup.
        var existingProducts = context.Products.ToDictionary(p => p.Barcode, p => p);

        // Process each product from the parsed list
        foreach (var product in products)
        {
            try
            {
                // Map department code to name using your static dictionary
                if (!int.TryParse(product.Department, out int departmentCode) ||
                    !departments.TryGetValue(departmentCode, out string departmentName))
                    continue;

                // Map category code to name using your static dictionary
                if (!int.TryParse(product.Category, out int categoryCode) ||
                    !categories.TryGetValue(categoryCode, out string categoryName))
                    continue;

                // Ensure a sub-category exists for the department
                if (!subCategories.TryGetValue(departmentName, out SubCatagory subCategory))
                {
                    subCategory = new SubCatagory { Name = departmentName, StockType = StockType.Stock, Catagory = Catagory.ShopSales };
                    context.SubCatagories.Add(subCategory);
                    subCategories[departmentName] = subCategory;
                    context.SaveChanges();
                }

                // Ensure a division exists for the category.
                // Instead of setting the foreign key manually, assign the navigation property.
                if (!divisions.TryGetValue(categoryName, out Division division))
                {
                    division = new Division { Name = categoryName, SubCatagory = subCategory };
                    context.Divisions.Add(division);
                    divisions[categoryName] = division;
                    context.SaveChanges();

                }

                // Check if product already exists (cached lookup)
                if (!existingProducts.TryGetValue(product.Barcode, out Product existingProduct))
                {
                    if (product.RecordType == RecordType.NewProduct)
                    {
                        Vat vat = null;

                        if (product.VatCode == "01")
                        {
                            vat = context.Vats.First(v => v.Code == "Code2");
                        }
                        else if (product.VatCode == "02")
                        {
                            vat = context.Vats.First(v => v.Code == "Code0");
                        }
                        else if (product.VatCode == "03")
                        {
                            vat = context.Vats.First(v => v.Code == "Code1");

                        }




                        // Create and add new product
                        var newProduct = new Product
                        {
                            PLU = product.ProductCode,
                            Barcode = product.Barcode,
                            PurchasePackSize = int.Parse(product.CaseQuantity),
                            PurchasePrice = decimal.TryParse(product.PurchasePrice, out decimal purchasePrice)
                                ? purchasePrice : 0,
                            SellingPrice = decimal.TryParse(product.RetailPrice, out decimal retailPrice)
                                ? retailPrice : 0,
                            Vat = vat,
                            Description = product.Description,
                            Name = product.ShortProductName,
                            DivisionId = division.Id, // if preferred, you can also set Division = division
                            Brand = brand,
                        };
                        context.Products.Add(newProduct);
                        existingProducts[newProduct.Barcode] = newProduct; // update cache

                        // Add a product-store record for each store associated with the brand
                        foreach (var store in storesForBrand)
                        {
                            context.ProductStores.Add(new ProductStore
                            {
                                InventoryCount = 0,
                                Product = newProduct,
                                Store = store,
                            });
                        }
                    }
                }
                else // Update existing product if applicable
                {
                    if (product.RecordType == RecordType.PriceChange)
                    {
                        existingProduct.PurchasePrice = decimal.TryParse(product.PurchasePrice, out decimal purchasePrice)
                            ? purchasePrice : 0;
                        existingProduct.SellingPrice = decimal.TryParse(product.RetailPrice, out decimal retailPrice)
                            ? retailPrice : 0;

                        // Reset store-specific prices for each associated store
                        var productStores = context.ProductStores
                            .Where(ps => ps.ProductId == existingProduct.Id && ps.StoreSpecificPrice != null)
                            .ToList();
                        foreach (var productStore in productStores)
                        {
                            productStore.StoreSpecificPrice = null;
                        }
                    }
                    else if (product.RecordType == RecordType.Promotions)
                    {
                        // TODO: Implement promotion logic here if needed.
                    }
                }
            }
            catch (Exception ex)
            {
                // Log the error (using NLog or another logging framework) with enough context to diagnose the issue.
                // Example: logger.Error(ex, $"Error processing product with barcode: {product.Barcode}");
            }
        }

        // Commit all changes in one transaction rather than after every update.
        context.SaveChanges();
    }
}
catch (Exception ex)
{
    Console.WriteLine(ex.Message);
    return;
}
