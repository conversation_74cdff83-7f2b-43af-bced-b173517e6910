﻿using RefactoredPrinter.Interfaces;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace RefactoredPrinter
{
    public class EpsonEmitter : IPrinterEmitter
    {
        public byte[] CenterAlign()
        {
            throw new NotImplementedException();
        }

        public byte[] PrintLine(string text)
        {
            throw new NotImplementedException();
        }
    }
}
