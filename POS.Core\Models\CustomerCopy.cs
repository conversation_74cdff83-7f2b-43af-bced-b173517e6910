﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace POS.Core.Models
{
    public class CustomerCopy
    {
        public int Id { get; set; }
        public DateTime TransactionDateTime { get; set; }

        public int SaleId { get; set; }

        /// <summary>
        /// pos machine id
        /// </summary>
        public string TerminalId { get; set; }
        public string EftReference { get; set; }
        public string EntryMode { get; set; }
        public string CardName { get; set; }
        public string SequenceNumber { get; set; }
        public string AID { get; set; }
        public string ApplicationCryptogram { get; set; }
        public string PANMasked { get; set; } // Masked card number (e.g., XXXXXX******XX)
        public string CardType { get; set; }
        public string ExpiryDateMasked { get; set; } // e.g., **/**
        public string Currency { get; set; } // e.g., G3P
        public decimal SaleAmount { get; set; }
        public string Response { get; set; }
        public string AuthCode { get; set; }
        public string VerificationMethod { get; set; } // e.g., No CVM
    }
}
