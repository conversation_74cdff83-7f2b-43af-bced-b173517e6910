﻿@model BackOffice.ViewModels.UserCreateViewModel

@{
    ViewData["Title"] = "Create User";
}

<h2>Create User</h2>

<form asp-action="Create" method="post">
    <div class="form-group">
        <label asp-for="Name"></label>
        <input asp-for="Name" class="form-control" required />
    </div>

    <div class="form-group">
        <label asp-for="Password"></label>
        <input asp-for="Password" type="password" class="form-control" required />
    </div>

    <div class="form-group">
        <label asp-for="Role">Role</label>
        <select asp-for="Role" class="form-control" required id="roleDropdown">
            <option value="">Select Role</option>
            <option value="Admin">Admin</option>
            <option value="Accountant">Accountant</option>
            <option value="BackOffice">BackOffice</option>
        </select>
    </div>

    <div id="storeSection" class="d-none">
        <h4>Select Store (For BackOffice Only)</h4>
        <select asp-for="StoreId" class="form-control">
            <option value="">Select Store</option>
            @foreach (var store in ViewBag.Stores)
            {
                <option value="@store.Value">@store.Text</option>
            }
        </select>
    </div>

    <div id="ipAddressSection" class="d-none">
        <h4>IP Addresses (For Accountant & BackOffice)</h4>
        <div id="ipInputs">
            <div class="form-group">
                <input type="text" name="IpAddresses" class="form-control ip-field" placeholder="Enter IP Address">
            </div>
        </div>
        <button type="button" id="addIp" class="btn btn-secondary">Add IP Address</button>
    </div>

    <button type="submit" class="btn btn-primary mt-3">Create User</button>
</form>

@section Scripts {
    <script>
        document.getElementById("roleDropdown").addEventListener("change", function () {
            let ipSection = document.getElementById("ipAddressSection");
            let storeSection = document.getElementById("storeSection");

            if (this.value === "Accountant" || this.value === "BackOffice") {
                ipSection.classList.remove("d-none");
            } else {
                ipSection.classList.add("d-none");
            }

            if (this.value === "BackOffice") {
                storeSection.classList.remove("d-none");
            } else {
                storeSection.classList.add("d-none");
            }
        });

        document.getElementById("addIp").addEventListener("click", function () {
            let newInput = document.createElement("div");
            newInput.classList.add("form-group");
            newInput.innerHTML = '<input type="text" name="IpAddresses" class="form-control ip-field" placeholder="Enter IP Address">';
            document.getElementById("ipInputs").appendChild(newInput);
        });
    </script>
}
