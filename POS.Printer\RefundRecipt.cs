﻿using ESCPOS_NET.Emitters;
using Integral.Library.GuardianClient;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace POS.Printer
{
    public class RefundRecipt
    {
        public static void PrintRefundReceipt(TransactionInfo transInfo, TillInformation tillInfo, string printerName)
        {
            var emitter = new EPSON();
            var commands = new List<byte>();

            // 1. Initialize Printer
            commands.AddRange(emitter.Initialize());
            commands.AddRange(emitter.CenterAlign());

            // 2. Print Header
            commands.AddRange(emitter.PrintLine(tillInfo.MerchantName));
            commands.AddRange(emitter.PrintLine(tillInfo.Address1));
            if (!string.IsNullOrEmpty(tillInfo.Address2))
                commands.AddRange(emitter.PrintLine(tillInfo.Address2));
            commands.AddRange(emitter.PrintLine(""));

            // 3. Print "REFUND" Banner
            commands.AddRange(emitter.PrintLine("=== REFUND RECEIPT ==="));
            commands.AddRange(emitter.PrintLine(""));

            // 4. Transaction Details (Left/Right aligned)
            commands.AddRange(emitter.PrintLine(
                Helper.CreateMixedAlignmentLine(
                    $"Transaction #{transInfo.TransactionRefNo}",
                    DateTime.Now.ToString("dd/MM/yy HH:mm"),
                    40
                )
            ));

            commands.AddRange(emitter.PrintLine(
                Helper.CreateMixedAlignmentLine(
                    "Auth Code:",
                    transInfo.AuthorisationCode,
                    40
                )
            ));

            // 5. Card Details
            commands.AddRange(emitter.PrintLine(""));
            commands.AddRange(emitter.PrintLine(
                Helper.CreateMixedAlignmentLine(
                    "Card:",
                    $"{transInfo.SchemeName} ****{transInfo.PanLast4Digits}",
                    40
                )
            ));

            // 6. Refund Amount
            commands.AddRange(emitter.PrintLine("----------------------------------------"));
            commands.AddRange(emitter.PrintLine(
                Helper.CreateMixedAlignmentLine(
                    "REFUND AMOUNT:",
                    $"€{(transInfo.CashbackAmount / 100m):0.00}", // Assuming amount is in cents
                    40
                )
            ));
            commands.AddRange(emitter.PrintLine("----------------------------------------"));

            // 7. Footer
            commands.AddRange(emitter.PrintLine(""));
            commands.AddRange(emitter.PrintLine("Customer copy - please retain"));
            commands.AddRange(emitter.PrintLine(""));

            // 8. Paper Cut
            // Add cutter command (full cut)
            commands.Add(0x1D); // GS
            commands.Add(0x56); // V
            commands.Add(0x00); // Full cut

            // Send to printer
            RawPrinterHelper.SendBytesToPrinter(printerName, commands.ToArray());
        }
    }
}
