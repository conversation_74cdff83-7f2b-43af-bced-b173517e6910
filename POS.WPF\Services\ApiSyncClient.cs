using System;
using System.Collections.Generic;
using System.Net.Http;
using System.Net.Http.Json;
using System.Threading.Tasks;
using Microsoft.EntityFrameworkCore;
using POS.Core.Models;

namespace POS.WPF.Services
{
    public class ApiSyncClient
    {
        private readonly HttpClient _httpClient;
        private readonly string _baseUrl;

        public ApiSyncClient()
        {
            _httpClient = new HttpClient();
            _baseUrl = ConfigurationHelper.APIUrl.TrimEnd('/');
        }

        public async Task<List<T>> GetUpdatedEntities<T>(string entityName, DateTime lastSync) where T : class
        {
            // Special handling for Void type to avoid conflict with System.Void
            string endpoint = entityName;
            if (typeof(T) == typeof(POS.Core.Models.Void))
            {
                endpoint = "Voids"; // Ensure we're using the plural form for the endpoint
            }

            // Make sure we're using the correct URL format
            var url = $"{_baseUrl}/sync/entities/{endpoint}?lastSync={lastSync:o}";
            Console.WriteLine($"Requesting: {url}"); // Debug logging
            
            try
            {
                var response = await _httpClient.GetFromJsonAsync<List<T>>(url);
                return response;
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Error fetching entities: {ex.Message}");
                throw;
            }
        }
        public async Task<int> CreateEntityAsync(string entityType, object entity)
        {
            try
            {
                var url = $"{_baseUrl}/sync/entities/{entityType}";
                Console.WriteLine($"Sending POST request to: {url}");
                
                // Log the request content
                var jsonOptions = new System.Text.Json.JsonSerializerOptions
                {
                    WriteIndented = true,
                    ReferenceHandler = System.Text.Json.Serialization.ReferenceHandler.Preserve
                };
                var requestJson = System.Text.Json.JsonSerializer.Serialize(entity, jsonOptions);
                Console.WriteLine($"Request content: {requestJson}");
                
                var response = await _httpClient.PostAsJsonAsync(url, entity);
                
                if (!response.IsSuccessStatusCode)
                {
                    var errorContent = await response.Content.ReadAsStringAsync();
                    Console.WriteLine($"API error response: {errorContent}");
                    throw new Exception($"API returned status code {response.StatusCode}: {errorContent}");
                }
                
                var result = await response.Content.ReadFromJsonAsync<CreateEntityResponse>();
                return result.Id;
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Exception in CreateEntityAsync: {ex.Message}");
                throw;
            }
        }

        public async Task UpdateEntityAsync<T>(string entityType, int id, T entity) where T : class
        {
            var url = $"{_baseUrl}/api/sync/entities/{entityType}/{id}";
            var response = await _httpClient.PutAsJsonAsync(url, entity);
            
            if (!response.IsSuccessStatusCode)
                throw new Exception($"Failed to update entity: {await response.Content.ReadAsStringAsync()}");
        }

        public async Task DeleteEntityAsync(string entityType, int id)
        {
            var url = $"{_baseUrl}/api/sync/entities/{entityType}/{id}";
            var response = await _httpClient.DeleteAsync(url);
            
            if (!response.IsSuccessStatusCode)
                throw new Exception($"Failed to delete entity: {await response.Content.ReadAsStringAsync()}");
        }

        public class CreateEntityResponse
        {
            public int Id { get; set; }
        }
    }
}
