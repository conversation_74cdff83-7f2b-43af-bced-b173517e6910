using System.ComponentModel.DataAnnotations;

namespace POS.Core.Models
{
    public class Brand :BaseEntity
    {
        public int Id { get; set; }
        public string Name { get; set; }

        [Display(Name = "Brand Logo")]
        public string? BrandLogo { get; set; } // Stores the file path of the logo

        public ICollection<Product> Products { get; set; } = new List<Product>();

        public ICollection<Store> Stores  { get; set; } = new List<Store>();

       
      
    }

   

}
