﻿<Window x:Class="POS.WPF.Views.TransactionHistory"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
        xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
        xmlns:local="clr-namespace:POS.WPF.Views"
        mc:Ignorable="d"
        Title="Transaction History" Height="450" Width="800"
        WindowStartupLocation="CenterScreen"
        Background="#004e7c">

    <Grid Margin="10">
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
            <RowDefinition Height="3*"/>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="Auto"/>

        </Grid.RowDefinitions>

        <!-- Sales List -->
        <DataGrid SelectionChanged="DgSales_SelectionChanged" x:Name="dgSales" Grid.Row="2" Margin="5" 
                 AutoGenerateColumns="False" IsReadOnly="True"
                 Background="#0a688d" Foreground="White"
                 HeadersVisibility="Column" FontSize="16"
                 BorderBrush="Transparent"
                 CanUserAddRows="False">

            <DataGrid.ColumnHeaderStyle>
                <Style TargetType="DataGridColumnHeader">
                    <Setter Property="Background" Value="Green"/>
                    <Setter Property="Foreground" Value="White"/>
                    <Setter Property="HorizontalContentAlignment" Value="Center"/>
                    <Setter Property="FontSize" Value="16"/>
                    <Setter Property="Padding" Value="10"/>
                </Style>
            </DataGrid.ColumnHeaderStyle>

            <DataGrid.CellStyle>
                <Style TargetType="DataGridCell">
                    <Setter Property="Background" Value="#0a688d"/>
                    <Setter Property="Foreground" Value="White"/>
                    <Setter Property="HorizontalContentAlignment" Value="Center"/>
                    <Setter Property="BorderBrush" Value="Transparent"/>
                </Style>
            </DataGrid.CellStyle>

            <DataGrid.Columns>
                <DataGridTextColumn Header="Sale ID" Binding="{Binding SaleId}" Width="*"/>
                <DataGridTextColumn Header="Date" Binding="{Binding Date, StringFormat=yyyy-MM-dd HH:mm}" Width="2*"/>
                <DataGridTextColumn Header="Total Value" Binding="{Binding TotalValue, StringFormat=£0.00}" Width="*"/>
                <DataGridTextColumn Header="Payment Method" Binding="{Binding PaymentMethod.Method}" Width="*"/>
            </DataGrid.Columns>
        </DataGrid>

        <!-- Sale Details -->
        <Grid Grid.Row="3" Margin="5">
            <Grid.RowDefinitions>
                <RowDefinition Height="Auto"/>
                <RowDefinition Height="*"/>
            </Grid.RowDefinitions>

            <TextBlock Text="Selected Sale Details" FontWeight="Bold" Margin="5"
                      Foreground="White" FontSize="18"/>

            <DataGrid x:Name="dgSaleDetails" Grid.Row="1" 
                     AutoGenerateColumns="False" IsReadOnly="True"
                     Background="#0a688d" Foreground="White"
                     HeadersVisibility="Column" FontSize="16"
                     BorderBrush="Transparent">

                <DataGrid.ColumnHeaderStyle>
                    <Style TargetType="DataGridColumnHeader">
                        <Setter Property="Background" Value="Green"/>
                        <Setter Property="Foreground" Value="White"/>
                        <Setter Property="HorizontalContentAlignment" Value="Center"/>
                        <Setter Property="FontSize" Value="16"/>
                    </Style>
                </DataGrid.ColumnHeaderStyle>

                <DataGrid.CellStyle>
                    <Style TargetType="DataGridCell">
                        <Setter Property="Background" Value="#0a688d"/>
                        <Setter Property="Foreground" Value="White"/>
                        <Setter Property="HorizontalContentAlignment" Value="Center"/>
                        <Setter Property="BorderBrush" Value="Transparent"/>
                    </Style>
                </DataGrid.CellStyle>

                <DataGrid.Columns>
                    <DataGridTextColumn Header="Product" Binding="{Binding ProductStore.Product.Name}" Width="3*"/>
                    <DataGridTextColumn Header="Quantity" Binding="{Binding Quantity}" Width="*"/>
                    <DataGridTextColumn Header="Unit Price" 
                                       Binding="{Binding ProductStore.Product.SellingPrice, StringFormat=£0.00}" 
                                       Width="*"/>
                    <DataGridTextColumn Header="Total" Binding="{Binding Total, StringFormat=£0.00}" Width="*"/>
                </DataGrid.Columns>
            </DataGrid>
        </Grid>

        <Button Grid.Row="4" Content="Print" Margin="5" Padding="20,5"
               Background="#004e7c" Foreground="White" FontSize="18"
               BorderBrush="Green" BorderThickness="1"
               Click="Button_Click"/>
        <Button Grid.Row="5" Content="Cancel"  Margin="5" Padding="20,5"
               Background="#004e7c" Foreground="White" FontSize="18"
               BorderBrush="Green" BorderThickness="1" Click="Button_Click_1"/>

    </Grid>
</Window>