﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Windows;
using System.Windows.Controls;
using System.Windows.Data;
using System.Windows.Documents;
using System.Windows.Input;
using System.Windows.Media;
using System.Windows.Media.Imaging;
using System.Windows.Shapes;
using WindowsInput;
using WindowsInput.Native;

namespace POS.WPF.Views
{
    /// <summary>
    /// Interaction logic for SpecialProductPrice.xaml
    /// </summary>
    public partial class SpecialProductPrice : Window
    {
        private InputSimulator _inputSimulator;

        public static decimal Amount { get; set; }

        public SpecialProductPrice(decimal amount)
        {
            InitializeComponent();
            _inputSimulator = new InputSimulator();
            Amount = 0;
           
            Helpers.WindowStyleHelper.SetWindowStyle(this);
        }

        private void Button_Click(object sender, RoutedEventArgs e)
        {
            Amount = decimal.Parse(txtOverridePrice.Text);
            this.Close();
        }

        private void NumericKeypad_KeyPressed(object sender, string key)
        {
            txtOverridePrice.Focus();

            switch (key)
            {
                case "Backspace":
                    _inputSimulator.Keyboard.KeyPress(VirtualKeyCode.BACK);
                    break;
                case "%":
                    _inputSimulator.Keyboard.KeyPress(VirtualKeyCode.SHIFT, VirtualKeyCode.VK_5);
                    break;
                case "00":
                    _inputSimulator.Keyboard.KeyPress(VirtualKeyCode.VK_0);
                    _inputSimulator.Keyboard.KeyPress(VirtualKeyCode.VK_0);
                    break;
                case ".":
                    _inputSimulator.Keyboard.KeyPress(VirtualKeyCode.DECIMAL);
                    break;
                default:
                    if (int.TryParse(key, out var number))
                    {
                        var keyCode = (VirtualKeyCode)Enum.Parse(
                            typeof(VirtualKeyCode),
                            $"VK_{number}"
                        );
                        _inputSimulator.Keyboard.KeyPress(keyCode);
                    }
                    break;
            }
        }

        private void Button_Click_1(object sender, RoutedEventArgs e)
        {
            this.Close();
        }
    }
}
