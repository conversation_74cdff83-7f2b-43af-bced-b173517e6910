﻿using RefactoredPrinter.Interfaces;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace RefactoredPrinter
{
    public class ReceiptDocument : IPrintDocument
    {
        private readonly PrintData _data;
        public ReceiptDocument(PrintData data) => _data = data;

        public List<byte> GetPrintCommands(IPrinterEmitter emitter)
        {
            var commands = new List<byte>();
            // Build receipt-specific commands
            return commands;
        }
    }
}
