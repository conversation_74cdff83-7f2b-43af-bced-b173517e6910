﻿@model POS.Core.Models.Brand

@{
    ViewData["Title"] = "Create Brand";
}

<h1>Create Brand</h1>

<form asp-action="Create" enctype="multipart/form-data">
    <div class="form-group">
        <label asp-for="Name" class="control-label"></label>
        <input asp-for="Name" class="form-control" />
    </div>
    <div class="form-group">
        <label asp-for="BrandLogo" class="control-label"></label>
        <input type="file" name="logoFile" class="form-control" accept=".png" />
    </div>
    <button type="submit" class="btn btn-primary">Create</button>
    <a asp-action="Index" class="btn btn-secondary">Back to List</a>
</form>
