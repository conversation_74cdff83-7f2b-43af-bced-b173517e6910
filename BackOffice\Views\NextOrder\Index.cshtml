﻿@model List<POS.Core.Models.NextOrderProduct>

@{
    ViewData["Title"] = "Next Order Products";
}

<h2>Next Order Products</h2>

@if (TempData["Success"] != null)
{
    <div class="alert alert-success">@TempData["Success"]</div>
}
@if (TempData["Error"] != null)
{
    <div class="alert alert-danger">@TempData["Error"]</div>
}

<!-- Upload CSV Form -->
<form asp-action="UploadCsv" method="post" enctype="multipart/form-data">
    <input type="file" name="file" accept=".csv" required />
    <button type="submit" class="btn btn-primary">Upload CSV</button>
</form>

<!-- Product List Table -->
<table class="table table-striped mt-3">
    <thead>
        <tr>
            <th>Barcode</th>
            <th>Quantity</th>
            <th>Actions</th>
        </tr>
    </thead>
    <tbody>
        @foreach (var product in Model)
        {
            <tr>
                <td>@product.Barcode</td>
                <td>
                    <form asp-action="UpdateQuantity" method="post" class="d-inline">
                        <input type="hidden" name="id" value="@product.Id" />
                        <input type="number" name="quantity" value="@product.Quantity" min="1" required />
                        <button type="submit" class="btn btn-sm btn-success">Update</button>
                    </form>
                </td>
                <td>
                    <form asp-action="Remove" method="post" class="d-inline">
                        <input type="hidden" name="id" value="@product.Id" />
                        <button type="submit" class="btn btn-sm btn-danger">Remove</button>
                    </form>
                </td>
            </tr>
        }
    </tbody>
</table>

<!-- Place Order Button -->
<form asp-action="PlaceOrder" method="post" onsubmit="return confirm('Are you sure you want to place this order?');">
    <!-- Hidden Field for StoreId -->
    <input type="hidden" name="storeId" value="@ViewBag.StoreId" />

    <!-- Date Picker for Selecting a Date -->
    <div class="form-group">
        <label for="selectedDate">Select a Date:</label>
        <input type="date" name="deliveryDate" id="deliveryDate" class="form-control" required />
    </div>

    <button type="submit" class="btn btn-lg btn-primary">Place Order</button>
</form>
