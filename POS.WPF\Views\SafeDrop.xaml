﻿<Window x:Class="POS.WPF.Views.SafeDrop"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        xmlns:local="clr-namespace:POS.WPF.Views"
        Title="SafeDrop" Height="450" Width="800"
        Background="#004e7c">

    <Grid Margin="10">
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
        </Grid.RowDefinitions>

        <!-- Amount Label -->
        <Label Grid.Row="0" Content="Amount" 
               FontSize="24" Foreground="White"
               Margin="0 0 0 10"/>

        <!-- Amount TextBox -->
        <TextBox x:Name="txtAmount" Grid.Row="1" 
                 FontSize="24" Height="50"
                 Background="#0a688d" Foreground="White"
                 Margin="0 0 0 20"/>

        <!-- Action Buttons -->
        <StackPanel Grid.Row="2" Orientation="Horizontal" 
                    HorizontalAlignment="Center" Margin="0 20">
            <Button Content="Safe Drop" 
                    Background="#004e7c" Foreground="White"
                    FontSize="24" Width="180" Margin="10"
                    Click="Button_Click"/>
            <Button Content="Cancel" 
                    Background="#004e7c" Foreground="White"
                    FontSize="24" Width="180" Margin="10"
                    Click="Button_Click_1"/>
        </StackPanel>

        <!-- Numeric Keypad -->
        <local:NumericKeypad Grid.Row="3"
                             KeyPressed="NumericKeypad_KeyPressed"
                             Margin="5"/>
    </Grid>
</Window>