﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Windows;
using System.Windows.Controls;
using System.Windows.Data;
using System.Windows.Documents;
using System.Windows.Input;
using System.Windows.Media;
using System.Windows.Media.Imaging;
using System.Windows.Shapes;

namespace POS.WPF.Views
{
    /// <summary>
    /// Interaction logic for InputTotalDialog.xaml
    /// </summary>
    public partial class InputTotalDialog : Window
    {
        public decimal TotalAmount { get; private set; }

        public InputTotalDialog()
        {
            InitializeComponent();
            Helpers.WindowStyleHelper.SetWindowStyle(this);
        }

        private void btnConfirm_Click(object sender, RoutedEventArgs e)
        {
            if (decimal.TryParse(txtTotal.Text, out var amount) && amount > 0)
            {
                TotalAmount = amount;
                DialogResult = true;
            }
            else
            {
                MessageBox.Show("Please enter a valid positive amount.");
            }
        }

        private void Button_Click(object sender, RoutedEventArgs e)
        {
            this.Close();
        }
    }
}
