﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Windows;

namespace POS.WPF.Helpers
{
    public  class WindowStyleHelper
    {
        public static void SetWindowStyle(Window window)
        {
            if (ConfigurationHelper.HideButtons)
            {
                window.ResizeMode = ResizeMode.NoResize;
                window.WindowState = WindowState.Maximized;
                window.WindowStyle = WindowStyle.None;
                window.WindowStartupLocation = WindowStartupLocation.CenterScreen;
            }
        }
    }
}
