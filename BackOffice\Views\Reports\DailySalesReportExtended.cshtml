﻿@using System.Globalization
@{
    ViewData["Title"] = "Daily Sales Report Extended";
    var days = ViewBag.Days as List<DateTime>;
    var report = ViewBag.Report as IEnumerable<dynamic>;
}

<h2>Daily Sales Report (Extended)</h2>

<form method="post" asp-action="DailySalesReportExtended">
    <div class="form-group">
        <label for="startDate">Start Date:</label>
        <input type="date" id="startDate" name="startDate" class="form-control" required />
    </div>
    <div class="form-group">
        <label for="endDate">End Date:</label>
        <input type="date" id="endDate" name="endDate" class="form-control" required />
    </div>
    <button type="submit" class="btn btn-primary">Generate Report</button>
</form>

@if (days != null && report != null)
{
    <table class="table table-bordered">
        <thead>
            <tr>
                <th>Category</th>
                <th>Product Name</th>
                @foreach (var day in days)
                {
                    <th>@day.ToString("dd MMMM yyyy")</th>
                }
            </tr>
        </thead>
        <tbody>
            @foreach (var category in report)
            {
                @foreach (var product in category.Products)
                {
                    <tr>
                        <td>@category.Category</td>
                        <td>@product.ProductName</td>
                        @foreach (var sales in product.DailySales)
                        {
                            <td>@sales.ToString("C", new CultureInfo("en-GB"))</td>
                        }
                    </tr>
                }
            }
        </tbody>
    </table>
}
