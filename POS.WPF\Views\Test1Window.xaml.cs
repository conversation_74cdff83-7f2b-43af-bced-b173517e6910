﻿using Integral.Library.GuardianClient;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Windows;
using System.Windows.Controls;
using System.Windows.Data;
using System.Windows.Documents;
using System.Windows.Input;
using System.Windows.Media;
using System.Windows.Media.Imaging;
using System.Windows.Shapes;
using static Integral.Library.GuardianClient.TransactionHook;

namespace POS.WPF.Views
{
    /// <summary>
    /// Interaction logic for Test1Window.xaml
    /// </summary>
    public partial class Test1Window : Window
    {
        public Test1Window()
        {
            InitializeComponent();
        }

        private void Button_Click(object sender, RoutedEventArgs e)
        {
            decimal amount = 78.59m;

            try
            {
                int amountInMinorUnits = (int)(amount * 100);
                var tillInfo = new TillInformation();
                var transactionHook = new TransactionHook();
                var transactionInfo = new TransactionInfo();

                bool isTransactionSuccessful = transactionHook.Process(
                    TRANSACTIONHOOK_TRANSACTIONTYPE.INT_TT_SALE,
                    amountInMinorUnits,
                    ref tillInfo,
                    ref transactionInfo
                );

                if (isTransactionSuccessful)
                {

                }
                else
                {
                }
            }
            catch (Exception ex)
            {
            }
        }
    }
}
