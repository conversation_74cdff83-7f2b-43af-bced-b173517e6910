﻿@{
    ViewData["Title"] = "Monthly Product Quantity Report";
    var months = ViewBag.Months as List<DateTime>;
    var report = ViewBag.Report as IEnumerable<dynamic>;
    var totalQuantities = ViewBag.TotalQuantities as List<int>;
}

<h2>Monthly Product Quantity Report</h2>

<table class="table table-bordered">
    <thead>
        <tr>
            <th>Sub Category</th>
            <th>Product</th>
            @foreach (var month in months)
            {
                <th>@month.ToString("MMMM yyyy")</th>
            }
        </tr>
    </thead>
    <tbody>
        @foreach (var subCategory in report)
        {
            @foreach (var product in subCategory.Products)
            {
                <tr>
                    <td>@subCategory.SubCategory</td>
                    <td>@product.ProductName</td>
                    @foreach (var quantity in product.MonthlyQuantities)
                    {
                        <td>@quantity</td>
                    }
                </tr>
            }
        }
        <tr class="font-weight-bold">
            <td colspan="2">Total</td>
            @foreach (var total in totalQuantities)
            {
                <td>@total</td>
            }
        </tr>
    </tbody>
</table>
