﻿using System.Text.Json.Serialization;

namespace BackOffice.Models
{
    public class MorrisonsProductItem
    {
        [JsonPropertyName("catalogueItemId")]
        public long CatalogueItemId { get; set; }

        [JsonPropertyName("itemId")]
        public string ItemId { get; set; }

        [JsonPropertyName("description")]
        public string Description { get; set; }

        [JsonPropertyName("category")]
        public string Category { get; set; }

        [JsonPropertyName("manufacturer")]
        public string Manufacturer { get; set; }

        [JsonPropertyName("barcodeEan")]
        public string BarcodeEan { get; set; }

        [JsonPropertyName("caseSize")]
        public double CaseSize { get; set; }

        [JsonPropertyName("prices")]
        public List<MorrisonsPrice> Prices { get; set; }

        [JsonPropertyName("subCategory")]
        public string SubCategory { get; set; }

        [JsonPropertyName("department")]
        public string Department { get; set; }

        [<PERSON>son<PERSON>ropertyName("tradingGroup")]
        public string TradingGroup { get; set; }
    }
}
