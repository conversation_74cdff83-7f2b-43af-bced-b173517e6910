﻿using System.Net.Http.Headers;

string url = "https://sit-api.morrisons.com/wholesaleorder/v1/customers/samyltd/catalogues/main/products?start=0&limit=800&apikey=DNfkuXuSFABqKhSl0gmUAzHgQqs1wPjH";
string authToken = "RE5ma3VYdVNGQUJxS2hTbDBnbVVBekhnUXFzMXdQakg6R0hmcERBemw0ODNHZVhIaA=="; 

using (HttpClient client = new HttpClient())
{
    client.DefaultRequestHeaders.Authorization = new AuthenticationHeaderValue("Basic", authToken);
    client.DefaultRequestHeaders.Accept.Add(new MediaTypeWithQualityHeaderValue("application/json"));

    HttpResponseMessage response = await client.GetAsync(url);

    if (response.IsSuccessStatusCode)
    {
        string responseData = await response.Content.ReadAsStringAsync();
        Console.WriteLine(responseData);
    }
    else
    {
        Console.WriteLine($"Error: {response.StatusCode}");
    }
}
