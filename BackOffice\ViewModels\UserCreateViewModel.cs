﻿namespace BackOffice.ViewModels
{
    public class UserCreateViewModel
    {
        public string Name { get; set; }
        public string Password { get; set; }
        public string Role { get; set; } // "Admin", "Accountant", "BackOffice", "Cashier"
        public List<string> IpAddresses { get; set; } = new();
        public int? StoreId { get; set; } // Nullable, only needed for BackOffice
    }

}
