﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace POS.Core.Models
{
    public class PendingSyncItem
    {
        public int Id { get; set; }
        public string EntityType { get; set; } // e.g., "Sale", "Product", etc.
        public int EntityId { get; set; }
        public DateTime CreatedAt { get; set; } = DateTime.Now;
        public bool IsSynced { get; set; } = false;
        public DateTime? SyncedAt { get; set; }
        public string SyncError { get; set; }
        public int RetryCount { get; set; }
        public string OperationType { get; set; } // "Insert", "Update", "Delete"
    }
}
