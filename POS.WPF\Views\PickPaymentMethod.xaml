﻿<Window x:Class="POS.WPF.Views.PickPaymentMethod"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        Title="Payment Methods" Height="450" Width="800">
    <Grid Background="#004e7c">
        <ListView Name="PaymentMethodsListView" Margin="10" FontSize="24"
                  Background="{StaticResource DataGridBackground}"
                  SelectionMode="Multiple">
            <ListView.View>
                <GridView>
                    <GridViewColumn Header="ID" DisplayMemberBinding="{Binding Id}" Width="80">
                        <GridViewColumn.HeaderContainerStyle>
                            <Style TargetType="GridViewColumnHeader">
                                <Setter Property="Background" Value="Green"/>
                                <Setter Property="Foreground" Value="White"/>
                                <Setter Property="FontSize" Value="16"/>
                            </Style>
                        </GridViewColumn.HeaderContainerStyle>
                    </GridViewColumn>
                    <GridViewColumn Header="Method" DisplayMemberBinding="{Binding Method}" Width="250">
                        <GridViewColumn.HeaderContainerStyle>
                            <Style TargetType="GridViewColumnHeader">
                                <Setter Property="Background" Value="Green"/>
                                <Setter Property="Foreground" Value="White"/>
                                <Setter Property="FontSize" Value="16"/>
                            </Style>
                        </GridViewColumn.HeaderContainerStyle>
                    </GridViewColumn>
                </GridView>
            </ListView.View>
            <ListView.ItemContainerStyle>
                <Style TargetType="ListViewItem">
                    <Setter Property="Background" Value="#0a688d"/>
                    <Setter Property="Foreground" Value="White"/>
                    <Setter Property="Margin" Value="0,2"/>
                </Style>
            </ListView.ItemContainerStyle>
        </ListView>

        <Button Content="SELECT" HorizontalAlignment="Right" VerticalAlignment="Bottom" 
                Margin="10" Padding="20,10" FontSize="24"
                Background="#004e7c" Foreground="White" Click="SelectButton_Click"/>

        <Button Content="CANCEL" HorizontalAlignment="Right" VerticalAlignment="Bottom" 
                Margin="0,0,133,10" Padding="20,10" FontSize="24"
                Background="#004e7c" Foreground="White" Click="CancelButton_Click"/>
    </Grid>
</Window>
