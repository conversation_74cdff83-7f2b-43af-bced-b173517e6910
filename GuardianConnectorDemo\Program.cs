﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using Integral.Library.GuardianClient;
using static Integral.Library.GuardianClient.TillInformation;

namespace GuardianConnectorDemo
{
    internal class Program
    {
        static void Main(string[] args)
        {
            int amountInPence = 100;

            bool result = ProcessPayment(amountInPence);

            Console.WriteLine(result ? "Payment successful!" : "Payment failed or was declined");
            Console.ReadLine();

            // ProcessTransaction();

        }

        public static bool ProcessTransaction()
        {
            bool returnValue = false;
            var tillInformation = new TillInformation();
            var transactionInfo = new TransactionInfo();
            var transaction = new TransactionHook();

            // Populate the till information object
            tillInformation.MerchantName = "Test Shop";
            tillInformation.Address1 = "Test Address 1";
            tillInformation.Address2 = "Test Address 2";
            tillInformation.Address3 = "Test Address 3";

            // If the till supports the processing of fuel cards then add
            // the products details associated with the transaction
            transaction.InitialiseProductInfo();
            transaction.AddProductInfo("002", "10900", "10000", 0); // Unleaded
            transaction.AddProductInfo("045", "500", "", 1);        // Car Wash

            // Process the transaction
            var transactionType = TransactionHook.TRANSACTIONHOOK_TRANSACTIONTYPE.INT_TT_SALE;

            if (transaction.Process(transactionType, 11400, ref tillInformation, ref transactionInfo))
            {
                // Transaction has been authorised
                // TransactionInfo object will contain information about the transaction
                returnValue = true;
            }
            else
            {
                // Transaction has been aborted/cancelled/declined
                // Check transactionInfo.ResponseCode to determine the exact outcome
            }

            return returnValue;
        }

        static bool ProcessPayment(int amountInLowestDenomination)
        {
            try
            {
                // Create required objects
                var tillInfo = new TillInformation();
                var transInfo = new TransactionInfo();
                var transaction = new TransactionHook();

                // Populate till information (required for receipts)
                tillInfo.MerchantName = "Sammy Stores";

                tillInfo.Address1 = "123 High Street";
                tillInfo.Address2 = "London";
                tillInfo.Address3 = "UK";

                // Process a sale transaction
                bool paymentResult = transaction.Process(
                    TransactionHook.TRANSACTIONHOOK_TRANSACTIONTYPE.INT_TT_SALE,
                    amountInLowestDenomination,
                    ref tillInfo,
                    ref transInfo);

                // Check transaction outcome
                if (paymentResult)
                {
                    Console.WriteLine("Transaction authorized!");
                    Console.WriteLine($"Auth Code: {transInfo.AuthorisationCode}");
                    Console.WriteLine($"Card: {transInfo.SchemeName} (****{transInfo.PanLast4Digits})");
                    return true;
                }
                else
                {
                    Console.WriteLine("Transaction failed or was declined");
                    if (transInfo != null)
                    {
                        Console.WriteLine($"Response Code: {transInfo.ResponseCode}");
                    }
                    return false;
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Error processing payment: {ex.Message}");
                return false;
            }
        }
    }
}



