﻿// See https://aka.ms/new-console-template for more information
using Microsoft.EntityFrameworkCore;
using POS.Core.Models;

Console.WriteLine("Hello, World!");



void BackupDatabase(string backupPath)
{
    var dbName = "POS1"; // Replace with your actual DB name
    var backupCommand = $@"BACKUP DATABASE [{dbName}] 
                           TO DISK = '{backupPath}' 
                           WITH FORMAT, INIT, NAME = '{dbName}-FullBackup';";

    using (var context = new Data())
    {
        context.Database.ExecuteSqlRaw(backupCommand);
    }

    Console.WriteLine($"Database backup completed at: {backupPath}");
}


 void RestoreDatabase(string backupPath)
{
    var dbName = "POS1"; // Replace with your actual DB name
    var restoreCommand = $@"
        USE master;
        ALTER DATABASE [{dbName}] SET SINGLE_USER WITH ROLLBACK IMMEDIATE;
        RESTORE DATABASE [{dbName}] FROM DISK = '{backupPath}' WITH REPLACE;
        ALTER DATABASE [{dbName}] SET MULTI_USER;";

    using (var context = new Data())
    {
        context.Database.ExecuteSqlRaw(restoreCommand);
    }

    Console.WriteLine("Database restored successfully.");
}



