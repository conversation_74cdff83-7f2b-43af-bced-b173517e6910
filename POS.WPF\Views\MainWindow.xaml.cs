﻿using System.Collections;
using System.Collections.ObjectModel;
using System.Configuration;
using System.Diagnostics;
using System.Diagnostics.CodeAnalysis;
using System.DirectoryServices.ActiveDirectory;
using System.IO;
using System.IO.Ports;
using System.Net.Cache;
using System.Net.Http;
using System.Reflection;
using System.Runtime.InteropServices;
using System.Text;
using System.Text.Json;
using System.Text.Json.Serialization;
using System.Text.RegularExpressions;
using System.Windows;
using System.Windows.Controls;
using System.Windows.Data;
using System.Windows.Documents;
using System.Windows.Input;
using System.Windows.Interop;
using System.Windows.Media;
using System.Windows.Media.Animation;
using System.Windows.Media.Converters;
using System.Windows.Media.Imaging;
using System.Windows.Navigation;
using System.Windows.Shapes;
using System.Windows.Threading;
using Integral.Library.GuardianClient;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Internal;
using Microsoft.EntityFrameworkCore.Storage.Json;
using NLog;
using POS.Core.Models;
using POS.Printer;
using POS.WPF.Helpers;
using POS.WPF.ViewModels;
using POS.WPF.Views;
using TimeManagement;
using WindowsInput;
using WindowsInput.Native;
using static Integral.Library.GuardianClient.TransactionHook;
using PaidOut = POS.WPF.Views.PaidOut;
using SafeDrop = POS.Core.Models.SafeDrop;
using Void = POS.Core.Models.Void;


namespace POS.WPF;

/// <summary>
/// Interaction logic for MainWindow.xaml
/// </summary>
public partial class MainWindow : Window
{
    public bool IsPriceInquiry { get; set; }
    public bool IsPriceOverride { get; set; }
    private DispatcherTimer _timer;
    private static readonly Logger _logger = LogManager.GetCurrentClassLogger();
    private DispatcherTimer syncTimer;

    private Product _selectedProduct;
    private SerialPort _serialPort;
    private InputSimulator _inputSimulator;

    /// <summary>
    /// this is for the dynamic product set in store
    /// </summary>
    private int? _specialProductId = null;
    public MainWindow()
    {

        if (ConfigurationHelper.HideButtons)
        {
            DisableCloseButton = true;
        }

        InitializeComponent();
        SetDataGridBackground();
        SetShiftUIData();
        Helpers.WindowStyleHelper.SetWindowStyle(this);

        syncTimer = new DispatcherTimer();
        syncTimer.Interval = TimeSpan.FromSeconds(5); // 1 minute
        syncTimer.Tick += SyncTimer_Tick;
        syncTimer.Start();

        // Initialize the timer
        _timer = new DispatcherTimer();
        _timer.Interval = TimeSpan.FromSeconds(1); // Update every second
        _timer.Tick += Timer_Tick;

        // Start the timer
        _timer.Start();

        // Set the initial time
        UpdateTime();

        DataContext = new MainViewModel();
        _inputSimulator = new InputSimulator();

        if (ConfigurationHelper.ScannerType == "KEYBOARD")
        {
            barcodeTextBox.Visibility = Visibility.Visible;
            barcodeTextBox.Focus();
            //
        }
        else if (ConfigurationHelper.ScannerType == "HID")
        {

        }
        else
        {
            _serialPort = new SerialPort(ConfigurationHelper.ScannerPort, 9600, Parity.None, 8, StopBits.One)
            {
                Handshake = Handshake.None,
                ReadTimeout = 500,
                WriteTimeout = 500
            };

            _serialPort.DataReceived += SerialPort_DataReceived;

            try
            {
                _serialPort.Open(); // Open the port
            }
            catch (Exception ex)
            {
                MessageBox.Show($"Error opening COM5: {ex.Message}");
                _logger.Error($"Error opening COM5: {ex.Message}");
            }
        }




    }

    private bool isSyncing = false;

    private async void SyncTimer_Tick(object sender, EventArgs e)
    {
        if (isSyncing) return; // Skip if already running

        isSyncing = true;

        await Task.Run(async () =>
        {
            try
            {
                Services.RemoteToLocalSyncService.SyncAll();
            }
            catch (Exception ex)
            {
                // log error
                _logger.Error(ex, "Error during sync");
            }
            finally
            {
                isSyncing = false;
            }
        });
    }

    // Keep the AddPendingSyncItem method as it's still used in MainWindow
    // Remove the SyncAll method as it's now in DataSyncService


    private const int SC_CLOSE = 0xF060;
    private const int WM_SYSCOMMAND = 0x0112;

    // Flag to enable/disable close button dynamically
    public bool DisableCloseButton { get; set; } = false;

    protected override void OnSourceInitialized(EventArgs e)
    {
        base.OnSourceInitialized(e);
        IntPtr hwnd = new WindowInteropHelper(this).Handle;
        HwndSource source = HwndSource.FromHwnd(hwnd);
        source.AddHook(WndProc);
    }

    private IntPtr WndProc(IntPtr hwnd, int msg, IntPtr wParam, IntPtr lParam, ref bool handled)
    {
        if (DisableCloseButton && msg == WM_SYSCOMMAND && wParam.ToInt32() == SC_CLOSE)
        {
            handled = true; // Prevents the window from closing
        }
        return IntPtr.Zero;
    }

    // Method to toggle close button behavior dynamically
    public void SetCloseButtonState(bool disable)
    {
        DisableCloseButton = disable;
    }

    private void Timer_Tick(object sender, EventArgs e)
    {
        UpdateTime();
    }

    private void UpdateTime()
    {
        // Get the current time and update the TextBlock
        labelCurrentTime.Content = "Current Time: " + DisplayTimeProvider.Now.ToString("dd/MM/yyyy HH:mm:ss");
    }

    private void SetShiftUIData()
    {
        labelCashierName.Content = $"Cashier:  {UiShift.CashierName}";
        labelShiftStartTime.Content = $"Shift Start Time:  {DisplayTimeProvider.ConvertToDisplayTime(UiShift.Shift.StartTime).ToString("hh:mm:ss tt")}";
        labelShiftId.Content = $"Shift Id:  {UiShift.ShiftId}";
        labelDayStartTime.Content = $"Day Start Time:  {DisplayTimeProvider.ConvertToDisplayTime(UiShift.StoreDay.StartTime).ToString()}";
    }

    /// <summary>
    /// Only affect the aesthetics of the DataGrid
    /// </summary>
    private void SetDataGridBackground()
    {
        using (LocalData.LocalContext localDb = new LocalData.LocalContext())
        {
            // Get your store data (from your existing code)
            var store = localDb.Stores
                .Include(s => s.Brand)
                .Where(s => s.StoreId == UiShift.StoreId).FirstOrDefault();

            if (store?.Brand?.BrandLogo == null) return;

            // Construct the full URL
            string baseUrl = ConfigurationHelper.LogoUrl;
            string imageName = store.Brand.BrandLogo;
            string fullUrl = $"{baseUrl}/{imageName}";

            // Create image brush with remote source
            var logoBrush = new ImageBrush();

            try
            {
                logoBrush.ImageSource = new BitmapImage(new Uri(fullUrl, UriKind.Absolute))
                {
                    CacheOption = BitmapCacheOption.None,
                    UriCachePolicy = new RequestCachePolicy(RequestCacheLevel.BypassCache)
                };

                // Styling properties
                logoBrush.Opacity = 0.3;  // Adjust transparency (30% visible)
                logoBrush.Stretch = Stretch.Uniform;
                logoBrush.AlignmentX = AlignmentX.Center;
                logoBrush.AlignmentY = AlignmentY.Center;

                // Apply to DataGrid
                MyDataGrid.Background = logoBrush;
                MyDataGrid.RowBackground = Brushes.Transparent;
            }
            catch (Exception ex)
            {
                // Handle image loading errors
                Console.WriteLine($"Error loading logo: {ex.Message}");
                _logger.Warn($"Error loading logo: {ex.Message}");
                // Optionally set a fallback background
                MyDataGrid.Background = new SolidColorBrush(Color.FromArgb(0x80, 0x00, 0x4E, 0x7C));
            }
        }
    }


    private void LoadLogo()
    {
        try
        {
            using (LocalData.LocalContext data = new LocalData.LocalContext())
            {
                var store = data.Stores
                    .Include(s => s.Brand)
                    .Where(s => s.StoreId == UiShift.StoreId).FirstOrDefault();


                string baseUrl = ConfigurationHelper.LogoUrl; // Replace with actual remote URL
                string imageName = store.Brand.BrandLogo; // Replace with actual image name
                string fullUrl = $"{baseUrl}/{imageName}.png";

                BitmapImage bitmap = new BitmapImage();
                bitmap.BeginInit();
                bitmap.UriSource = new Uri(fullUrl);
                bitmap.CacheOption = BitmapCacheOption.OnLoad;
                bitmap.EndInit();

                //imgLogo.Source = bitmap;
            }
        }
        catch (Exception ex)
        {
            MessageBox.Show("Error loading logo: " + ex.Message, "Error", MessageBoxButton.OK, MessageBoxImage.Error);
            _logger.Error("Error loading logo: " + ex.Message);
        }
    }

    private void SerialPort_DataReceived(object sender, SerialDataReceivedEventArgs e)
    {
        try
        {
            string scannedData = _serialPort.ReadExisting(); // Read all available data
            Dispatcher.Invoke(() => ProcessBarcode(scannedData.Trim())); // Update the UI thread
        }
        catch (Exception ex)
        {
            Dispatcher.Invoke(() => MessageBox.Show($"Error reading data: {ex.Message}"));
            _logger.Error($"Error reading data: {ex.Message}");
        }
    }




    private void ProcessBarcode(string barcode)
    {
        txtTotal.Text = "";
        txtTotal.Visibility = Visibility.Hidden;

        barcode = Regex.Replace(barcode, @"\D", "");

        using (LocalData.LocalContext context = new LocalData.LocalContext())
        {
            try
            {
                var productData = context.Products
               .Where(p => p.Barcode == barcode)
               .Select(p => new
               {
                   Product = p,
                   Division = p.Division,
                   SubCategory = p.Division.SubCatagory,
                   ProductStore = context.ProductStores.FirstOrDefault(ps => ps.StoreId == UiShift.StoreId
                   && ps.ProductId == p.Id),
                   Promotion = context.Promotions.Include(promo => promo.Products).FirstOrDefault(promo =>
                       promo.StartTime <= CustomTimeProvider.Now &&
                       promo.EndTime >= CustomTimeProvider.Now && p.PromotionId == promo.Id),
                   NonBrandPromotion = context.NonBrandPromotions.Include(promo => promo.Products).FirstOrDefault(promo =>
                       promo.StartTime <= CustomTimeProvider.Now &&
                       promo.EndTime >= CustomTimeProvider.Now && p.NonBrandPromotionId == promo.Id)
               })
               .FirstOrDefault();

                if (productData.Product != null)
                    ProcessLoadedProduct(productData);
                else MessageBox.Show("Product not found!");
            }
            catch (Exception ex)
            {
                MessageBox.Show(ex.Message);
                _logger.Error(ex.Message);
            }
        }
    }

    /// <summary>
    /// Processes a core product in the POS system by validating store compatibility, 
    /// checking for cashier alerts, and adding it to the UI product list with promotions.
    /// </summary>
    /// <param name="context">The database context used to fetch and validate data.</param>
    /// <param name="coreProduct">The product being processed.</param>
    /// <remarks>
    /// The method performs the following steps:
    /// 1. Validates that the selected product belongs to the correct brand for the store.
    ///    - If the product's brand does not match the store's brand, an error message is displayed, and processing stops.
    /// 2. Retrieves the product's sub-category from the division information.
    /// 3. Checks if the sub-category has a cashier alert message.
    ///    - If a cashier alert exists, a confirmation dialog is displayed.
    ///    - If the cashier selects "No", the UI product list is cleared, a transaction cancellation message is shown, and processing stops.
    /// 4. If validation passes, the product is added to the UI product list with details from the `ProductStore` table.
    /// 5. Applies any relevant promotions to the product based on its PLU (Price Look-Up) code.
    /// </remarks>



    private void Window_Closing(object sender, System.ComponentModel.CancelEventArgs e)
    {
        if (_serialPort != null && _serialPort.IsOpen)
        {
            _serialPort.Close(); // Close the port when the application exits
        }
    }


    private void UpdateTransactionSummary()
    {
        //dataGridView1.DataSource = null;
        //dataGridView1.DataSource = _currentTransaction.Items;
        //textBox1.Text = $"{_currentTransaction.GrandTotal:C}";
    }


    //add product
    private void Button_Click(object sender, RoutedEventArgs e)
    {
        using (var context = new LocalData.LocalContext())
        {
            var productData = context.Products
                .Where(p => p.PLU == txtInput.Text)
                .Select(p => new
                {
                    Product = p,
                    Division = p.Division,
                    SubCategory = p.Division.SubCatagory,
                    ProductStore = context.ProductStores.FirstOrDefault(ps => ps.StoreId == UiShift.StoreId
                    && ps.ProductId == p.Id),
                    Promotion = context.Promotions.Include(promo => promo.Products).OrderBy(p => p.Id).LastOrDefault(promo =>
                        promo.StartTime <= CustomTimeProvider.Now &&
                        promo.EndTime >= CustomTimeProvider.Now && p.PromotionId == promo.Id),
                    NonBrandPromotion = context.NonBrandPromotions.Include(promo => promo.Products).FirstOrDefault(promo =>
                        promo.StartTime <= CustomTimeProvider.Now &&
                        promo.EndTime >= CustomTimeProvider.Now && p.NonBrandPromotionId == promo.Id),
                    // Get the quantity limit for this subcategory and store
                    SubCategoryLimit = context.StoreSubCategoryLimits
                        .FirstOrDefault(sl => sl.StoreId == UiShift.StoreId &&
                                             sl.SubCategoryId == p.Division.SubCatagory.Id)
                })
                .FirstOrDefault();

            if (productData != null)
            {
                // Check if there's a quantity limit for this subcategory
                if (productData.SubCategoryLimit != null && productData.SubCategoryLimit.QuantityLimit > 0)
                {
                    // Calculate current quantity of products from this subcategory in the cart
                    var viewModel = DataContext as MainViewModel;
                    var currentSubCategoryQuantity = viewModel.Products
                        .Where(p => p.SubCategoryId == productData.SubCategory.Id)
                        .Sum(p => p.Quantity);

                    // If adding one more would exceed the limit, show a message and return
                    if (currentSubCategoryQuantity + 1 > productData.SubCategoryLimit.QuantityLimit)
                    {
                        MessageBox.Show($"Cannot add more items from {productData.SubCategory.Name}. " +
                                       $"Limit of {productData.SubCategoryLimit.QuantityLimit} has been reached.",
                                       "Quantity Limit Reached", MessageBoxButton.OK, MessageBoxImage.Warning);
                        return;
                    }
                }

                if (IsPriceInquiry)
                {
                    if (productData?.Product == null ||
                        productData.Product.BrandId != context.Stores.First(s => s.StoreId == UiShift.StoreId).BrandId)
                    {
                        IsPriceInquiry = false;
                        return;
                    }

                    IsPriceInquiry = false;
                    return;
                }

                if (productData?.Product == null)
                {
                    MessageBox.Show("Invalid Product!");
                    return;
                }

                txtTotal.Text = "";
                txtTotal.Visibility = Visibility.Hidden;

                ProcessLoadedProduct(productData);
            }
        }

        barcodeTextBox.Visibility = Visibility.Visible;
        barcodeTextBox.Focus();
    }

    private void ProcessLoadedProduct(dynamic productData)
    {
        try
        {
            var product = productData.Product;
            var storeBrandId = productData.ProductStore?.Product.BrandId;

            if (product.BrandId != null && storeBrandId != product.BrandId)
            {
                MessageBox.Show("Invalid Product for the Store!");
                return;
            }

            if (!string.IsNullOrEmpty(productData.SubCategory?.CashierAlert))
            {
                var dialog = MessageBox.Show(productData.SubCategory.CashierAlert, "Alert", MessageBoxButton.YesNo, MessageBoxImage.Question);
                if (dialog == MessageBoxResult.No)
                {
                    (DataContext as MainViewModel).Products.Clear();
                    MessageBox.Show("Transaction Cancelled!");
                    return;
                }
            }

            AddCoreProductToUIProduct(product, productData.ProductStore, productData.Promotion, productData.NonBrandPromotion);

            // After adding the product, recalculate group promotions
        }
        catch (Exception ex)
        {
            MessageBox.Show(ex.Message);
            _logger.Error(ex.Message);
        }
    }



    private void AddCoreProductToUIProduct(Core.Models.Product product, Core.Models.ProductStore productStore, Promotion promotion, NonBrandPromotion nonBrandPromotion)
    {
        if ((DataContext as MainViewModel).Products.Any(pp => pp.Barcode == product.Barcode))
        {
            var existingUiProduct = (DataContext as MainViewModel).Products.FirstOrDefault(p => p.Barcode == product.Barcode);
            existingUiProduct.Quantity++;
        }
        else
        {
            POS.WPF.Product product1 = new POS.WPF.Product();
            product1.ProductName = product.Name;
            product1.PLU = product.PLU;
            product1.Barcode = product.Barcode;
            product1.UnitPrice = productStore.StoreSpecificPrice ?? product.SellingPrice;
            (DataContext as MainViewModel).Products.Add(product1);
            var uiProduct = (DataContext as MainViewModel).Products.FirstOrDefault(p => p.Barcode == product.Barcode);
            if (uiProduct != null && promotion != null)
            {
                uiProduct.Promition = promotion;
                uiProduct.Note = promotion.Name;
            }
            else if (uiProduct != null && nonBrandPromotion != null)
            {
                uiProduct.NonBrandPromotion = nonBrandPromotion;
                uiProduct.Note = nonBrandPromotion.Name;
            }
            product1.Quantity = 1;
        }

    }



    //void
    private async void Button_Click_1(object sender, RoutedEventArgs e)
    {
        try
        {
            if ((DataContext as MainViewModel)?.Products == null || !(DataContext as MainViewModel).Products.Any())
            {
                MessageBox.Show("No items to void!", "Warning", MessageBoxButton.OK, MessageBoxImage.Warning);
                return;
            }

            var dialog = MessageBox.Show("Are you sure you want to void this transaction?", "Confirm Void",
                MessageBoxButton.YesNo, MessageBoxImage.Question);

            if (dialog == MessageBoxResult.No)
                return;

            // Capture UI data before clearing
            var viewModel = DataContext as MainViewModel;
            var voidSaleItems = viewModel.Products.Select(item => new VoidSaleItem
            {
                Qty = item.Quantity,
                Total = item.Total,
                PLU = item.PLU,
                IsDeleted = false,
                LastModified = CustomTimeProvider.Now
            }).ToList();

            // Clear UI immediately for better UX
            viewModel.Products.Clear();
            barcodeTextBox.Visibility = Visibility.Visible;
            barcodeTextBox.Focus();

            // Process void in background
            await Task.Run(async () =>
            {
                using (var context = new LocalData.LocalContext())
                {
                    var @void = new Core.Models.Void
                    {
                        PosId = (int)UiShift.PosMachineId,
                        StoreId = (int)UiShift.StoreId,
                        VoidSaleItems = voidSaleItems,
                        VoidTime = CustomTimeProvider.Now,
                        IsDeleted = false,
                        LastModified = CustomTimeProvider.Now
                    };

                    // Save the void entity
                    context.Voids.Add(@void);
                    await context.SaveChangesAsync();

                    // Add to PendingSyncItem for syncing
                    var pendingSyncItem = new PendingSyncItem
                    {
                        EntityType = "Void",
                        EntityId = @void.Id,
                        CreatedAt = CustomTimeProvider.Now,
                        IsSynced = false,
                        OperationType = "Insert",
                        SyncError = ""
                    };

                    // Add to PendingSyncItem for syncing
                    context.PendingSyncItems.Add(pendingSyncItem);
                    await context.SaveChangesAsync();
                }
            });

            MessageBox.Show("Transaction voided successfully!", "Success", MessageBoxButton.OK, MessageBoxImage.Information);
        }
        catch (Exception ex)
        {
            _logger.Error(ex, "Error voiding transaction");
            MessageBox.Show($"Error voiding transaction: {ex.Message}", "Error", MessageBoxButton.OK, MessageBoxImage.Error);
        }
        finally
        {
            barcodeTextBox.Visibility = Visibility.Visible;
            barcodeTextBox.Focus();
        }
    }





    //hold transaction
    private async void Button_Click_2(object sender, RoutedEventArgs e)
    {
        if ((DataContext as MainViewModel).Products.Count == 0)
        {
            MessageBox.Show("No items to hold!");
            return;
        }

        try
        {
            var dialog = MessageBox.Show("Confirmed?", "Alart", MessageBoxButton.YesNo, MessageBoxImage.Question);

            if (dialog == MessageBoxResult.Yes)
            {
                // Capture data we need from UI thread
                var viewModel = DataContext as MainViewModel;
                var products = viewModel.Products.ToList();
                var totalAmount = viewModel.TotalAmount;

                // Clear UI immediately
                viewModel.Products.Clear();
                barcodeTextBox.Visibility = Visibility.Visible;
                barcodeTextBox.Focus();

                // Process in background
                await Task.Run(async () =>
                {
                    using (var context = new LocalData.LocalContext())
                    {
                        Sale sale = new Sale();
                        sale.Date = CustomTimeProvider.Now;
                        sale.ShiftId = (int)UiShift.ShiftId;
                        sale.TotalValue = totalAmount;
                        sale.IsOnHold = true;
                        sale.ProductStoreSales = new List<ProductStoreSale>();
                        sale.SpecialProductSales = new List<SpecialProductSale>();

                        foreach (var item in products)
                        {
                            if (item.IsSpecialProduct == true)
                            {
                                var coreProduct = context.Products.First(p => p.Name == item.ProductName);

                                var specialProductSale = new SpecialProductSale()
                                {
                                    Amount = item.Total,
                                    ProductId = coreProduct.Id,
                                    Product = coreProduct,
                                    IsDeleted = false,
                                    LastModified = CustomTimeProvider.Now
                                };

                                sale.SpecialProductSales.Add(specialProductSale);
                            }
                            else
                            {
                                var productStore = GetProductStore(item.Barcode, (int)UiShift.StoreId);
                                sale.ProductStoreSales.Add(CreateProductStoreSale(item, productStore.ProductStoreId));
                            }
                        }

                        // Save the sale entity
                        context.Sales.Add(sale);
                        await context.SaveChangesAsync();


                        var OriginalProductStoreSales = sale.ProductStoreSales.ToList();
                        var OriginalSpecialProductSales = sale.SpecialProductSales.ToList();

                        context.Entry(sale).State = EntityState.Detached;
                        foreach (var productStoreSale in OriginalProductStoreSales)
                        {
                            context.Entry(productStoreSale).State = EntityState.Detached;
                        }

                        foreach (var specialProductSale in OriginalSpecialProductSales)
                        {
                            context.Entry(specialProductSale).State = EntityState.Detached;
                        }





                        // Add to PendingSyncItem for syncing
                        var pendingSyncItem = new PendingSyncItem
                        {
                            EntityType = "Sale",
                            EntityId = sale.SaleId,
                            CreatedAt = CustomTimeProvider.Now,
                            IsSynced = false,
                            OperationType = "Insert",
                            SyncError = ""
                        };
                        context.PendingSyncItems.Add(pendingSyncItem);




                        foreach (var productStoreSale in OriginalProductStoreSales)
                        {
                            context.PendingSyncItems.Add(new PendingSyncItem
                            {
                                EntityType = "ProductStoreSale",
                                EntityId = productStoreSale.ProductStoreSaleId,
                                CreatedAt = CustomTimeProvider.Now,
                                IsSynced = false,
                                OperationType = "Insert",
                                SyncError = ""
                            });
                        }

                        foreach (var specialProductSale in OriginalSpecialProductSales)
                        {
                            context.PendingSyncItems.Add(new PendingSyncItem
                            {
                                EntityType = "SpecialProductSale",
                                EntityId = specialProductSale.Id,
                                CreatedAt = CustomTimeProvider.Now,
                                IsSynced = false,
                                OperationType = "Insert",
                                SyncError = ""
                            });
                        }


                        await context.SaveChangesAsync();
                    }
                });

                MessageBox.Show("Transaction held successfully!", "Success", MessageBoxButton.OK, MessageBoxImage.Information);
            }
        }
        catch (Exception ex)
        {
            _logger.Error(ex, "Error holding transaction");
            MessageBox.Show($"Error holding transaction: {ex.Message}", "Error", MessageBoxButton.OK, MessageBoxImage.Error);
        }
        finally
        {
            barcodeTextBox.Visibility = Visibility.Visible;
            barcodeTextBox.Focus();
        }
    }

    public Dictionary<string, ObservableCollection<Product>> HeldTransactions { get; private set; } = new();



    //finalize
    private async void Button_Click_3(object sender, RoutedEventArgs e)
    {
        if ((DataContext as MainViewModel).Products.Count() == 0)
            return;

        try
        {
            // Capture UI data on the UI thread before background processing
            var products = (DataContext as MainViewModel).Products.ToList();
            var totalAmount = (DataContext as MainViewModel).TotalAmount;

            // Get payment methods in background while preparing sale data
            var paymentMethodsTask = Task.Run(() =>
            {
                using (var context = new LocalData.LocalContext())
                {
                    return context.PaymentMethods
                        .Where(p => p.Method != "Cash")
                        .ToList();
                }
            });

            // Create sale object and prepare data in background
            var saleDataTask = Task.Run(async () =>
            {
                using (var context = new LocalData.LocalContext())
                {
                    var sale = new Sale
                    {
                        Date = CustomTimeProvider.Now,
                        ShiftId = (int)UiShift.ShiftId,
                        TotalValue = totalAmount,
                        ProductStoreSales = new List<ProductStoreSale>()
                    };

                    var productStoreSales = new List<ProductStoreSale>();
                    var specialProductSales = new List<SpecialProductSale>();
                    var printerProducts = new List<Printer.Product>();

                    // Process products in parallel
                    var productTasks = products.Select(async item =>
                    {
                        if (item.IsSpecialProduct)
                        {
                            printerProducts.Add(new Printer.Product(item.ProductName, item.Total, Printer.ProductPriceReductionType.None, 0));

                            var coreProduct = await context.Products.FirstAsync(p => p.Barcode == item.Barcode);
                            specialProductSales.Add(new SpecialProductSale()
                            {
                                Amount = item.Total,
                                ProductId = coreProduct.Id,
                                Product = coreProduct
                            });
                        }
                        else
                        {
                            var productStore = await GetProductStoreAsync(item.Barcode, (int)UiShift.StoreId);
                            var productStoreSale = CreateProductStoreSale(item, productStore.ProductStoreId);
                            productStoreSales.Add(productStoreSale);

                            await UpdateInventoryAsync(productStore, item.Quantity);

                            var reductionType = DetermineReductionType(productStoreSale);
                            decimal reducedAmount = GetReducedAmount(productStoreSale);

                            printerProducts.Add(new Printer.Product($"{productStore.Product.Name} {productStoreSale.Quantity}",
                                productStoreSale.Total, reductionType, reducedAmount));
                        }
                    });

                    await Task.WhenAll(productTasks);
                    return (sale, productStoreSales, specialProductSales, printerProducts);
                }
            });

            // Wait for both tasks to complete
            await Task.WhenAll(paymentMethodsTask, saleDataTask);

            var paymentMethods = paymentMethodsTask.Result;
            var saleData = saleDataTask.Result;

            var sale = saleData.sale;
            var productStoreSales = saleData.productStoreSales;
            var specialProductSales = saleData.specialProductSales;
            var printerProducts = saleData.printerProducts;

            // Show payment window on UI thread
            PaymentWindow paymentWindow = new PaymentWindow(paymentMethods, sale.TotalValue);
            bool? result = paymentWindow.ShowDialog();


            if (result != true || !paymentWindow.IsPaymentConfirmed)
            {
                return;
            }


            (DataContext as MainViewModel).Products.Clear();


            sale.CashTendered = paymentWindow.PaidAmount;

            // Process payment methods in background
            var (salePaymentMethods, otherPayment, otherPaymentTotal, cashBalance) = await Task.Run(() =>
            {
                using (var context = new LocalData.LocalContext())
                {
                    var salePaymentMethods = new List<SalePaymentMethod2>();
                    PaymentMethod otherPayment = null;
                    decimal otherPaymentTotal = 0;
                    decimal cashBalance = 0;

                    if (paymentWindow.SelectedPaymentMethods.Count == 0) // cash only
                    {
                        salePaymentMethods.Add(new SalePaymentMethod2
                        {
                            Sale = sale,
                            PaymentMethodId = context.PaymentMethods.Where(p => p.Method == "Cash").First().Id,
                            Amount = sale.TotalValue
                        });

                        cashBalance = (decimal)sale.CashTendered - sale.TotalValue;
                    }
                    else if (paymentWindow.SelectedPaymentMethods.Count == 1) // cash and another
                    {
                        otherPaymentTotal = sale.TotalValue - (sale.CashTendered ?? 0);
                        otherPayment = paymentWindow.SelectedPaymentMethods.FirstOrDefault(pm => pm.Method != "Cash");
                        var cashPayment = context.PaymentMethods.Where(p => p.Method == "Cash").First();

                        if (sale.CashTendered > 0 && otherPayment != null)
                        {
                            salePaymentMethods.Add(new SalePaymentMethod2
                            {
                                Sale = sale,
                                PaymentMethodId = cashPayment.Id,
                                Amount = sale.CashTendered ?? 0
                            });

                            salePaymentMethods.Add(new SalePaymentMethod2
                            {
                                Sale = sale,
                                PaymentMethodId = otherPayment.Id,
                                Amount = otherPaymentTotal
                            });
                        }
                        else if (otherPayment != null) //other payment only
                        {
                            salePaymentMethods.Add(new SalePaymentMethod2
                            {
                                Sale = sale,
                                PaymentMethodId = otherPayment.Id,
                                Amount = otherPaymentTotal
                            });
                        }
                    }

                    return (salePaymentMethods, otherPayment, otherPaymentTotal, cashBalance);
                }
            });

            sale.SalePaymentMethods = salePaymentMethods;

            // Process payment in background
            await Task.Run(() =>
            {
                ProcessPayment(sale, printerProducts, productStoreSales, specialProductSales,
                    salePaymentMethods.Select(p => p.PaymentMethodId).ToList(), totalAmount);
            });

            // Update UI on UI thread
            if (otherPayment != null)
            {
                txtTotal.Visibility = Visibility.Visible;
                txtTotal.Text = $"{otherPayment.Method} Amount: {otherPaymentTotal}";
            }
            else
            {
                txtTotal.Visibility = Visibility.Visible;
                txtTotal.Text = $"Cash Balance Amount: {cashBalance}";
            }

            barcodeTextBox.Visibility = Visibility.Visible;
            barcodeTextBox.Focus();
        }
        catch (Exception ex)
        {
            MessageBox.Show($"Error: {ex.Message}", "Error", MessageBoxButton.OK, MessageBoxImage.Error);
            _logger.Error(ex, "Error finalizing transaction");
            File.AppendAllText("errors.txt", $"{DateTime.Now}: {ex.Message}\n");
            File.AppendAllText("errors.txt", $"{DateTime.Now}: {ex.InnerException}\n");
            File.AppendAllText("errors.txt", $"{DateTime.Now}: {ex.StackTrace}\n");

        }
    }

    // Helper method to get product store asynchronously
    private async Task<ProductStore> GetProductStoreAsync(string barcode, int storeId)
    {
        using (LocalData.LocalContext context = new LocalData.LocalContext())
        {
            return await context.ProductStores
                .Include(p => p.Product)
                .FirstAsync(p => p.Product.Barcode == barcode && p.StoreId == storeId);
        }
    }

    // Helper method to update inventory asynchronously
    private async Task UpdateInventoryAsync(ProductStore productStore, int quantity)
    {
        try
        {
            using (var localContext = new LocalData.LocalContext())
            {
                // Get the product store from local database
                var localProductStore = await localContext.ProductStores
                    .FirstOrDefaultAsync(ps => ps.ProductStoreId == productStore.ProductStoreId);

                if (localProductStore != null)
                {
                    // Update inventory count
                    localProductStore.InventoryCount -= quantity;

                    // Check if store allows negative inventory
                    var store = await localContext.Stores.FindAsync(localProductStore.StoreId);
                    if (store != null && !store.AllowNegativeInventory && localProductStore.InventoryCount < 0)
                    {
                        localProductStore.InventoryCount = 0;
                    }

                    // Save changes to local database
                    await localContext.SaveChangesAsync();

                    //TODO: Sync with remote database
                    //Create PendingSyncItem for inventory update

                    //var pendingSyncItem = new PendingSyncItem
                    //{
                    //    EntityType = "ProductStore",
                    //    EntityId = productStore.ProductStoreId,
                    //    CreatedAt = CustomTimeProvider.Now,
                    //    IsSynced = false,
                    //    OperationType = "Update",
                    //    SyncError = "" // Initialize with empty string to satisfy NOT NULL constraint
                    //};

                    // // Add to PendingSyncItem for syncing
                    // localContext.PendingSyncItems.Add(pendingSyncItem);
                    // await localContext.SaveChangesAsync();
                }
            }
        }
        catch (Exception ex)
        {
            _logger.Error(ex, "Error updating inventory");
            throw; // Re-throw the exception to be handled by the caller
        }
    }

    /// <summary>
    /// Displays a confirmation dialog before proceeding with the transaction.
    /// </summary>
    /// <returns>Returns true if the user confirms, otherwise false.</returns>
    private bool ConfirmTransaction()
    {
        var dialog = MessageBox.Show("Confirmed?", "Alert", MessageBoxButton.YesNo, MessageBoxImage.Question);
        return dialog == MessageBoxResult.Yes;
    }

    /// <summary>
    /// Creates a new Sale object with the current date, shift, and total value.
    /// </summary>
    /// <returns>Returns a newly created Sale object.</returns>
    private Sale CreateSale()
    {
        return new Sale
        {
            Date = CustomTimeProvider.Now,
            ShiftId = (int)UiShift.ShiftId,
            TotalValue = (DataContext as MainViewModel).TotalAmount,
            ProductStoreSales = new List<ProductStoreSale>()
        };
    }

    /// <summary>
    /// Retrieves a ProductStore entity based on the provided PLU and Store ID.
    /// </summary>
    /// <param name="context">The database context.</param>
    /// <param name="plu">The product lookup code.</param>
    /// <param name="storeId">The store ID where the product is located.</param>
    /// <returns>Returns the matching ProductStore entity.</returns>
    private ProductStore GetProductStore(string barcode, int storeId)
    {
        using (LocalData.LocalContext context = new LocalData.LocalContext())
        {
            return context.ProductStores.Include(p => p.Product)
                .First(p => p.Product.Barcode == barcode && p.StoreId == storeId);
        }
    }

    /// <summary>
    /// Creates a ProductStoreSale entry based on the given product details.
    /// </summary>
    /// <param name="item">The product item from the UI.</param>
    /// <param name="productStoreId">The ID of the associated ProductStore.</param>
    /// <returns>Returns a ProductStoreSale object.</returns>
    private ProductStoreSale CreateProductStoreSale(WPF.Product item, int productStoreId)
    {
        return new ProductStoreSale
        {
            ProductStoreId = productStoreId,
            Quantity = item.Quantity,
            Total = item.Total,
            PriceOverriden = item.IsPriceOverridden,
            StaffDiscount = item.IsStaffDiscount,
            StaffDiscountedAmount = item.StaffDiscountedAmount,
            OverriddenDiscountAmount = item.OverriddenDiscountAmount,
            IsPromotion = item.Promition is Promotion,
            PromotionId = item.Promition?.Id,
            IsNonBrandPromotion = item.NonBrandPromotion is NonBrandPromotion,
            NonBrandPromotionId = item.NonBrandPromotion?.Id

        };
    }


    /// <summary>
    /// Determines the type of price reduction applied to a sale item.
    /// </summary>
    /// <param name="sale">The ProductStoreSale object.</param>
    /// <returns>Returns the corresponding ProductPriceReductionType.</returns>
    private ProductPriceReductionType DetermineReductionType(ProductStoreSale sale)
    {
        if (sale.PriceOverriden) return ProductPriceReductionType.PriceOverride;
        if (sale.StaffDiscount) return ProductPriceReductionType.StaffDiscount;
        if (sale.IsPromotion) return ProductPriceReductionType.Promotion;
        if (sale.IsNonBrandPromotion) return ProductPriceReductionType.NonBrandPromotion;
        return ProductPriceReductionType.None;
    }

    /// <summary>
    /// Calculates the reduced amount based on discounts or overridden prices.
    /// </summary>
    /// <param name="sale">The ProductStoreSale object.</param>
    /// <returns>Returns the reduced amount as a decimal.</returns>
    private decimal GetReducedAmount(ProductStoreSale sale)
    {
        return sale.PriceOverriden ? (decimal)sale.OverriddenDiscountAmount :
               sale.StaffDiscount ? sale.StaffDiscountedAmount :
               0;
    }

    /// <summary>
    /// Opens a payment selection dialog and retrieves the chosen payment method.
    /// </summary>
    /// <param name="context">The database context.</param>
    /// <returns>Returns the selected PaymentMethod object, or null if no selection was made.</returns>

    /// <summary>
    /// Processes the payment based on the selected payment method.
    /// </summary>
    /// <param name="context">The database context.</param>
    /// <param name="sale">The Sale object containing transaction details.</param>
    /// <param name="products">The list of products included in the sale.</param>
    /// <param name="productStoreSales">The list of ProductStoreSales associated with the sale.</param>
    /// <param name="paymentMethod">The selected payment method (Cash, Card, etc.).</param>
    private void ProcessPayment(Sale sale, List<Printer.Product> products, List<ProductStoreSale> productStoreSales, List<SpecialProductSale> specialProductSales, List<int> paymentMethodIds, decimal totalAmount)
    {
        using (LocalData.LocalContext context = new LocalData.LocalContext())
        {
            var paymentMethods = context.PaymentMethods.Where(p => paymentMethodIds.Contains(p.Id)).ToList();
            if (paymentMethods.Count == 1)
            {
                if (paymentMethods[0].Method == "Cash")
                    HandleCashPayment(sale, products, productStoreSales, specialProductSales);
                else if (paymentMethods[0].Method == "Card")
                    HandleCardPayment(context, sale, products, productStoreSales, specialProductSales, totalAmount); //TODO: temp card
                else
                    HandleOtherPayments(sale, products, productStoreSales, specialProductSales);
            }
            else
            {
                HandleCashAndOtherPayments(context, sale, products, productStoreSales, specialProductSales);
            }
        }


    }


    private void HandleCashAndOtherPayments(LocalData.LocalContext contxt, Sale sale, List<Printer.Product> products, List<ProductStoreSale> productStoreSales, List<SpecialProductSale> specialProductSales)
    {

        TransactionInfo transactionInfo = null;

        if (sale.SalePaymentMethods.Count == 2
            && sale.SalePaymentMethods.ToList()[1].PaymentMethodId == 1)
        {

            Application.Current.Dispatcher.Invoke(() =>
            {
                try
                {
                    transactionInfo = ProcessCardPaymentTransaction(contxt, sale, products, productStoreSales, specialProductSales, sale.SalePaymentMethods.ToList()[1].Amount);

                }
                catch (Exception ex)
                {
                    _logger.Error(ex, "Error processing payment.");
                    MessageBox.Show("Error processing payment.", "Error", MessageBoxButton.OK, MessageBoxImage.Error);
                    return;
                }
            });
        }


        try
        {
            ProcessPayment(sale, productStoreSales, specialProductSales);
        }
        catch (Exception ex)
        {
            _logger.Error(ex, "Error processing payment.");
            MessageBox.Show("Error processing payment.", "Error", MessageBoxButton.OK, MessageBoxImage.Error);
            return;
        }

        List<string> cashierAlerts = GetCashierAlerts(sale.SaleId);

        Printer.PaymentType? paymentType2 = sale.SalePaymentMethods.Last().PaymentMethodId switch
        {
            1 => Printer.PaymentType.Card,
            3 => Printer.PaymentType.Voucher,
            4 => Printer.PaymentType.Deliveroo,
            5 => Printer.PaymentType.Uber,
            6 => Printer.PaymentType.PPCard,
            _ => null
        };




        // Call the common print method
        PrintBill(sale, products, productStoreSales, specialProductSales, Printer.PaymentType.Cash, paymentType2, sale.CashTendered.ToString(), "0", cashierAlerts, transactionInfo?.CustomerReceipt ?? null);
    }


    //TODO: Include special product sales
    private void HandleCardPayment(LocalData.LocalContext contxt, Sale sale, List<Printer.Product> products, List<ProductStoreSale> productStoreSales, List<SpecialProductSale> specialProductSales, decimal totalAmount)
    {
        MessageBoxResult dialogResult = MessageBoxResult.No;
        TransactionInfo transactionInfo = null;

        // Execute UI operations on the UI thread
        Application.Current.Dispatcher.Invoke(() =>
        {
            //show a window to ask for cash back



            try
            {
                transactionInfo = ProcessCardPaymentTransaction(contxt, sale, products, productStoreSales, specialProductSales, totalAmount);
            }
            catch (Exception ex)
            {

            }
        });

        try
        {

            // Continue with payment processing

            if (transactionInfo != null)
            {
                sale.Cashback = (decimal)transactionInfo.CashbackAmount / 100;
                ProcessPayment(sale, productStoreSales, specialProductSales);
                List<string> cashierAlerts = GetCashierAlerts(sale.SaleId);
                PrintBill(sale, products, productStoreSales, specialProductSales, Printer.PaymentType.Card, null, totalAmount.ToString(), sale.Cashback.ToString(), cashierAlerts, transactionInfo.CustomerReceipt);
            }
        }
        catch (Exception ex)
        {

        }

    }

    private TransactionInfo ProcessCardPaymentTransaction(LocalData.LocalContext contxt, Sale sale, List<Printer.Product> products, List<ProductStoreSale> productStoreSales, List<SpecialProductSale> specialProductSales, decimal totalAmount)
    {
        File.WriteAllText("errors.txt", "");
        int amountInMinorUnits = (int)(totalAmount * 100);
        var tillInfo = new TillInformation();
        var transactionHook = new TransactionHook();
        var transactionInfo = new TransactionInfo();

        string address = ConfigurationHelper.Address;
        var addresslines = address.Split(',').ToList();




        try { tillInfo.MerchantName = addresslines[0]; } catch(Exception ex) { }
        try {tillInfo.TillNumber = "POS1"; } catch (Exception ex) { }
        try {tillInfo.Address1 = addresslines[1]; } catch (Exception ex) { }
        try {tillInfo.Address2 = addresslines[2]; } catch (Exception ex) { }
        try {tillInfo.Address3 = addresslines[3]; } catch (Exception ex) { }
        try { tillInfo.PhoneNumber = addresslines[4]; }  catch (Exception ex) { }

        var isTransactionSuccessful = transactionHook.Process(
        TRANSACTIONHOOK_TRANSACTIONTYPE.INT_TT_SALE,
        amountInMinorUnits,
        ref tillInfo,
        ref transactionInfo
        );

        if (isTransactionSuccessful)
        {
            ObjectDumper.DumpToFile(transactionInfo, "errors.txt");
            return transactionInfo;
        }
        else
        {
            return null;
        }


    }

    // Helper method to format the customer receipt
    private List<string> FormatCustomerReceipt(string receipt)
    {
        if (string.IsNullOrEmpty(receipt))
            return new List<string>();

        // Split the receipt by line breaks and return as a list
        return receipt.Split(new[] { "\r\n", "\r", "\n" }, StringSplitOptions.None)
            .Where(line => !string.IsNullOrWhiteSpace(line))
            .ToList();
    }

    private void HandleCashPayment(Sale sale, List<Printer.Product> products, List<ProductStoreSale> productStoreSales, List<SpecialProductSale> specialProductSales)
    {

        try
        {
            ProcessPayment(sale, productStoreSales, specialProductSales);
        }
        catch (Exception ex)
        {
            _logger.Error(ex, "Error processing payment.");
            MessageBox.Show("Error processing payment.", "Error", MessageBoxButton.OK, MessageBoxImage.Error);
            return;
        }

        List<string> cashierAlerts = GetCashierAlerts(sale.SaleId);

        // Call the common print method
        PrintBill(sale, products, productStoreSales, specialProductSales, Printer.PaymentType.Cash, null, sale.CashTendered.ToString(), (sale.CashTendered - sale.TotalValue).ToString(), cashierAlerts);
    }

    private void HandleOtherPayments(Sale sale, List<Printer.Product> products, List<ProductStoreSale> productStoreSales, List<SpecialProductSale> specialProductSales)
    {
        try
        {
            ProcessPayment(sale, productStoreSales, specialProductSales);
        }
        catch (Exception ex)
        {
            _logger.Error(ex, "Error processing payment.");
            MessageBox.Show("Error processing payment.", "Error", MessageBoxButton.OK, MessageBoxImage.Error);
            return;
        }

        Printer.PaymentType? paymentType = sale.SalePaymentMethods.Last().PaymentMethodId switch
        {
            1 => Printer.PaymentType.Card,
            3 => Printer.PaymentType.Voucher,
            4 => Printer.PaymentType.Deliveroo,
            5 => Printer.PaymentType.Uber,
            6 => Printer.PaymentType.PPCard,
            _ => null
        };


        List<string> cashierAlerts = GetCashierAlerts(sale.SaleId);

        // Call the common print method
        PrintBill(sale, products, productStoreSales, specialProductSales, (Printer.PaymentType)paymentType, null, "0", "0", cashierAlerts);
    }

    // Extracted common printing method
    private void PrintBill(Sale sale, List<Printer.Product> products, List<ProductStoreSale> productStoreSales
       , List<SpecialProductSale> specialProductSales, Printer.PaymentType paymentType1, Printer.PaymentType? paymentType2, string paidAmount, string changeDue, List<string> cashierAlerts = null, string cardCustomerReceipt = null)
    {
        using (LocalData.LocalContext context = new LocalData.LocalContext())
        {
            POS.Printer.PrintData printData = new Printer.PrintData(paymentType1, paymentType2)
            {
                StoreId = UiShift.StoreId.ToString(),
                Time = sale.Date.ToString(),
                CashierName = UiShift.CashierName,
                TransactionId = sale.SaleId.ToString(),
                PaidAmount = paidAmount,
                ChangeDue = (double.TryParse(changeDue, out double changeDueDouble) && changeDueDouble < 0) ? "0" : changeDue,
                Products = products,
                Total = sale.TotalValue.ToString(),
                VatNo = context.Stores.Find(UiShift.StoreId).VatNo,
                Barcode = sale.Barcode,
                StoreName = context.Stores.Find(UiShift.StoreId).StoreName,
                PosMachineId = UiShift.PosMachineId.ToString(),
                CashierAlerts = cashierAlerts ?? new List<string>(),
                VatEntries = GenerateVatEntries(productStoreSales, specialProductSales, sale),
                Type2Amount = sale.TotalValue - decimal.Parse(paidAmount),
                CustomerReceipt = string.IsNullOrEmpty(cardCustomerReceipt) ? null : FormatCustomerReceipt(cardCustomerReceipt)
            };

            POS.Printer.Printer.Print(printData, ConfigurationHelper.Address, ConfigurationHelper.PrinterName);
        }
    }



    private bool PromptForPayment(Sale sale)
    {
        var paidAmountWindow = new PaidAmount((DataContext as MainViewModel).TotalAmount);
        paidAmountWindow.ShowDialog();

        if (PaidAmount.Cancelled) return false;

        sale.CashTendered = PaidAmount.Amount;
        return true;
    }

    // Extracted: Processing payment
    private void ProcessPayment(Sale sale, List<ProductStoreSale> productStoreSales, List<SpecialProductSale> specialProductSales = null)
    {
        try
        {
            using (LocalData.LocalContext context = new LocalData.LocalContext())
            {
                // Set sale properties
                sale.ProductStoreSales = productStoreSales;
                sale.SpecialProductSales = specialProductSales;
                sale.LastModified = CustomTimeProvider.Now;
                sale.IsDeleted = false;

                foreach (var specialProductSale in sale.SpecialProductSales)
                {
                    specialProductSale.Product = null;

                }


                // Add ProductStoreSales to context

                // Save to local database
                context.Sales.Add(sale);
                context.SaveChanges();

                // Generate barcode
                sale.Barcode = sale.SaleId.ToString("D12");
                context.SaveChanges();

                // STEP 1: Create PendingSyncItem for Sale (parent entity) FIRST
                // Temporarily clear navigation properties to avoid circular references
                var originalProductStoreSales = sale.ProductStoreSales;
                var originalSpecialProductSales = sale.SpecialProductSales;
                var originalSalePaymentMethods = sale.SalePaymentMethods;
                sale.ProductStoreSales = null;
                sale.SpecialProductSales = null;
                sale.SalePaymentMethods = null;

                AddPendingSyncItem(context, "Sale", sale.SaleId, "Insert");
                context.SaveChanges(); // Save to ensure Sale is processed first

                // Restore navigation properties


                // STEP 2: Create PendingSyncItem for each ProductStoreSale (child entities) AFTER
                foreach (var productStoreSale in originalProductStoreSales)
                {
                    productStoreSale.ProductStore = null;
                    productStoreSale.Promotion = null;
                    productStoreSale.NonBrandPromotion = null;
                    productStoreSale.Sale = null;

                    AddPendingSyncItem(context, "ProductStoreSale", productStoreSale.ProductStoreSaleId, "Insert");

                    context.ProductStoreSales.Add(productStoreSale);

                }

                // STEP 3: Create PendingSyncItem for each SpecialProductSale (child entities) LAST
                if (originalSpecialProductSales != null)
                {
                    foreach (var specialProductSale in originalSpecialProductSales)
                    {
                        specialProductSale.Product = null;
                        specialProductSale.Sale = null;

                        AddPendingSyncItem(context, "SpecialProductSale", specialProductSale.Id, "Insert");
                        context.SpecialProductSales.Add(specialProductSale);
                    }
                }

                // STEP 4: Create PendingSyncItem for each SalePaymentMethod2 (payment method entities)
                if (originalSalePaymentMethods != null)
                {
                    foreach (var paymentMethod in originalSalePaymentMethods)
                    {
                        // Temporarily clear navigation properties
                        paymentMethod.Sale = null;
                        paymentMethod.PaymentMethod = null;


                        AddPendingSyncItem(context, "SalePaymentMethod2", paymentMethod.Id, "Insert");
                        context.SalePaymentMethods.Add(paymentMethod);
                    }
                }

                // Save all changes
                context.SaveChanges();
            }
        }
        catch (Exception ex)
        {
            LogError(ex);
        }
    }

    private void AddPendingSyncItem(DbContext context, string entityType, int entityId, string operationType)
    {
        var pendingSyncItem = new PendingSyncItem
        {
            EntityType = entityType,
            EntityId = entityId,
            CreatedAt = CustomTimeProvider.Now,
            IsSynced = false,
            OperationType = operationType,
            SyncError = ""
        };
        context.Set<PendingSyncItem>().Add(pendingSyncItem);
    }

    // Extracted: Get cashier alerts
    private List<string> GetCashierAlerts(int saleId)
    {
        using (LocalData.LocalContext context = new LocalData.LocalContext())
        {
            return context.ProductStoreSales
                .Where(pss => pss.SaleId == saleId)
                .Select(pss => pss.ProductStore.Product.Division.SubCatagory.CashierAlert)
                .Where(alert => !string.IsNullOrEmpty(alert))
                .Distinct()
                .ToList();
        }
    }


    // Placeholder for logging errors
    private void LogError(Exception ex)
    {
        // Implement actual logging mechanism
        Console.WriteLine(ex.Message);
    }


    //TODO : add special product sales
    public static List<VatEntry> GenerateVatEntries(List<ProductStoreSale> productStoreSales, List<SpecialProductSale> specialProductSales, Sale sale)
    {
        using (LocalData.LocalContext dbContext = new LocalData.LocalContext())
        {
            // Process ProductStoreSales
            var productEntries = productStoreSales.Select(pss =>
            {
                var productStore = dbContext.ProductStores.First(ps => ps.ProductStoreId == pss.ProductStoreId);
                var product = dbContext.Products.First(p => p.Id == productStore.ProductId);
                var vatRate = dbContext.Vats.First(v => v.Id == product.VatId);

                decimal totalPrice = pss.Total; // Use the individual sale item's total
                decimal vatValue = vatRate.Value / 100m;
                decimal exVat = totalPrice / (1 + vatValue);
                decimal vatAmount = totalPrice - exVat;

                return new
                {
                    Code = vatRate.Code,
                    Rate = vatRate.Rate,
                    ExVat = Math.Round(exVat, 2),
                    VatAmount = Math.Round(vatAmount, 2),
                    TotalPrice = Math.Round(totalPrice, 2)
                };
            });

            // Process SpecialProductSales
            var specialEntries = specialProductSales.Select(sps =>
            {
                var product = dbContext.Products.First(p => p.Id == sps.ProductId);
                var vatRate = dbContext.Vats.First(v => v.Id == product.VatId);

                decimal totalPrice = sps.Amount; // Use the special product sale amount
                decimal vatValue = vatRate.Value / 100m;
                decimal exVat = totalPrice / (1 + vatValue);
                decimal vatAmount = totalPrice - exVat;

                return new
                {
                    Code = vatRate.Code,
                    Rate = vatRate.Rate,
                    ExVat = Math.Round(exVat, 2),
                    VatAmount = Math.Round(vatAmount, 2),
                    TotalPrice = Math.Round(totalPrice, 2)
                };
            });

            // Combine and group results
            return productEntries
                .Concat(specialEntries)
                .GroupBy(x => x.Code)
                .Select(g => new VatEntry(
                    g.Key,
                    g.First().Rate,
                    g.Sum(x => x.ExVat),
                    g.Sum(x => x.VatAmount),
                    g.Sum(x => x.TotalPrice)
                ))
                .ToList();
        }
    }



    //safe drop
    private void Button_Click_4(object sender, RoutedEventArgs e)
    {
        Views.SafeDrop safeDrop = new Views.SafeDrop();
        safeDrop.Owner = this;
        safeDrop.ShowDialog();


        if (POS.WPF.Views.SafeDrop.Cancelled) return;

        using (LocalData.LocalContext context = new LocalData.LocalContext())
        {
            if (POS.WPF.Views.SafeDrop.Amount == null) return;

            // Create the safe drop entity
            var entity = new POS.Core.Models.SafeDrop()
            {
                Amount = POS.WPF.Views.SafeDrop.Amount.Value,
                Time = CustomTimeProvider.Now,
                StoreId = (int)UiShift.StoreId,
                shiftId = (int)UiShift.ShiftId,
                IsDeleted = false,
                LastModified = CustomTimeProvider.Now
            };

            // Save to local database
            context.SafeDrops.Add(entity);
            context.SaveChanges();

            // Create PendingSyncItem for syncing
            var pendingSyncItem = new PendingSyncItem
            {
                EntityType = "SafeDrop",
                EntityId = entity.Id,
                CreatedAt = CustomTimeProvider.Now,
                IsSynced = false,
                OperationType = "Insert",
                SyncedAt = TimeManagement.CustomTimeProvider.Now,
                SyncError = ""
            };

            // Add to PendingSyncItem for syncing
            context.PendingSyncItems.Add(pendingSyncItem);
            context.SaveChanges();

            // Get cashier name for printing
            var cashierName = context.Cashiers.Find(UiShift.CashierId)?.Name ?? "Unknown";

            // Print receipt
            Printer.SafeDropPrinter.Print(UiShift.StoreId.ToString(), UiShift.PosMachineId.ToString()
                , DisplayTimeProvider.ConvertToDisplayTime(entity.Time).ToString(), entity.Amount.ToString(), cashierName, entity.Id.ToString(), ConfigurationHelper.PrinterName);
        }

        barcodeTextBox.Visibility = Visibility.Visible;
        barcodeTextBox.Focus();
    }


    /// <summary>
    /// validation : already one of discount added
    /// 
    /// get discount amount from Separate window
    /// set products OverriddenPrice 
    /// set IsPriceOverridden flag
    /// set OverriddenDiscountAmount
    /// </summary>
    /// <param name="sender"></param>
    /// <param name="e"></param>
    private void Button_Click_5(object sender, RoutedEventArgs e)
    {
        this.IsPriceOverride = true;

        if (_selectedProduct != null)
        {
            if (_selectedProduct.IsPriceOverridden == true || _selectedProduct.IsStaffDiscount == true || _selectedProduct.Promition != null)
            {
                MessageBox.Show("This product is already discounted!");
                return;
            }


            decimal amount = _selectedProduct.Total;

            PriceOverride priceOverride = new PriceOverride(amount);
            priceOverride.ShowDialog();

            if (PriceOverride.Amount != 0)
            {
                var product = (DataContext as MainViewModel).Products
                    .First(p => p.Barcode == _selectedProduct.Barcode);
                product.OverriddenPrice = PriceOverride.Amount;
                product.IsPriceOverridden = true;
                product.Note = "Price Override";
                product.OverriddenDiscountAmount = product.UnitPrice * product.Quantity - PriceOverride.Amount;

                // Force totals update
                (DataContext as MainViewModel).UpdateTotals();
            }
        }

        barcodeTextBox.Visibility = Visibility.Visible;
        barcodeTextBox.Focus();

    }

    private void OnProductCellClick(object sender, MouseButtonEventArgs e)
    {
        // Find the DataGridRow associated with the clicked cell
        var stackPanel = sender as StackPanel;
        if (stackPanel == null) return;

        var dataGridRow = ItemsControl.ContainerFromElement(MyDataGrid, stackPanel) as DataGridRow;
        if (dataGridRow == null) return;

        // Select the row in the DataGrid
        MyDataGrid.SelectedItem = dataGridRow.DataContext;

        // Execute custom logic
        _selectedProduct = dataGridRow.DataContext as Product; // Cast to your model type if needed
                                                               // Example: Debug.WriteLine($"Selected item: {selectedItem}");
    }


    //on hold list
    private void Button_Click_6(object sender, RoutedEventArgs e)
    {
        using (LocalData.LocalContext context = new LocalData.LocalContext())
        {
            // Get on-hold sales from local database
            var OnHoldSales = context.Sales.Where(s => s.IsOnHold == true).ToList();

            // Show on-hold list dialog
            OnHoldList onHoldList = new OnHoldList(OnHoldSales);
            onHoldList.ShowDialog();

            if (OnHoldList.SelectedSale != null)
            {
                // Clear current products in UI
                (DataContext as MainViewModel).Products.Clear();

                // Get product store sales for the selected sale
                var productStoreSales = context.ProductStoreSales
                    .Include(pss => pss.ProductStore).ThenInclude(ps => ps.Product)
                    .Where(p => p.SaleId == OnHoldList.SelectedSale.SaleId)
                    .ToList();

                // Add products to UI
                foreach (var productStoreSale in productStoreSales)
                {
                    WPF.Product product = new WPF.Product();
                    product.ProductName = productStoreSale.ProductStore.Product.Name;
                    product.Barcode = productStoreSale.ProductStore.Product.Barcode;
                    product.UnitPrice = productStoreSale.ProductStore.Product.SellingPrice;
                    product.Quantity = productStoreSale.Quantity;
                    product.IsPriceOverridden = productStoreSale.PriceOverriden;

                    if (productStoreSale.PriceOverriden == true)
                    {
                        product.OverriddenPrice = productStoreSale.Total;
                    }
                    else
                    {
                        product.UpdateTotal();
                    }

                    (DataContext as MainViewModel).Products.Add(product);
                }


                //TODO : Sync
                //// Create PendingSyncItem for the sale being removed from on-hold
                //var pendingSyncItem = new PendingSyncItem
                //{
                //    EntityType = "Sale",
                //    EntityId = OnHoldList.SelectedSale.SaleId,
                //    CreatedAt = CustomTimeProvider.Now,
                //    IsSynced = false,
                //    OperationType = "Delete", // Update to change IsOnHold status
                //    SyncError = ""
                //};

                //// Add to PendingSyncItem for syncing
                //context.PendingSyncItems.Add(pendingSyncItem);

                //// Delete existing sale and set on hold to false
                //var filteredSales = context.ProductStoreSales.Where(pss => pss.SaleId == OnHoldList.SelectedSale.SaleId);
                //context.ProductStoreSales.RemoveRange(filteredSales);
                //context.Sales.Remove(OnHoldList.SelectedSale);

                // Save all changes
                context.SaveChanges();
            }
        }

        barcodeTextBox.Visibility = Visibility.Visible;
        barcodeTextBox.Focus();
    }


    private void InitializeQuickAccessButtons()
    {
        //try
        //{
        //    using (var context = new LocalData.LocalContext())
        //    {
        //        // Get the current store ID
        //        int storeId = (int)UiShift.StoreId;

        //        // Get quick access buttons for this store
        //        var quickAccessButtons = context.QuickAccessButtons
        //            .Include(b => b.Product)
        //            .Where(b => b.StoreId == storeId)
        //            .ToList();

        //        // Clear existing buttons
        //        quickAccessButtonsPanel.Children.Clear();

        //        // Add buttons dynamically
        //        foreach (var buttonConfig in quickAccessButtons)
        //        {
        //            Button button = new Button
        //            {
        //                Background = new SolidColorBrush((Color)ColorConverter.ConvertFromString("#004e7c")),
        //                Margin = new Thickness(5),
        //                Tag = buttonConfig.Product.PLU // Store PLU in Tag for later use
        //            };

        //            StackPanel stackPanel = new StackPanel
        //            {
        //                Orientation = Orientation.Vertical,
        //                HorizontalAlignment = HorizontalAlignment.Center
        //            };

        //            Image image = new Image
        //            {
        //                Source = new BitmapImage(new Uri("/Images/shopping.png", UriKind.Relative)),
        //                Width = 30,
        //                Height = 30
        //            };

        //            TextBlock textBlock = new TextBlock
        //            {
        //                Text = buttonConfig.ButtonText,
        //                Foreground = new SolidColorBrush(Colors.White),
        //                FontWeight = FontWeights.Bold,
        //                TextAlignment = TextAlignment.Center,
        //                TextWrapping = TextWrapping.Wrap
        //            };

        //            stackPanel.Children.Add(image);
        //            stackPanel.Children.Add(textBlock);
        //            button.Content = stackPanel;

        //            // Add click handler
        //            button.Click += QuickAccessButton_Click;

        //            // Add to panel
        //            quickAccessButtonsPanel.Children.Add(button);
        //        }
        //    }
        //}
        //catch (Exception ex)
        //{
        //    _logger.Error(ex, "Error loading quick access buttons");
        //    MessageBox.Show("Failed to load quick access buttons: " + ex.Message);
        //}
    }

    private void QuickAccessButton_Click(object sender, RoutedEventArgs e)
    {
        Button button = sender as Button;
        if (button != null && button.Tag != null)
        {
            string barcode = button.Tag.ToString();
            txtInput.Text = barcode;

            // Trigger the same logic as if the user had entered the PLU
            ProcessBarcode(barcode);

            barcodeTextBox.Visibility = Visibility.Visible;
            barcodeTextBox.Focus();
        }
    }

    //shift end
    private void Button_Click_7(object sender, RoutedEventArgs e)
    {
        try
        {
            var dialog = MessageBox.Show("Confirmed?", "Alert", MessageBoxButton.YesNo, MessageBoxImage.Question);
            if (dialog == MessageBoxResult.No) return;

            using (LocalData.LocalContext context = new LocalData.LocalContext())
            {
                // Update shift in local database
                var shift = context.Shifts.FirstOrDefault(s => s.Id == UiShift.ShiftId);
                if (shift != null)
                {
                    shift.LastModified = CustomTimeProvider.Now;
                    shift.EndTime = CustomTimeProvider.Now;
                    shift.InProgress = false;

                    // Create PendingSyncItem for shift update
                    var shiftPendingSyncItem = new PendingSyncItem
                    {
                        EntityType = "Shift",
                        EntityId = (int)UiShift.ShiftId,
                        CreatedAt = CustomTimeProvider.Now,
                        IsSynced = false,
                        OperationType = "Update",
                        SyncError = ""
                    };

                    // Add to PendingSyncItem for syncing
                    context.PendingSyncItems.Add(shiftPendingSyncItem);
                }

                // Save changes
                context.SaveChanges();

                // Get subcategory summaries from local database
                var result = context.ProductStoreSales.Include(pss => pss.Sale)
                    .Where(pss => pss.Sale.ShiftId == UiShift.ShiftId)
                    .GroupBy(pss => pss.ProductStore.Product.Division.SubCatagory.Name)
                    .Select(g => new SubCategorySummary
                    {
                        SubCategoryName = g.Key,
                        Quantity = g.Sum(pss => pss.Quantity),
                        Total = (decimal)g.Sum(pss => (double)pss.Total) // Convert to double for SQLite aggregation
                    })
                    .ToList();

                // Get special product sales from local database
                var groupedSpecialProductSales = context.SpecialProductSales
                    .Include(sps => sps.Product)
                    .Include(sps => sps.Sale)
                    .Where(sps => sps.Sale.Date >= shift.StartTime && sps.Sale.Date <= shift.EndTime)
                    .AsEnumerable() // Switch to LINQ to Objects
                    .GroupBy(sps => sps.Product.Name)
                    .Select(g => new SpecialProductSaleDetails
                    {
                        ProductName = g.Key,
                        Amount = g.Sum(sps => sps.Amount)
                    })
                    .ToList();

                // Add debug message to check if data is being retrieved
                if (groupedSpecialProductSales.Count > 0)
                {
                    Console.WriteLine($"Found {groupedSpecialProductSales.Count} special product sales");
                }
                else
                {
                    Console.WriteLine("No special product sales found");
                }

                foreach (var s in groupedSpecialProductSales)
                {
                    result.Add(new SubCategorySummary() { SubCategoryName = s.ProductName, Quantity = 0, Total = s.Amount });
                }

                // Get manual discounts from local database
                var manualDiscounts = context.ProductStoreSales
                    .Include(pss => pss.Sale)
                    .Include(pss => pss.Promotion)
                    .Include(pss => pss.NonBrandPromotion)
                    .Where(pss => pss.Sale.ShiftId == UiShift.ShiftId && pss.PriceOverriden)
                    .Select(pss => new ManualDiscountDetails
                    {
                        PLU = pss.ProductStore.Product.PLU,
                        ProductName = pss.ProductStore.Product.Name,
                        Quantity = pss.Quantity,
                        DiscountAmount = pss.OverriddenDiscountAmount
                    })
                    .ToList();

                // Get staff discounts from local database
                var staffDiscounts = context.ProductStoreSales
                    .Include(pss => pss.Sale)
                    .Include(pss => pss.Promotion)
                     .Include(pss => pss.NonBrandPromotion)
                    .Where(pss => pss.Sale.ShiftId == UiShift.ShiftId && pss.StaffDiscount)
                    .Select(pss => new StaffDiscountDetails
                    {
                        PLU = pss.ProductStore.Product.PLU,
                        ProductName = pss.ProductStore.Product.Name,
                        Quantity = pss.Quantity,
                        DiscountAmount = pss.StaffDiscountedAmount
                    })
                    .ToList();

                // Get promotions from local database
                var promotions = GetPromotionSalesForShift((int)UiShift.ShiftId);
                var nonBrandPromotionsSalesReport = GetNonBrandPromotionSalesForShift((int)UiShift.ShiftId);


                // 1. Project each sale along with its refund total via a correlated subquery
                // Step 1: Pull sales and their related data
                var sales = context.ProductStoreSales
                    .Where(pss => pss.Sale.ShiftId == UiShift.ShiftId)
                    .Include(pss => pss.Sale)
                    .Include(pss => pss.ProductStore)
                        .ThenInclude(ps => ps.Product)
                            .ThenInclude(p => p.Vat)
                    .ToList(); // Load all data into memory

                // Step 2: Pull all refunds for current shift's sales
                var saleIds = sales.Select(s => s.ProductStoreSaleId).ToList();

                // Load all relevant refunds into memory

                // Step 3: Calculate RefundTotal in memory and project
                var salesByVat = sales.Select(sale => new
                {
                    Sale = sale,

                });



                // 2. Group by VAT code & rate, and do your VAT math
                var vatSummary = salesByVat
                    .GroupBy(x => new
                    {
                        Code = x.Sale.ProductStore.Product.Vat.Code,
                        Value = x.Sale.ProductStore.Product.Vat.Value
                    })
                    .Select(g => new VatSummary
                    {
                        VatRate = g.Key.Code,

                        // total including VAT minus refunds
                        IncVat = g.Sum(x => x.Sale.Total),

                        // strip out VAT from the net (sale minus refunds)
                        ExVat = g.Sum(x =>
                        {
                            var net = x.Sale.Total;
                            return net / (1 + (g.Key.Value / 100));
                        }),

                        // the VAT component of that net
                        VatAmount = g.Sum(x =>
                        {
                            var net = x.Sale.Total;
                            return net - (net / (1 + (g.Key.Value / 100)));
                        })
                    })
                    .ToList();


                // Add VAT for special product sales
                var specialProductVat = context.SpecialProductSales
                    .Include(sps => sps.Sale)
                    .Include(sps => sps.Product)
                    .ThenInclude(p => p.Vat)
                    .Where(sps => sps.Sale.ShiftId == UiShift.ShiftId)
                    .AsEnumerable() // Switch to LINQ to Objects
                    .GroupBy(sps => new { Code = sps.Product.Vat.Code, Rate = sps.Product.Vat.Rate })
                    .Select(g => new VatSummary
                    {
                        VatRate = g.Key.Code,
                        ExVat = g.Sum(sps => sps.Amount / (1 + (sps.Product.Vat.Value / 100))),
                        VatAmount = g.Sum(sps => sps.Amount - (sps.Amount / (1 + (sps.Product.Vat.Value / 100)))),
                        IncVat = g.Sum(sps => sps.Amount)
                    })
                    .ToList();

                // Combine regular and special product VAT summaries
                foreach (var spVat in specialProductVat)
                {
                    var existingVat = vatSummary.FirstOrDefault(v => v.VatRate == spVat.VatRate);
                    if (existingVat != null)
                    {
                        existingVat.ExVat += spVat.ExVat;
                        existingVat.VatAmount += spVat.VatAmount;
                        existingVat.IncVat += spVat.IncVat;
                    }
                    else
                    {
                        vatSummary.Add(spVat);
                    }
                }

                // Get sales in shift from local database
                var salesInShift = context.Sales
                    .Where(s => s.ShiftId == UiShift.ShiftId)
                    .Include(s => s.SalePaymentMethods)
                    .ThenInclude(spm => spm.PaymentMethod)
                    .ToList();


                var refundsInStoreDay = context.Refunds
                    .Where(r => r.Date >= shift.StartTime && r.Date <= shift.EndTime)
                    .Include(r => r.ProductStoreSale)
                    .ThenInclude(pss => pss.Sale)
                    .ThenInclude(s => s.SalePaymentMethods)
                    .ThenInclude(spm => spm.PaymentMethod)
                    .ToList();

                // Calculate refund totals by payment method
                decimal cashRefunds = 0;
                decimal cardRefunds = 0;
                decimal uberRefunds = 0;
                decimal voucherRefunds = 0;
                decimal ppCardRefunds = 0;
                decimal deliverooRefunds = 0;

                foreach (var refund in refundsInStoreDay)
                {
                    // Determine payment method of the original sale
                    var salePaymentMethods = refund.ProductStoreSale.Sale.SalePaymentMethods;

                    // If the sale had multiple payment methods, assume refund is from cash
                    if (salePaymentMethods.Count > 1)
                    {
                        cashRefunds += refund.Amount;
                        continue;
                    }

                    // Get the payment method of the sale
                    var paymentMethod = salePaymentMethods.FirstOrDefault()?.PaymentMethod?.Method ?? "Cash";

                    // Add refund amount to the appropriate payment method
                    switch (paymentMethod)
                    {
                        case "Cash":
                            cashRefunds += refund.Amount;
                            break;
                        case "Card":
                            cardRefunds += refund.Amount;
                            break;
                        case "Uber":
                            uberRefunds += refund.Amount;
                            break;
                        case "Voucher":
                            voucherRefunds += refund.Amount;
                            break;
                        case "PP Card":
                            ppCardRefunds += refund.Amount;
                            break;
                        case "Deliveroo":
                            deliverooRefunds += refund.Amount;
                            break;
                        default:
                            cashRefunds += refund.Amount; // Default to cash for unknown payment methods
                            break;
                    }
                }

                // Get payment totals and deduct refunds
                var paymentTotals = salesInShift
                    .SelectMany(s => s.SalePaymentMethods)
                    .GroupBy(spm => spm.PaymentMethod.Method)
                    .ToDictionary(g => g.Key, g => g.Sum(spm => spm.Amount));

                var cardTotal = paymentTotals.GetValueOrDefault("Card", 0) - cardRefunds;
                var cashTotal = paymentTotals.GetValueOrDefault("Cash", 0) - cashRefunds;
                var uberTotal = paymentTotals.GetValueOrDefault("Uber", 0) - uberRefunds;
                var voucherTotal = paymentTotals.GetValueOrDefault("Voucher", 0) - voucherRefunds;
                var ppCardTotal = paymentTotals.GetValueOrDefault("PP Card", 0) - ppCardRefunds;
                var deliverooTotal = paymentTotals.GetValueOrDefault("Deliveroo", 0) - deliverooRefunds;

                // Get expected cash from local database
                var totalPaidOut = context.PaidOuts
                    .Where(po => po.ShiftId == UiShift.ShiftId)
                    .Sum(po => (double)po.Amount);
                var expectedCash = cashTotal - (decimal)totalPaidOut;

                // Get safe drops from local database
                var safeDrops = context.SafeDrops
                    .Where(sd => sd.shiftId == UiShift.ShiftId)
                    .Select(sd => new SafeDropDetails
                    {
                        CashierName = sd.Shift.Cashier.Name,
                        Time = sd.Time,
                        Amount = sd.Amount
                    })
                    .ToList();

                // Get paid out summaries from local database
                var paidOutSummaries = context.PaidOuts
                    .Where(po => po.ShiftId == UiShift.ShiftId)
                    .GroupBy(po => po.PaidOutOption.Option)
                    .Select(g => new PaidOutSummary
                    {
                        PaidOutOption = g.Key,
                        Amount = (decimal)g.Sum(po => (double)po.Amount)
                    })
                    .ToList();

                var cashTaken = cashTotal - (decimal)totalPaidOut;
                var totalSafeDrops = (decimal)safeDrops.Sum(sd => (double)sd.Amount);
                var cashback = (decimal)salesInShift.Sum(s => (double)s.Cashback);
                var targetCash = cashTaken - totalSafeDrops - cashback;

                // Print day end report
                Printer.ShiftEndPrinter.Print(
                    UiShift.StoreName,
                    UiShift.ShiftId.ToString(),
                    shift.StartTime.ToShortTimeString(),
                    shift.EndTime.ToShortTimeString(),
                    UiShift.CashierName,
                    result,
                    manualDiscounts,
                    staffDiscounts,
                    promotions,
                    nonBrandPromotionsSalesReport,
                    vatSummary,
                    cardTotal,
                    cashTotal,
                    uberTotal,
                    voucherTotal,
                    ppCardTotal,
                    deliverooTotal,
                    expectedCash,
                    safeDrops,
                    paidOutSummaries,
                    ConfigurationHelper.PrinterName,
                    cashTaken,
                    totalSafeDrops,
                    cashback,
                    targetCash,
                    40,
                    ConfigurationHelper.Address,
                    groupedSpecialProductSales);

                // Reset UiShift properties
                UiShift.CashierId = null;
                UiShift.PosMachineId = null;
                UiShift.ShiftId = null;
                UiShift.CashierName = null;

                // Show login window and close current window
                LoginWindow loginWindow = new LoginWindow();
                loginWindow.Show();
                this.Close();
            }

            barcodeTextBox.Visibility = Visibility.Visible;
            barcodeTextBox.Focus();
        }
        catch (Exception ex)
        {
            _logger.Error(ex, "Error ending shift");
            MessageBox.Show($"Error ending shift: {ex.Message}", "Error", MessageBoxButton.OK, MessageBoxImage.Error);
        }
    }

    private List<NonPromotionSalesReport> GetNonBrandPromotionSalesForShift(int shiftId)
    {
        try
        {
            using (LocalData.LocalContext context = new LocalData.LocalContext())
            {
                var result = context.ProductStoreSales
.Where(pss => pss.Sale.ShiftId == shiftId && pss.IsNonBrandPromotion)
.GroupBy(pss => pss.NonBrandPromotion.Name) // Grouping by a scalar value
.Select(g => new NonPromotionSalesReport
{
    PromotionName = g.Key,
    SoldQuantity = g.Sum(pss => pss.Quantity),
    TotalDiscount = (decimal)g.Sum(pss => (double)pss.ProductStore.Product.SellingPrice * (double)pss.Quantity - (double)pss.Total)
})
.ToList();


                return result;
            }

        }
        catch (Exception ex)
        {
            MessageBox.Show(ex.Message);
            return null;
        }
    }

    public List<PromotionSalesReport> GetPromotionSalesForShift(int shiftId)
    {
        try
        {
            using (LocalData.LocalContext context = new LocalData.LocalContext())
            {
                var result = context.ProductStoreSales
.Where(pss => pss.Sale.ShiftId == shiftId && pss.IsPromotion)
.GroupBy(pss => pss.Promotion.Name) // Grouping by a scalar value
.Select(g => new PromotionSalesReport
{
    PromotionName = g.Key,
    SoldQuantity = g.Sum(pss => pss.Quantity),
    TotalDiscount = (decimal)g.Sum(pss => (double)pss.ProductStore.Product.SellingPrice * (double)pss.Quantity - (double)pss.Total)
})
.ToList();


                return result;
            }

        }
        catch (Exception ex)
        {
            MessageBox.Show(ex.Message);
            return null;
        }
    }


    //staff discount
    private void Button_Click_8(object sender, RoutedEventArgs e)
    {
        if (_selectedProduct == null) return;

        //confirm
        var dialog = MessageBox.Show("Confirmed?", "Alart", MessageBoxButton.YesNo, MessageBoxImage.Question);
        if (dialog == MessageBoxResult.Yes)
        {

            var product = (DataContext as MainViewModel).Products
                    .First(p => p.Barcode == _selectedProduct.Barcode);

            if (product.IsPriceOverridden == true || product.IsStaffDiscount == true || product.Promition != null)
            {
                MessageBox.Show("This product is already discounted!");
                return;
            }

            product.OverriddenPrice = product.Total * 80 / 100;
            product.IsStaffDiscount = true;
            product.Note = "Staff Discount";
            product.StaffDiscountedAmount = product.UnitPrice * product.Quantity * 0.2m;
        }

            (DataContext as MainViewModel).UpdateTotals();

        barcodeTextBox.Visibility = Visibility.Visible;
        barcodeTextBox.Focus();

    }


    //paid out
    private void Button_Click_9(object sender, RoutedEventArgs e)
    {
        PaidOut paidOut = new PaidOut();
        paidOut.ShowDialog();

        barcodeTextBox.Visibility = Visibility.Visible;
        barcodeTextBox.Focus();

    }

    //reprint recipt
    private void Button_Click_10(object sender, RoutedEventArgs e)
    {
        TransactionHistory transactionHistory = new TransactionHistory();
        transactionHistory.ShowDialog();

        barcodeTextBox.Visibility = Visibility.Visible;
        barcodeTextBox.Focus();

    }


    //










    private void NumericKeypad_KeyPressed(object sender, string key)
    {
        txtInput.Focus();

        switch (key)
        {
            case "Backspace":
                _inputSimulator.Keyboard.KeyPress(VirtualKeyCode.BACK);
                break;
            case "%":
                _inputSimulator.Keyboard.KeyPress(VirtualKeyCode.SHIFT, VirtualKeyCode.VK_5);
                break;
            case "00":
                _inputSimulator.Keyboard.KeyPress(VirtualKeyCode.VK_0);
                _inputSimulator.Keyboard.KeyPress(VirtualKeyCode.VK_0);
                break;
            case ".":
                _inputSimulator.Keyboard.KeyPress(VirtualKeyCode.DECIMAL);
                break;
            case "Clear":
                barcodeTextBox.Visibility = Visibility.Visible;
                barcodeTextBox.Focus();

                txtInput.Text = "";
                break;
            default:
                if (int.TryParse(key, out var number))
                {
                    var keyCode = (VirtualKeyCode)Enum.Parse(
                        typeof(VirtualKeyCode),
                        $"VK_{number}"
                    );
                    _inputSimulator.Keyboard.KeyPress(keyCode);
                }
                break;
        }


    }

    private void Button_Click_23(object sender, RoutedEventArgs e)
    {

    }

    private void Button_Click_24(object sender, RoutedEventArgs e)
    {
    }

    static (DateTime fromTime, DateTime toTime) GetTimeRange()
    {
        DateTime now = CustomTimeProvider.Now;
        DateTime today10PM = now.Date.AddHours(22); // Today at 10:00 PM

        DateTime fromTime, toTime;

        if (now >= today10PM)
        {
            // Current time is past 10 PM: FromTime is yesterday 10 PM, ToTime is today 9:59 PM
            fromTime = now.Date.AddDays(-1).AddHours(22); // Yesterday 10:00 PM
            toTime = now.Date.AddHours(21).AddMinutes(59); // Today 9:59 PM
        }
        else
        {
            // Current time is before 10 PM: FromTime is the day before yesterday 10 PM, ToTime is yesterday 9:59 PM
            fromTime = now.Date.AddDays(-2).AddHours(22); // Day before yesterday 10:00 PM
            toTime = now.Date.AddDays(-1).AddHours(21).AddMinutes(59); // Yesterday 9:59 PM
        }

        return (fromTime, toTime);
    }

    //day end
    private void Button_Click_11(object sender, RoutedEventArgs e)
    {
        var dialog = MessageBox.Show("Confirmed?", "Alert", MessageBoxButton.YesNo, MessageBoxImage.Question);
        if (dialog == MessageBoxResult.No) return;

        try
        {

            using (LocalData.LocalContext context = new LocalData.LocalContext())
            {
                // Update shift in local database
                var shift = context.Shifts.FirstOrDefault(s => s.Id == UiShift.ShiftId);
                if (shift != null)
                {
                    shift.EndTime = CustomTimeProvider.Now;
                    shift.InProgress = false;
                    shift.LastModified = CustomTimeProvider.Now;

                    // Create PendingSyncItem for shift update
                    var shiftPendingSyncItem = new PendingSyncItem
                    {
                        EntityType = "Shift",
                        EntityId = shift.Id,
                        CreatedAt = CustomTimeProvider.Now,
                        IsSynced = false,
                        OperationType = "Update",
                        SyncError = ""
                    };

                    // Add to PendingSyncItem for syncing
                    context.PendingSyncItems.Add(shiftPendingSyncItem);
                }

                // Update store day in local database
                var storeDay = context.StoreDays.FirstOrDefault(s => s.Id == UiShift.StoreDayId);
                if (storeDay != null)
                {
                    storeDay.EndTime = CustomTimeProvider.Now;
                    storeDay.InProgress = false;
                    storeDay.LastModified = CustomTimeProvider.Now;



                    // Create PendingSyncItem for store day update
                    var storeDayPendingSyncItem = new PendingSyncItem
                    {
                        EntityType = "StoreDay",
                        EntityId = storeDay.Id,
                        CreatedAt = CustomTimeProvider.Now,
                        IsSynced = false,
                        OperationType = "Update",
                        SyncError = ""
                    };

                    // Add to PendingSyncItem for syncing
                    context.PendingSyncItems.Add(storeDayPendingSyncItem);
                }

                // Create new store day
                var nextStoreDay = new StoreDay()
                {
                    InProgress = true,
                    StartTime = CustomTimeProvider.Now.AddMinutes(1),
                    StoreId = (int)UiShift.StoreId,
                    LastModified = CustomTimeProvider.Now
                };

                // Add new store day to local database
                context.StoreDays.Add(nextStoreDay);

                context.SaveChanges();

                // Create PendingSyncItem for new store day
                var newStoreDayPendingSyncItem = new PendingSyncItem
                {
                    EntityType = "StoreDay",
                    EntityId = nextStoreDay.Id,
                    CreatedAt = CustomTimeProvider.Now,
                    IsSynced = false,
                    OperationType = "Insert",
                    SyncError = ""
                };

                // Add to PendingSyncItem for syncing
                context.PendingSyncItems.Add(newStoreDayPendingSyncItem);

                // Save all changes
                context.SaveChanges();

                // Get subcategory summaries from local database
                var result = context.ProductStoreSales.Include(pss => pss.Sale)
                    .Where(pss => pss.Sale.Date >= storeDay.StartTime && pss.Sale.Date <= storeDay.EndTime)
                    .GroupBy(pss => pss.ProductStore.Product.Division.SubCatagory.Name)
                    .Select(g => new SubCategorySummary
                    {
                        SubCategoryName = g.Key,
                        Quantity = g.Sum(pss => pss.Quantity),
                        Total = (decimal)g.Sum(pss => (double)pss.Total) // Convert to double for SQLite aggregation
                    })
                    .ToList();

                // Get special product sales from local database
                var groupedSpecialProductSales = context.SpecialProductSales
                    .Include(sps => sps.Product)
                    .Include(sps => sps.Sale)
                    .Where(pss => pss.Sale.Date >= storeDay.StartTime && pss.Sale.Date <= storeDay.EndTime)
                    .GroupBy(sps => sps.Product.Name)
                    .Select(g => new SpecialProductSaleDetails
                    {
                        ProductName = g.Key,
                        Amount = (decimal)g.Sum(sps => (double)sps.Amount)
                    })
                    .ToList();

                foreach (var s in groupedSpecialProductSales)
                {
                    result.Add(new SubCategorySummary() { SubCategoryName = s.ProductName, Quantity = 0, Total = s.Amount });
                }

                // Get manual discounts from local database
                var manualDiscounts = context.ProductStoreSales
                    .Include(pss => pss.Sale)
                    .Include(pss => pss.Promotion)
                    .Where(pss => pss.Sale.Date >= storeDay.StartTime && pss.Sale.Date <= storeDay.EndTime && pss.PriceOverriden)
                    .Select(pss => new ManualDiscountDetails
                    {
                        PLU = pss.ProductStore.Product.PLU,
                        ProductName = pss.ProductStore.Product.Name,
                        Quantity = pss.Quantity,
                        DiscountAmount = pss.OverriddenDiscountAmount
                    })
                    .ToList();

                // Get staff discounts from local database
                var staffDiscounts = context.ProductStoreSales
                    .Include(pss => pss.Sale)
                    .Include(pss => pss.Promotion)
                    .Where(pss => pss.Sale.Date >= storeDay.StartTime && pss.Sale.Date <= storeDay.EndTime && pss.StaffDiscount)
                    .Select(pss => new StaffDiscountDetails
                    {
                        PLU = pss.ProductStore.Product.PLU,
                        ProductName = pss.ProductStore.Product.Name,
                        Quantity = pss.Quantity,
                        DiscountAmount = pss.StaffDiscountedAmount
                    })
                    .ToList();

                // Get promotions from local database
                var promotions = GetPromotionSalesForShift((int)UiShift.ShiftId);
                var nonBrandPromotions = GetNonBrandPromotionSalesForShift((int)UiShift.ShiftId);
                // 1. Project each sale along with its refund total via a correlated subquery
                // Step 1: Pull sales and their related data
                var sales = context.ProductStoreSales
                    .Where(pss => pss.Sale.Date >= storeDay.StartTime && pss.Sale.Date <= storeDay.EndTime)
                    .Include(pss => pss.Sale)
                    .Include(pss => pss.ProductStore)
                        .ThenInclude(ps => ps.Product)
                            .ThenInclude(p => p.Vat)
                    .ToList(); // Load all data into memory

                // Step 2: Pull all refunds for current shift's sales
                var saleIds = sales.Select(s => s.ProductStoreSaleId).ToList();

                // Load all relevant refunds into memory

                // Step 3: Calculate RefundTotal in memory and project
                var salesByVat = sales.Select(sale => new
                {
                    Sale = sale,

                });



                // 2. Group by VAT code & rate, and do your VAT math
                var vatSummary = salesByVat
                    .GroupBy(x => new
                    {
                        Code = x.Sale.ProductStore.Product.Vat.Code,
                        Value = x.Sale.ProductStore.Product.Vat.Value
                    })
                    .Select(g => new VatSummary
                    {
                        VatRate = g.Key.Code,

                        // total including VAT minus refunds
                        IncVat = g.Sum(x => x.Sale.Total),

                        // strip out VAT from the net (sale minus refunds)
                        ExVat = g.Sum(x =>
                        {
                            var net = x.Sale.Total;
                            return net / (1 + (g.Key.Value / 100));
                        }),

                        // the VAT component of that net
                        VatAmount = g.Sum(x =>
                        {
                            var net = x.Sale.Total;
                            return net - (net / (1 + (g.Key.Value / 100)));
                        })
                    })
                    .ToList();


                // Add VAT for special product sales
                var specialProductVat = context.SpecialProductSales
                    .Include(sps => sps.Sale)
                    .Include(sps => sps.Product)
                    .ThenInclude(p => p.Vat)
                    .Where(sps => sps.Sale.Date >= storeDay.StartTime && sps.Sale.Date <= storeDay.EndTime)
                    .AsEnumerable() // Switch to LINQ to Objects
                    .GroupBy(sps => new { Code = sps.Product.Vat.Code, Rate = sps.Product.Vat.Rate })
                    .Select(g => new VatSummary
                    {
                        VatRate = g.Key.Code,
                        ExVat = g.Sum(sps => sps.Amount / (1 + (sps.Product.Vat.Value / 100))),
                        VatAmount = g.Sum(sps => sps.Amount - (sps.Amount / (1 + (sps.Product.Vat.Value / 100)))),
                        IncVat = g.Sum(sps => sps.Amount)
                    })
                    .ToList();

                // Combine regular and special product VAT summaries
                foreach (var spVat in specialProductVat)
                {
                    var existingVat = vatSummary.FirstOrDefault(v => v.VatRate == spVat.VatRate);
                    if (existingVat != null)
                    {
                        existingVat.ExVat += spVat.ExVat;
                        existingVat.VatAmount += spVat.VatAmount;
                        existingVat.IncVat += spVat.IncVat;
                    }
                    else
                    {
                        vatSummary.Add(spVat);
                    }
                }

                // Get sales in shift from local database
                var salesInShift = context.Sales
                    .Where(s => s.Date >= storeDay.StartTime && s.Date <= storeDay.EndTime)
                    .Include(s => s.SalePaymentMethods)
                    .ThenInclude(spm => spm.PaymentMethod)
                    .ToList();

                var refundsInStoreDay = context.Refunds
    .Where(r => r.Date >= storeDay.StartTime && r.Date <= storeDay.EndTime)
    .Include(r => r.ProductStoreSale)
    .ThenInclude(pss => pss.Sale)
    .ThenInclude(s => s.SalePaymentMethods)
    .ThenInclude(spm => spm.PaymentMethod)
    .ToList();

                // Calculate refund totals by payment method
                decimal cashRefunds = 0;
                decimal cardRefunds = 0;
                decimal uberRefunds = 0;
                decimal voucherRefunds = 0;
                decimal ppCardRefunds = 0;
                decimal deliverooRefunds = 0;

                foreach (var refund in refundsInStoreDay)
                {
                    // Determine payment method of the original sale
                    var salePaymentMethods = refund.ProductStoreSale.Sale.SalePaymentMethods;

                    // If the sale had multiple payment methods, assume refund is from cash
                    if (salePaymentMethods.Count > 1)
                    {
                        cashRefunds += refund.Amount;
                        continue;
                    }

                    // Get the payment method of the sale
                    var paymentMethod = salePaymentMethods.FirstOrDefault()?.PaymentMethod?.Method ?? "Cash";

                    // Add refund amount to the appropriate payment method
                    switch (paymentMethod)
                    {
                        case "Cash":
                            cashRefunds += refund.Amount;
                            break;
                        case "Card":
                            cardRefunds += refund.Amount;
                            break;
                        case "Uber":
                            uberRefunds += refund.Amount;
                            break;
                        case "Voucher":
                            voucherRefunds += refund.Amount;
                            break;
                        case "PP Card":
                            ppCardRefunds += refund.Amount;
                            break;
                        case "Deliveroo":
                            deliverooRefunds += refund.Amount;
                            break;
                        default:
                            cashRefunds += refund.Amount; // Default to cash for unknown payment methods
                            break;
                    }
                }

                // Get payment totals and deduct refunds
                var paymentTotals = salesInShift
                    .SelectMany(s => s.SalePaymentMethods)
                    .GroupBy(spm => spm.PaymentMethod.Method)
                    .ToDictionary(g => g.Key, g => g.Sum(spm => spm.Amount));

                var cardTotal = paymentTotals.GetValueOrDefault("Card", 0) - cardRefunds;
                var cashTotal = paymentTotals.GetValueOrDefault("Cash", 0) - cashRefunds;
                var uberTotal = paymentTotals.GetValueOrDefault("Uber", 0) - uberRefunds;
                var voucherTotal = paymentTotals.GetValueOrDefault("Voucher", 0) - voucherRefunds;
                var ppCardTotal = paymentTotals.GetValueOrDefault("PP Card", 0) - ppCardRefunds;
                var deliverooTotal = paymentTotals.GetValueOrDefault("Deliveroo", 0) - deliverooRefunds;

                // Get expected cash from local database
                var totalPaidOut = (decimal)context.PaidOuts
                    .Where(po => po.Time >= storeDay.StartTime && po.Time <= storeDay.EndTime)
                    .Sum(po => (double)po.Amount);
                var expectedCash = cashTotal - totalPaidOut;

                // Get safe drops from local database
                var safeDrops = context.SafeDrops
                    .Where(sd => sd.Time >= storeDay.StartTime && sd.Time <= storeDay.EndTime)
                    .Select(sd => new SafeDropDetails
                    {
                        CashierName = sd.Shift.Cashier.Name,
                        Time = sd.Time,
                        Amount = sd.Amount
                    })
                    .ToList();

                // Get paid out summaries from local database
                var paidOutSummaries = context.PaidOuts
                    .Where(po => po.Time >= storeDay.StartTime && po.Time <= storeDay.EndTime)
                    .GroupBy(po => po.PaidOutOption.Option)
                    .Select(g => new PaidOutSummary
                    {
                        PaidOutOption = g.Key,
                        Amount = (decimal)g.Sum(po => (double)po.Amount)
                    })
                    .ToList();

                var cashTaken = cashTotal - totalPaidOut;
                var totalSafeDrops = safeDrops.Sum(sd => sd.Amount);
                var cashback = salesInShift.Sum(s => s.Cashback);
                var targetCash = cashTaken - totalSafeDrops - cashback;

                // Get store name from local database
                string storeName = context.Stores.Find(UiShift.StoreId)?.StoreName ?? "Unknown Store";

                // Print day end report
                Printer.DayEndPrinter.Print(
                    storeName,
                    UiShift.PosMachineId.ToString(),
                    UiShift.CashierName,
                    UiShift.StoreId.ToString(),
                    DisplayTimeProvider.ConvertToDisplayTime(storeDay.StartTime).ToString(),
                    DisplayTimeProvider.ConvertToDisplayTime(storeDay.EndTime).ToString(),
                    result,
                    manualDiscounts,
                    staffDiscounts,
                    promotions,
                    vatSummary,
                    cardTotal,
                    cashTotal,
                    uberTotal,
                    voucherTotal,
                    ppCardTotal,
                    deliverooTotal,
                    expectedCash,
                    safeDrops,
                    paidOutSummaries,
                    ConfigurationHelper.PrinterName,
                    cashTaken,
                    totalSafeDrops,
                    cashback,
                    targetCash,
                    40,
                    ConfigurationHelper.Address,
                    groupedSpecialProductSales);

                // Reset UiShift properties
                UiShift.Shift = null;
                UiShift.StoreDay = null;
                UiShift.CashierId = null;
                UiShift.CashierName = null;
                UiShift.StoreId = null;
                UiShift.StoreName = null;
                UiShift.ShiftId = null;
                UiShift.PosMachineId = null;
                UiShift.StoreDayId = null;

                // Show login window and close current window
                LoginWindow loginWindow = new LoginWindow();
                loginWindow.Show();
                this.Close();
            }

        }
        catch (Exception ex)
        {
            MessageBox.Show(ex.Message);
        }

        barcodeTextBox.Visibility = Visibility.Visible;
        barcodeTextBox.Focus();
    }


    private void OpenQuickAccessProducts_Click(object sender, RoutedEventArgs e)
    {
        try
        {
            // Get list of PLUs already in the cart
            var alreadyAddedBarcodes = (DataContext as MainViewModel).Products
                .Where(p => !string.IsNullOrEmpty(p.Barcode))
                .Select(p => p.Barcode)
                .ToList();

            // Create and show the quick access products window
            var quickAccessWindow = new QuickAccessProductsWindow(DataContext as MainViewModel, alreadyAddedBarcodes);
            quickAccessWindow.ShowDialog();

            // After window is closed, focus back on barcode textbox
            barcodeTextBox.Visibility = Visibility.Visible;
            barcodeTextBox.Focus();
        }
        catch (Exception ex)
        {
            _logger.Error(ex, "Error opening quick access products window");
            MessageBox.Show($"Error: {ex.Message}", "Error", MessageBoxButton.OK, MessageBoxImage.Error);
        }
    }

    //logout
    private void Button_Click_12(object sender, RoutedEventArgs e)
    {
        //promt whether user wants to end the shift
        var dialog = MessageBox.Show("Confirmed?", "Alart", MessageBoxButton.YesNo, MessageBoxImage.Question);
        if (dialog == MessageBoxResult.No) return;


        UiShift.CashierId = 0;
        UiShift.CashierName = "";

        LoginWindow loginWindow = new LoginWindow();
        loginWindow.Show();

        this.Close();


        barcodeTextBox.Visibility = Visibility.Visible;
        barcodeTextBox.Focus();

    }


    //refund
    private void Button_Click_13(object sender, RoutedEventArgs e)
    {
        RefundWindow refundWindow = new RefundWindow();
        refundWindow.ShowDialog();

        barcodeTextBox.Visibility = Visibility.Visible;
        barcodeTextBox.Focus();

    }

    //news paper
    private void Button_Click_14(object sender, RoutedEventArgs e)
    {
        using (LocalData.LocalContext contxt = new LocalData.LocalContext())
        {
            var product = contxt.Products.First(p => p.Barcode == "31324546574645345346564643545");

            POS.WPF.Product product1 = new POS.WPF.Product();
            product1.ProductName = product.Name;
            product1.PLU = "";
            product1.IsSpecialProduct = true;
            product1.Barcode = product.Barcode;
            (DataContext as MainViewModel).Products.Add(product1);

            PriceOverride priceOverride = new PriceOverride(0);
            priceOverride.ShowDialog();

            if (PriceOverride.Amount != 0)
            {
                product1.OverriddenPrice = PriceOverride.Amount;
            }
        }

        barcodeTextBox.Visibility = Visibility.Visible;
        barcodeTextBox.Focus();

    }

    //paypoint
    private void Button_Click_15(object sender, RoutedEventArgs e)
    {
        using (LocalData.LocalContext contxt = new LocalData.LocalContext())
        {
            var product = contxt.Products.First(p => p.Barcode == "2342342342342353532432424235345");

            POS.WPF.Product product1 = new POS.WPF.Product();
            product1.ProductName = product.Name;
            product1.PLU = "";
            product1.IsSpecialProduct = true;
            product1.Barcode = product.Barcode;
            (DataContext as MainViewModel).Products.Add(product1);

            PriceOverride priceOverride = new PriceOverride(0);
            priceOverride.ShowDialog();

            if (PriceOverride.Amount != 0)
            {
                product1.OverriddenPrice = PriceOverride.Amount;
            }
        }

        barcodeTextBox.Visibility = Visibility.Visible;
        barcodeTextBox.Focus();

    }

    //hot food
    private void Button_Click_16(object sender, RoutedEventArgs e)
    {

        using (LocalData.LocalContext contxt = new LocalData.LocalContext())
        {
            var product = contxt.Products.First(p => p.Barcode == "898757463453453456576574563445");

            POS.WPF.Product product1 = new POS.WPF.Product();
            product1.ProductName = product.Name;
            product1.PLU = "";
            product1.IsSpecialProduct = true;
            product1.Barcode = product.Barcode;
            (DataContext as MainViewModel).Products.Add(product1);

            PriceOverride priceOverride = new PriceOverride(0);
            priceOverride.ShowDialog();

            if (PriceOverride.Amount != 0)
            {
                product1.OverriddenPrice = PriceOverride.Amount;
            }
        }

        barcodeTextBox.Visibility = Visibility.Visible;
        barcodeTextBox.Focus();

    }

    private void BarcodeTextBox_KeyDown(object sender, KeyEventArgs e)
    {

        _logger.Info($"BarcodeTextBox_KeyDown {e.Key.ToString()}");

        // Check if the Enter key is pressed indicating the end of a barcode scan.
        if (e.Key == System.Windows.Input.Key.Enter)
        {
            // Retrieve and trim the scanned barcode value.
            string scannedBarcode = barcodeTextBox.Text.Trim();

            if (!string.IsNullOrEmpty(scannedBarcode))
            {
                // Process the scanned barcode (e.g., search for the product)
                ProcessBarcode(scannedBarcode);
            }

            // Clear the textbox for the next scan.
            barcodeTextBox.Clear();

            barcodeTextBox.Visibility = Visibility.Visible;
            barcodeTextBox.Focus();


            // Prevent further handling if needed.
            e.Handled = true;
        }
    }

    private void Button_Click_17(object sender, RoutedEventArgs e)
    {
        using (LocalData.LocalContext contxt = new LocalData.LocalContext())
        {
            var product = contxt.Products.First(p => p.Barcode == "3423454563536787667567678");

            POS.WPF.Product product1 = new POS.WPF.Product();
            product1.ProductName = product.Name;
            product1.PLU = "";
            product1.IsSpecialProduct = true;
            product1.Barcode = product.Barcode;
            (DataContext as MainViewModel).Products.Add(product1);

            PriceOverride priceOverride = new PriceOverride(0);
            priceOverride.ShowDialog();

            if (PriceOverride.Amount != 0)
            {
                product1.OverriddenPrice = PriceOverride.Amount;
            }
        }

        barcodeTextBox.Visibility = Visibility.Visible;
        barcodeTextBox.Focus();
    }

    private void Button_Click_18(object sender, RoutedEventArgs e)
    {
    }

    private void Button_Click_19(object sender, RoutedEventArgs e)
    {

        txtInput.Text = "12753";

        // Trigger the same logic as if the user had entered the PLU/barcode
        Button_Click(sender, e);

        barcodeTextBox.Visibility = Visibility.Visible;
        barcodeTextBox.Focus();
    }

    private void Button_Click_20(object sender, RoutedEventArgs e)
    {

        txtInput.Text = "127534";

        // Trigger the same logic as if the user had entered the PLU/barcode
        Button_Click(sender, e);

        barcodeTextBox.Visibility = Visibility.Visible;
        barcodeTextBox.Focus();
    }


}

// Create a static synchronization object
public static class SyncManager
{
    private static readonly SemaphoreSlim _syncLock = new SemaphoreSlim(1, 1);

    public static async Task<bool> TryAcquireSyncLockAsync(TimeSpan timeout)
    {
        return await _syncLock.WaitAsync(timeout);
    }

    public static void ReleaseSyncLock()
    {
        _syncLock.Release();
    }
}

public static class ObjectDumper
{
    public static void DumpToFile(object obj, string filePath, int maxDepth = 3)
    {
        using var writer = new StreamWriter(filePath, append: true);
        DumpObject(obj, writer, 0, maxDepth);
    }

    private static void DumpObject(object obj, StreamWriter writer, int depth, int maxDepth)
    {
        if (obj == null || depth > maxDepth)
        {
            writer.WriteLine($"{Indent(depth)}null");
            return;
        }

        Type type = obj.GetType();

        // For primitives, strings, DateTime, etc.
        if (type.IsPrimitive || obj is string || obj is DateTime || obj is decimal)
        {
            writer.WriteLine($"{Indent(depth)}{obj}");
            return;
        }

        // If it's a collection, loop through items
        if (obj is IEnumerable enumerable && !(obj is string))
        {
            writer.WriteLine($"{Indent(depth)}{type.Name} (Collection):");
            foreach (var item in enumerable)
            {
                DumpObject(item, writer, depth + 1, maxDepth);
            }
            return;
        }

        // Dump all properties
        writer.WriteLine($"{Indent(depth)}{type.Name}:");
        foreach (var prop in type.GetProperties(BindingFlags.Public | BindingFlags.Instance))
        {
            object value;
            try
            {
                value = prop.GetValue(obj);
            }
            catch
            {
                writer.WriteLine($"{Indent(depth + 1)}{prop.Name} = [Error reading]");
                continue;
            }

            if (value == null || prop.PropertyType.IsPrimitive || value is string || value is DateTime || value is decimal)
            {
                writer.WriteLine($"{Indent(depth + 1)}{prop.Name} = {value}");
            }
            else
            {
                writer.WriteLine($"{Indent(depth + 1)}{prop.Name}:");
                DumpObject(value, writer, depth + 2, maxDepth);
            }
        }
    }

    private static string Indent(int depth)
    {
        return new string(' ', depth * 2);
    }
}
