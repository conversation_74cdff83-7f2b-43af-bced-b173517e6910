using System;
using System.Linq;
using System.Threading.Tasks;
using Microsoft.EntityFrameworkCore;
using POS.Core.Models;
using TimeManagement;
using NLog;
using ESCPOS_NET.Utils;

namespace POS.WPF.Services
{
    public static class RemoteToLocalSyncService
    {

        private static readonly Logger _logger = LogManager.GetCurrentClassLogger();

        public static bool SyncAll()
        {

            // Try to acquire the lock with a timeout
            if (!SyncManager.TryAcquireSyncLockAsync(TimeSpan.FromSeconds(30)).Result)
            {
                // Log that we couldn't acquire the lock
                _logger.Warn("Could not acquire sync lock, skipping SyncAll");
                return false;
            }

            try
            {
                using (var centralDb = new Data())
                using (var localDb = new LocalData.LocalContext())
                {
                    var lastSyncTemp = localDb.SyncedTimes.FirstOrDefault()?.Time;
                    DateTime lastSync = DateTime.MinValue;
                    DateTime syncStartTime = CustomTimeProvider.Now;

                    if (lastSyncTemp == null)
                    {
                        localDb.SyncedTimes.Add(new SyncedTime() { Time = lastSync });
                        localDb.SaveChanges();
                    }
                    else
                    {
                        lastSync = (DateTime)lastSyncTemp;
                    }

                    // Sync base/reference entities first
                    SyncEntities(centralDb.Companies, localDb.Companies, lastSync, localDb);
                    SyncEntities(centralDb.Brands, localDb.Brands, lastSync, localDb);
                    SyncEntities(centralDb.PosMachines, localDb.PosMachines, lastSync, localDb);
                    SyncEntities(centralDb.Stores, localDb.Stores, lastSync, localDb);
                    SyncEntities(centralDb.Divisions, localDb.Divisions, lastSync, localDb);
                    SyncEntities(centralDb.SubCatagories, localDb.SubCatagories, lastSync, localDb);
                    SyncEntities(centralDb.Vats, localDb.Vats, lastSync, localDb);
                    SyncEntities(centralDb.Promotions, localDb.Promotions, lastSync, localDb);
                    SyncEntities(centralDb.Products, localDb.Products, lastSync, localDb);
                    SyncEntities(centralDb.ProductStores, localDb.ProductStores, lastSync, localDb);
                    SyncEntities(centralDb.Users, localDb.Users, lastSync, localDb);
                    SyncEntities(centralDb.Admins, localDb.Admins, lastSync, localDb);
                    SyncEntities(centralDb.Accountants, localDb.Accountants, lastSync, localDb);
                    SyncEntities(centralDb.BackOffices, localDb.BackOffices, lastSync, localDb);
                    SyncEntities(centralDb.Cashiers, localDb.Cashiers, lastSync, localDb);
                    SyncEntities(centralDb.PaymentMethods, localDb.PaymentMethods, lastSync, localDb);
                    SyncEntities(centralDb.PaidOutOptions, localDb.PaidOutOptions, lastSync, localDb);
                    SyncEntities(centralDb.NonBrandPromotions, localDb.NonBrandPromotions, lastSync, localDb);
                    SyncEntities(centralDb.StoreSubCategoryLimits, localDb.StoreSubCategoryLimits, lastSync, localDb);
                    SyncEntities(centralDb.QuickAccessButtons, localDb.QuickAccessButtons, lastSync, localDb);

                    // Sync entities with composite keys
                    SyncEntitiesWithCompositeKey(centralDb.CashierStores, localDb.CashierStores, lastSync, localDb,
                        new[] { "StoreId", "CashierId" });


                    localDb.SaveChanges();
                    localDb.SyncedTimes.First().Time = syncStartTime;
                    localDb.SaveChanges();

                    return true;
                }
            }
            catch (Exception ex)
            {
                _logger.Error(ex, "Error during SyncAll");
                return false;
            }
            finally
            {
                // Always release the lock
                SyncManager.ReleaseSyncLock();
            }
        }

        public static void SyncEntities<T>(DbSet<T> centralSet, DbSet<T> localSet, DateTime lastSync, DbContext localDb)
            where T : class
        {
            var entityType = typeof(T);
            Console.WriteLine(entityType.ToString());

            var keyProperty = entityType.GetProperty("Id") ?? entityType.GetProperty(entityType.Name + "Id");
            var isDeletedProp = entityType.GetProperty("IsDeleted");
            var lastModifiedProp = entityType.GetProperty("LastModified");

            if (keyProperty == null || lastModifiedProp == null)
                throw new InvalidOperationException($"Required properties not found for type {entityType.Name}");

            var updatedEntities = centralSet
                .AsNoTracking()
                .Where(e => EF.Property<DateTime?>(e, "LastModified") > lastSync)
                .ToList();

            foreach (var entity in updatedEntities)
            {
                var key = keyProperty.GetValue(entity);
                var isDeleted = isDeletedProp != null && (bool)isDeletedProp.GetValue(entity);
                var existing = localSet.Local.FirstOrDefault(e => keyProperty.GetValue(e).Equals(key))
                    ?? localSet.AsEnumerable().FirstOrDefault(e => keyProperty.GetValue(e).Equals(key));

                if (isDeleted && existing != null)
                {
                    localSet.Remove(existing);
                }
                else if (existing != null)
                {
                    localDb.Entry(existing).CurrentValues.SetValues(entity);
                }
                else if (existing == null && isDeleted == false)
                {
                    localSet.Add(entity);
                    var remoteId = keyProperty.GetValue(entity);
                    // Only add ID mapping if we have both local and remote IDs
                    if (remoteId != null)
                    {
                        AddIdMapping(localDb, entityType.Name, (int)remoteId, (int)remoteId);
                    }
                }
                else if (existing == null && isDeleted == true)
                {
                    // dont have to do anything as, the entity is deleted
                }
            }
        }

        public static void SyncEntitiesWithCompositeKey<T>(DbSet<T> centralSet, DbSet<T> localSet, DateTime lastSync, DbContext localDb,
            string[] keyPropertyNames)
            where T : class
        {
            var entityType = typeof(T);
            Console.WriteLine($"Syncing composite key entity: {entityType}");

            var keyProperties = keyPropertyNames.Select(name => entityType.GetProperty(name)).ToArray();
            var isDeletedProp = entityType.GetProperty("IsDeleted");
            var lastModifiedProp = entityType.GetProperty("LastModified");

            if (keyProperties.Any(p => p == null) || lastModifiedProp == null)
                throw new InvalidOperationException($"Required properties not found for type {entityType.Name}");

            var updatedEntities = centralSet
                .AsNoTracking()
                .Where(e => EF.Property<DateTime?>(e, "LastModified") > lastSync)
                .ToList();

            foreach (var entity in updatedEntities)
            {
                // Create a composite key comparison function
                bool KeysMatch(T e)
                {
                    return keyProperties.All(prop =>
                        prop.GetValue(e)?.Equals(prop.GetValue(entity)) ?? false);
                }

                var isDeleted = isDeletedProp != null && (bool)isDeletedProp.GetValue(entity);
                var existing = localSet.Local.FirstOrDefault(KeysMatch)
                    ?? localSet.AsEnumerable().FirstOrDefault(KeysMatch);

                if (isDeleted && existing != null)
                {
                    localSet.Remove(existing);
                }
                else if (existing != null)
                {
                    localDb.Entry(existing).CurrentValues.SetValues(entity);
                }
                else if (existing == null && isDeleted == false)
                {
                    localSet.Add(entity);
                }
                else if (existing == null && isDeleted == true)
                {
                    // Don't have to do anything as the entity is deleted
                }
            }
        }

        public static void AddIdMapping(DbContext context, string entityType, int localId, int remoteId)
        {
            var idMapping = new IdMapping
            {
                EntityType = entityType,
                LocalId = localId,
                RemoteId = remoteId,
                MappedAt = CustomTimeProvider.Now
            };
            context.Set<IdMapping>().Add(idMapping);
        }
    }
}
