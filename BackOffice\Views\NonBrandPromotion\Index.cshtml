@model IEnumerable<POS.Core.Models.NonBrandPromotion>
@{
    ViewData["Title"] = "Non-Brand Promotions";
    int currentPage = ViewBag.CurrentPage;
    int totalPages = ViewBag.TotalPages;
}

<h2>Non-Brand Promotions</h2>

<p>
    <a asp-action="Create" class="btn btn-success mb-3">Create New Promotion</a>
</p>

<table class="table table-striped">
    <thead>
        <tr>
            <th>Name</th>
            <th>Type</th>
            <th>Start Time</th>
            <th>End Time</th>
            <th>Price</th>
            <th>Quantity</th>
            <th>Actions</th>
        </tr>
    </thead>
    <tbody>
        @foreach (var item in Model)
        {
            <tr>
                <td>@item.Name</td>
                <td>@item.Type</td>
                <td>@item.StartTime.ToShortDateString()</td>
                <td>@item.EndTime.ToShortDateString()</td>
                <td>@(item.PromotionPrice?.ToString("C2") ?? "N/A")</td>
                <td>@item.Quantity</td>
                <td>
                    <a asp-action="Edit" asp-route-id="@item.Id" class="btn btn-warning btn-sm">Edit</a> |
                    <a asp-action="Details" asp-route-id="@item.Id" class="btn btn-info btn-sm">Details</a> |
                    <form asp-action="Delete" asp-route-id="@item.Id" method="post" style="display: inline;">
                        <button type="submit" class="btn btn-danger btn-sm" 
                                onclick="return confirm('Are you sure you want to delete this promotion?');">
                            Delete
                        </button>
                    </form>
                </td>
            </tr>
        }
    </tbody>
</table>

<nav aria-label="Page navigation">
    <ul class="pagination">
        @for (int i = 1; i <= totalPages; i++)
        {
            <li class="page-item @(i == currentPage ? "active" : "")">
                <a class="page-link" asp-action="Index" asp-route-page="@i">@i</a>
            </li>
        }
    </ul>
</nav>