﻿using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace POS.Core.Migrations
{
    /// <inheritdoc />
    public partial class migration6 : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.AddColumn<bool>(
                name: "IsNonBrandPromotion",
                table: "ProductStoreSales",
                type: "bit",
                nullable: false,
                defaultValue: false);

            migrationBuilder.AddColumn<int>(
                name: "NonBrandPromotionId",
                table: "ProductStoreSales",
                type: "int",
                nullable: true);

            migrationBuilder.CreateIndex(
                name: "IX_ProductStoreSales_NonBrandPromotionId",
                table: "ProductStoreSales",
                column: "NonBrandPromotionId");

            migrationBuilder.AddForeignKey(
                name: "FK_ProductStoreSales_NonBrandPromotions_NonBrandPromotionId",
                table: "ProductStoreSales",
                column: "NonBrandPromotionId",
                principalTable: "NonBrandPromotions",
                principalColumn: "Id");
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropForeignKey(
                name: "FK_ProductStoreSales_NonBrandPromotions_NonBrandPromotionId",
                table: "ProductStoreSales");

            migrationBuilder.DropIndex(
                name: "IX_ProductStoreSales_NonBrandPromotionId",
                table: "ProductStoreSales");

            migrationBuilder.DropColumn(
                name: "IsNonBrandPromotion",
                table: "ProductStoreSales");

            migrationBuilder.DropColumn(
                name: "NonBrandPromotionId",
                table: "ProductStoreSales");
        }
    }
}
