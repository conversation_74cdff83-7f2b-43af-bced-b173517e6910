﻿@model POS.Core.Models.Cashier

@{
    ViewData["Title"] = "Create Cashier";
}

<h1>Create Cashier</h1>

<h4>Cashier</h4>
<hr />

<form asp-action="Create" method="post">
    <div class="form-group">
        <label asp-for="Name" class="control-label"></label>
        <input asp-for="Name" class="form-control" />
        <span asp-validation-for="Name" class="text-danger"></span>
    </div>
    <div class="form-group">
        <label asp-for="Password" class="control-label"></label>
        <input asp-for="Password" class="form-control" />
        <span asp-validation-for="Password" class="text-danger"></span>
    </div>

    <div class="form-group">
        <label for="Stores">Assign Stores</label>
        <select multiple class="form-control" id="Stores" name="StoreIds">
            @foreach (var store in ViewBag.Stores as List<POS.Core.Models.Store>)
            {
                <option value="@store.StoreId">@store.StoreName</option>
            }
        </select>
    </div>


    <div class="form-group">
        <button type="submit" class="btn btn-primary">Create</button>
        <a href="@Url.Action("Index")" class="btn btn-secondary">Back to List</a>
    </div>
</form>

@section Scripts {
    @{
        await Html.RenderPartialAsync("_ValidationScriptsPartial");
    }
}
