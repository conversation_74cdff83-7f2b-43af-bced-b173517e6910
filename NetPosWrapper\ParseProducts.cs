﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace NetPosWrapper
{




    public class ParseProducts
    {
        public static List<Product> Parse(string filePath)
        {
            List<Product> products = new List<Product>();

            try
            {
                using (StreamReader reader = new StreamReader(filePath))
                {
                    string line;
                    while ((line = reader.ReadLine()) != null)
                    {
                        try
                        {
                            if (line.Length < 85) continue;

                            RecordType? productType = null;
                            if (line.StartsWith("06")) productType = RecordType.NewProduct;
                            else if (line.StartsWith("17")) productType = RecordType.ProductAmendment;
                            else if (line.StartsWith("18")) productType = RecordType.Promotions;
                            else if (line.StartsWith("49")) productType = RecordType.Deletions;
                            else if (line.StartsWith("03")) productType = RecordType.PriceChange;
                            else continue;

                            string productCode = line.Substring(2, 6);
                            string barcode = line.Substring(9, 13);
                            string caseQuantity = line.Substring(22, 5);
                            string purchasePrice = line.Substring(27, 7);
                            string retailPrice = line.Substring(34, 7);
                            string vatCode = line.Substring(41, 2);
                            string description = line.Substring(43, 30).Trim();
                            string shortProductName = line.Substring(73, 12).Trim();
                            string department = line.Substring(116, 2);
                            string category = line.Substring(118, 2);



                            if (productType.HasValue)
                            {
                                products.Add(new Product
                                {
                                    Barcode = barcode,
                                    CaseQuantity = caseQuantity,
                                    Description = description,
                                    ProductCode = productCode,
                                    PurchasePrice = FormatStringWithDecimal(purchasePrice), // Convert purchasePrice,
                                    RetailPrice = FormatStringWithDecimal(retailPrice),
                                    ShortProductName = shortProductName,
                                    VatCode = vatCode,
                                    RecordType = productType.Value,
                                    Department = department,
                                    Category = category,
                                    
                                    
                                });
                            }
                        }
                        catch (Exception ex)
                        {
                            Console.WriteLine($"Error parsing line: {ex.Message}");
                        }
                    }
                    return products;
                }
            }
            catch (FileNotFoundException ex)
            {
                Console.WriteLine($"File not found: {ex.Message}");
                return new List<Product>();
            }
            catch (IOException ex)
            {
                Console.WriteLine($"Error reading file: {ex.Message}");
                return new List<Product>();
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Unexpected error: {ex.Message}");
                return new List<Product>();
            }
        }


        static string FormatStringWithDecimal(string input)
        {
            // Validate that the input contains only digits
            if (string.IsNullOrEmpty(input) || !long.TryParse(input, out _))
            {
                return input;
            }

            // If the string has fewer than 3 characters, pad it with leading zeros
            if (input.Length < 3)
            {
                input = input.PadLeft(3, '0');
            }

            // Insert the decimal point two characters from the end
            int decimalPosition = input.Length - 2;
            string formattedString = input.Insert(decimalPosition, ".");

            return formattedString;
        }
    }
}
