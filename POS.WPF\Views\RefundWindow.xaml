﻿<Window x:Class="POS.WPF.Views.RefundWindow"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        xmlns:local="clr-namespace:POS.WPF.Views"
        Title="Refund Window" Height="500" Width="800"
        Background="#004e7c">
    <Grid Margin="10">
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
            <RowDefinition Height="Auto"/>
        </Grid.RowDefinitions>

        <!-- Add this new section for sales list -->
        <StackPanel Grid.Row="0" Orientation="Vertical" Margin="0,0,0,10">
            <TextBlock Text="Today's Sales:" 
                       Foreground="White" 
                       FontSize="16"
                       Margin="0,0,0,5"/>

            <ListView x:Name="salesListView" 
                      Height="150"
                      Background="#0a688d"
                      Foreground="White"
                      SelectionChanged="salesListView_SelectionChanged">
                <ListView.View>
                    <GridView>
                        <GridViewColumn Header="Sale ID" DisplayMemberBinding="{Binding SaleId}" Width="80"/>
                        <GridViewColumn Header="Barcode" DisplayMemberBinding="{Binding Barcode}" Width="150"/>
                        <GridViewColumn Header="Time" DisplayMemberBinding="{Binding Date,StringFormat={}{0:HH:mm}}" Width="100"/>
                        <GridViewColumn Header="Total" DisplayMemberBinding="{Binding TotalValue,StringFormat=C}" Width="100"/>
                    </GridView>
                </ListView.View>
            </ListView>

            <StackPanel Orientation="Horizontal" Margin="0,5,0,0">
                <TextBox x:Name="txtSearchSales" 
                         Width="200"
                         Margin="0,0,10,0"
                         KeyDown="txtSearchSales_KeyDown"
                         Tag="Search by barcode or ID..."/>
                <Button Content="Refresh" 
                        Click="btnRefreshSales_Click"
                        Padding="10,2"/>
            </StackPanel>
        </StackPanel>

        <TextBox Grid.Row="1" x:Name="barcodeTextBox" Visibility="Hidden" KeyDown="BarcodeTextBox_KeyDown"/>


        <!-- Scanner Status Section -->
        <Border Grid.Row="1" Background="#0a688d" Margin="0,0,0,10" CornerRadius="5">
            <Label x:Name="labelReciptScanStatus" 
                   Content="Waiting for scan..."
                   FontSize="18" FontWeight="Bold" 
                   Foreground="White"
                   VerticalAlignment="Center"
                   HorizontalAlignment="Center"
                   Padding="10"/>
        </Border>

        <!-- Manual Input Section -->
        <StackPanel Grid.Row="2" Orientation="Horizontal" Margin="0,0,0,10">
            <TextBox x:Name="txtManualInput"
                     Width="200"
                     FontSize="16"
                     Background="#0a688d"
                     Foreground="White"
                     Margin="0,0,10,0"
                     VerticalContentAlignment="Center"
                     KeyDown="txtManualInput_KeyDown"/>

            <Button Content="SEARCH" 
                    Background="#004e7c"
                    Foreground="White"
                    FontSize="16"
                    Padding="15,5"
                    Click="btnManualSearch_Click"/>
            <Button Content="CANCEL" 
         Background="#004e7c"
         Foreground="White"
         FontSize="16"
         Padding="15,5"
         Click="Button_Click"/>
        </StackPanel>

        <Grid Grid.Row="3" Margin="0,0,0,10">
            <Grid.ColumnDefinitions>
                <ColumnDefinition Width="*" />
                <ColumnDefinition Width="Auto" />
            </Grid.ColumnDefinitions>

            <ListBox x:Name="listProductSales"
             Grid.Column="0"
             SelectionChanged="listProductSales_SelectionChanged"
             Background="#0a688d"
             Foreground="White"
             BorderBrush="Transparent"
             ScrollViewer.VerticalScrollBarVisibility="Auto">
                <ListBox.ItemTemplate>
                    <DataTemplate>
                        <StackPanel Orientation="Vertical" Background="Green" Margin="2">
                            <TextBlock Text="{Binding DisplayText}" 
                               FontSize="16"
                               Foreground="White"
                               Padding="10"
                               TextWrapping="Wrap"/>
                        </StackPanel>
                    </DataTemplate>
                </ListBox.ItemTemplate>
            </ListBox>

            <local:NumericKeypad Grid.Column="1"
                         KeyPressed="NumericKeypad_KeyPressed"
                         Margin="10,0,0,0"
                         VerticalAlignment="Stretch"/>
        </Grid>


        <!-- Quantity Input Section -->
        <Border Grid.Row="4" 
                Background="#0a688d" 
                CornerRadius="5"
                Visibility="Collapsed"
                x:Name="quantityPanel">
            <StackPanel Orientation="Vertical"
                        HorizontalAlignment="Center"
                        VerticalAlignment="Center"
                        Margin="10">
                <StackPanel Orientation="Horizontal" Margin="0,0,0,10">
                    <TextBlock Text="Quantity to Refund:" 
                           Foreground="White"
                           FontSize="16"
                           Margin="0,0,10,0"/>

                    <TextBox x:Name="txtQuantity"
                         Width="80"
                         FontSize="16"
                         Background="#004e7c"
                         Foreground="White"
                         Margin="0,0,10,0"
                         VerticalContentAlignment="Center"/>
                </StackPanel>
                
                <StackPanel Orientation="Horizontal" Margin="0,0,0,10">
                    <CheckBox x:Name="chkCardRefund" 
                              Foreground="White" 
                              VerticalAlignment="Center"
                              Margin="0,0,10,0"/>
                    <TextBlock Text="Refund to Card" 
                               Foreground="White"
                               FontSize="16"/>
                </StackPanel>

                <Button Content="PROCESS REFUND"
                        Background="#004e7c"
                        Foreground="White"
                        FontSize="16"
                        Padding="15,5"
                        Click="btnProcessRefund_Click"/>
            </StackPanel>
        </Border>
    </Grid>
</Window>


