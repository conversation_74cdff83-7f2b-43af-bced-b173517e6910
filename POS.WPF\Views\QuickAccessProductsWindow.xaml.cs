using System;
using System.Collections.Generic;
using System.Linq;
using System.Windows;
using System.Windows.Controls;
using System.Windows.Media;
using System.Windows.Media.Imaging;
using Microsoft.EntityFrameworkCore;
using POS.Core.Models;
using POS.WPF.Helpers;
using POS.WPF.ViewModels;
using TimeManagement;

namespace POS.WPF.Views
{
    public partial class QuickAccessProductsWindow : Window
    {
        private MainViewModel _mainViewModel;
        private List<string> _alreadyAddedBarcodess;

        public QuickAccessProductsWindow(MainViewModel mainViewModel, List<string> alreadyAddedPLUs)
        {
            InitializeComponent();
            _mainViewModel = mainViewModel;
            _alreadyAddedBarcodess = alreadyAddedPLUs ?? new List<string>();

            Helpers.WindowStyleHelper.SetWindowStyle(this);
            LoadQuickAccessButtons();
        }

        private void LoadQuickAccessButtons()
        {
            try
            {
                using (var context = new LocalData.LocalContext())
                {
                    // Get the current store ID
                    int storeId = (int)UiShift.StoreId;

                    // Get quick access buttons for this store
                    var quickAccessButtons = context.QuickAccessButtons
                        .Include(b => b.Product)
                        .Where(b => b.StoreId == storeId)
                        .ToList();

                    // Clear existing buttons
                    productsPanel.Children.Clear();

                    // Add buttons dynamically
                    foreach (var buttonConfig in quickAccessButtons)
                    {
                        // Skip products that are already in the cart
                        if (_alreadyAddedBarcodess.Contains(buttonConfig.Product.Barcode))
                            continue;

                        Button button = new Button
                        {
                            Background = new SolidColorBrush((Color)ColorConverter.ConvertFromString("#004e7c")),
                            Margin = new Thickness(10),
                            Padding = new Thickness(10),
                            MinWidth = 150,
                            MinHeight = 100,
                            Tag = buttonConfig.Product.Barcode // Store PLU in Tag for later use
                        };

                        StackPanel stackPanel = new StackPanel
                        {
                            Orientation = Orientation.Vertical,
                            HorizontalAlignment = HorizontalAlignment.Center
                        };

                        Image image = new Image
                        {
                            Source = new BitmapImage(new Uri("/Images/shopping.png", UriKind.Relative)),
                            Width = 40,
                            Height = 40,
                            Margin = new Thickness(0, 0, 0, 10)
                        };

                        TextBlock textBlock = new TextBlock
                        {
                            Text = buttonConfig.ButtonText,
                            Foreground = new SolidColorBrush(Colors.White),
                            FontWeight = FontWeights.Bold,
                            TextAlignment = TextAlignment.Center,
                            TextWrapping = TextWrapping.Wrap
                        };

                        stackPanel.Children.Add(image);
                        stackPanel.Children.Add(textBlock);
                        button.Content = stackPanel;

                        // Add click handler
                        button.Click += QuickAccessButton_Click;

                        // Add to panel
                        productsPanel.Children.Add(button);
                    }
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show("Failed to load quick access buttons: " + ex.Message);
            }
        }

        private void QuickAccessButton_Click(object sender, RoutedEventArgs e)
        {
            Button button = sender as Button;
            if (button != null && button.Tag != null)
            {
                string barcode = button.Tag.ToString();

                try
                {
                    using (var context = new LocalData.LocalContext())
                    {
                        // First get the product
                        var product = context.Products
                            .Include(p => p.Division)
                            .ThenInclude(d => d.SubCatagory)
                            .FirstOrDefault(p => p.Barcode == barcode);

                        if (product == null)
                        {
                            MessageBox.Show("Product not found", "Error",
                                MessageBoxButton.OK, MessageBoxImage.Error);
                            return;
                        }

                        // Then get related data
                        var productStore = context.ProductStores
                            .FirstOrDefault(ps => ps.StoreId == UiShift.StoreId && ps.ProductId == product.Id);

                        var promotion = context.Promotions
                            .Include(promo => promo.Products)
                            .FirstOrDefault(promo =>
                                promo.StartTime <= CustomTimeProvider.Now &&
                                promo.EndTime >= CustomTimeProvider.Now &&
                                product.PromotionId == promo.Id);

                        var nonBrandPromotion = context.NonBrandPromotions
                            .Include(promo => promo.Products)
                            .FirstOrDefault(promo =>
                                promo.StartTime <= CustomTimeProvider.Now &&
                                promo.EndTime >= CustomTimeProvider.Now &&
                                product.NonBrandPromotionId == promo.Id);

                        // Add product to the main view model with promotion information
                        AddProductToMainViewModel(product, productStore, promotion, nonBrandPromotion);

                        // Add to already added PLUs list
                        _alreadyAddedBarcodess.Add(barcode);

                        // Disable the button
                        button.IsEnabled = false;
                        button.Opacity = 0.5;

                        MessageBox.Show($"{product.Name} added to cart", "Product Added",
                            MessageBoxButton.OK, MessageBoxImage.Information);
                    }
                }
                catch (Exception ex)
                {
                    MessageBox.Show($"Error adding product: {ex.Message}", "Error",
                        MessageBoxButton.OK, MessageBoxImage.Error);
                }
            }
        }

        private void AddProductToMainViewModel(Core.Models.Product product, Core.Models.ProductStore productStore,
            Promotion promotion, NonBrandPromotion nonBrandPromotion)
        {
            if (_mainViewModel.Products.Any(pp => pp.Barcode == product.Barcode))
            {
                var existingUiProduct = _mainViewModel.Products.FirstOrDefault(p => p.Barcode == product.Barcode);
                existingUiProduct.Quantity++;
            }
            else
            {
                POS.WPF.Product product1 = new POS.WPF.Product();
                product1.ProductName = product.Name;
                product1.PLU = product.PLU;
                product1.Barcode = product.Barcode;
                product1.UnitPrice = productStore?.StoreSpecificPrice ?? product.SellingPrice;
                _mainViewModel.Products.Add(product1);

                // Apply promotions
                if (promotion != null)
                {
                    product1.Promition = promotion;
                    product1.Note = promotion.Name;
                }
                else if (nonBrandPromotion != null)
                {
                    product1.NonBrandPromotion = nonBrandPromotion;
                    product1.Note = nonBrandPromotion.Name;
                }

                product1.Quantity = 1;
            }

            // Force totals update
            _mainViewModel.UpdateTotals();

            // Apply group promotions if needed
            if (_mainViewModel.ApplyAllPromotions != null)
            {
                _mainViewModel.ApplyAllPromotions();
            }
        }

        private void CloseButton_Click(object sender, RoutedEventArgs e)
        {
            this.Close();
        }
    }
}

