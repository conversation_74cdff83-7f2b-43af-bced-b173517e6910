namespace POS.Core.Models
{
    public class SubCatagory : BaseEntity
    {
        public int Id { get; set; }
        public string Name { get; set; }

        public Catagory Catagory { get; set; }

        public StockType StockType { get; set; }
        public string? CashierAlert { get; set; }

        public ICollection<Division> Divisions { get; set; }
        public ICollection<StoreSubCategoryLimit> StoreLimits { get; set; } = new List<StoreSubCategoryLimit>();



      
    }

}
