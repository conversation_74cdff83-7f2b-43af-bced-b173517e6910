﻿@model IEnumerable<POS.Core.Models.Store>

@{
    ViewData["Title"] = "Stores";
}

<h1>Stores</h1>

<p>
    <a asp-action="Create" class="btn btn-primary">Create New</a>
</p>

<table class="table">
    <thead>
        <tr>
            <th>Store Name</th>
            <th>Company</th>
            <th>Brand</th>

            <th>Negative Inventory Allowed</th>

            <th>Actions</th>
        </tr>
    </thead>
    <tbody>
        @foreach (var item in Model)
        {
            <tr>
                <td>@item.StoreName</td>
                <td>@item?.Company?.Name</td>
                <td>@item?.Brand?.Name</td>

                <td>@item.AllowNegativeInventory</td>

                <td>
                    <a asp-action="Edit" asp-route-id="@item.StoreId" class="btn btn-sm btn-warning">Edit</a>
                    <form asp-action="Delete" asp-route-id="@item.StoreId" method="post" style="display:inline;">
                        <button type="submit" class="btn btn-sm btn-danger">Delete</button>
                    </form>
                </td>
            </tr>
        }
    </tbody>
</table>
