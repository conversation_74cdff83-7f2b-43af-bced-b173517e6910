﻿using Microsoft.AspNetCore.Mvc;
using POS.Core.Models;

namespace BackOffice.Controllers
{
    // Controllers/EmailSubscriptionController.cs
    public class EmailSubscriptionController : Controller
    {
        private readonly Data _context;

        public EmailSubscriptionController(Data context)
        {
            _context = context;
        }

        public IActionResult Index()
        {
            var subscriptions = _context.EmailSubscriptions.ToList();

            ViewBag.NightEnabled = _context.NightEmails.FirstOrDefault()?.Enabled ?? false;

            return View(subscriptions);
        }

        [HttpPost]
        public IActionResult Save(List<string> emails, bool NightEnabled)
        {
            if (emails != null && emails.Any())
            {
                var existingEmails = _context.EmailSubscriptions.Select(e => e.Email).ToList();

                var newEmails = emails
                    .Where(e => !string.IsNullOrWhiteSpace(e) && IsValidEmail(e))
                    .Distinct()
                    .ToList();

                var emailsToRemove = existingEmails.Except(newEmails);
                _context.EmailSubscriptions.RemoveRange(
                    _context.EmailSubscriptions.Where(e => emailsToRemove.Contains(e.Email)));

                var emailsToAdd = newEmails.Except(existingEmails);
              

                if(_context.NightEmails.FirstOrDefault() == null)
                {
                    _context.NightEmails.Add(new NightEmail { Enabled = NightEnabled });
                }
                else
                {
                    _context.NightEmails.FirstOrDefault().Enabled = NightEnabled;
                    
                }
                
                foreach (var email in emailsToAdd)
                {
                    _context.EmailSubscriptions.Add(new EmailSubscription { Email = email });
                }



                _context.SaveChanges();
            }

            return RedirectToAction("Index");
        }

        private bool IsValidEmail(string email)
        {
            try
            {
                var addr = new System.Net.Mail.MailAddress(email);
                return addr.Address == email;
            }
            catch
            {
                return false;
            }
        }
    }
}
