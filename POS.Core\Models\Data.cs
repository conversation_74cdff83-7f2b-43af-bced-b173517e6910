using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.SqlServer.Migrations.Internal;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Linq.Expressions;
using System.Runtime.Serialization.Json;
using System.Security.Principal;
using System.Text;
using System.Threading.Tasks;

namespace POS.Core.Models
{
    public class Data : DbContext
    {
        public DbSet<Store> Stores { get; set; }
        public DbSet<Product> Products { get; set; }
        public DbSet<CashierStore> CashierStores { get; set; }

        public DbSet<Brand> Brands { get; set; }

        public DbSet<ProductStore> ProductStores { get; set; }
        public DbSet<Sale> Sales { get; set; }

        public DbSet<StoreDay> StoreDays { get; set; }

        public DbSet<ProductStoreSale> ProductStoreSales { get; set; }

        public DbSet<SpecialProductSale> SpecialProductSales { get; set; }
        public DbSet<Cashier> Cashiers { get; set; }

        public DbSet<User> Users { get; set; }

        public DbSet<Admin> Admins { get; set; }
        public DbSet<BackOffice> BackOffices { get; set; }
        public DbSet<Accountant> Accountants { get; set; }


        public DbSet<IpAddress> IpAddresses { get; set; }


        public DbSet<SafeDrop> SafeDrops { get; set; }

        public DbSet<PaymentMethod> PaymentMethods { get; set; }

        public DbSet<Shift> Shifts { get; set; }

        public DbSet<PaidOut> PaidOuts { get; set; }

        public DbSet<PaidOutOption> PaidOutOptions { get; set; }

        public DbSet<Company> Companies { get; set; }

        public DbSet<Vat> Vats { get; set; }

        public DbSet<SubCatagory> SubCatagories { get; set; }

        public DbSet<PosMachine> PosMachines { get; set; }

        public DbSet<Division> Divisions { get; set; }

        public DbSet<Refund> Refunds { get; set; }

        public DbSet<EmailSubscription> EmailSubscriptions { get; set; }

        public DbSet<NightEmail> NightEmails { get; set; }


        public DbSet<Promotion> Promotions { get; set; }

        public DbSet<SalePaymentMethod2> SalePaymentMethods { get; set; }

        public DbSet<NextOrderProduct> NextOrderProducts { get; set; }

        public DbSet<MorrisonOrder> MorrisonOrders { get; set; }

        public DbSet<MorrisonsOrderItem> MorrisonsOrderItems { get; set; }

        public DbSet<VoidSaleItem> VoidSaleItems { get; set; }

        public DbSet<Void> Voids { get; set; }

        public DbSet<DeletedSaleItem> DeletedSaleItems { get; set; }

        public DbSet<NonBrandPromotion> NonBrandPromotions { get; set; }

        public DbSet<StoreSubCategoryLimit> StoreSubCategoryLimits { get; set; }

        public DbSet<QuickAccessButton> QuickAccessButtons { get; set; }
        protected override void OnModelCreating(ModelBuilder modelBuilder)
        {
            // Add global query filter for all entities that inherit from BaseEntity
            foreach (var entityType in modelBuilder.Model.GetEntityTypes())
            {
                // Skip derived types in TPH (Table Per Hierarchy) inheritance
                if (entityType.BaseType != null)
                    continue;
                
                if (typeof(BaseEntity).IsAssignableFrom(entityType.ClrType))
                {
                    var parameter = Expression.Parameter(entityType.ClrType, "e");
                    var property = Expression.Property(parameter, nameof(BaseEntity.IsDeleted));
                    var falseConstant = Expression.Constant(false);
                    var body = Expression.Equal(property, falseConstant);
                    var lambda = Expression.Lambda(body, parameter);

                    modelBuilder.Entity(entityType.ClrType).HasQueryFilter(lambda);
                }
            }
            
            // Existing model configuration
            modelBuilder.Entity<ProductStore>()
                .HasIndex(ps => new { ps.StoreId, ps.ProductId })
                .IsUnique();
            
            modelBuilder.Entity<SalePaymentMethod2>(entity =>
            {
                entity.HasKey(e => e.Id);  // Set primary key

                // Add unique constraint
                entity.HasIndex(e => new { e.SaleId, e.PaymentMethodId })
                      .IsUnique();

                entity.HasOne(e => e.Sale)
                      .WithMany(s => s.SalePaymentMethods)
                      .HasForeignKey(e => e.SaleId);

                entity.HasOne(e => e.PaymentMethod)
                      .WithMany(p => p.SalePaymentMethods)
                      .HasForeignKey(e => e.PaymentMethodId);
            });

            modelBuilder.Entity<CashierStore>()
                .HasKey(cs => new { cs.StoreId, cs.CashierId });

            modelBuilder.Entity<CashierStore>()
                .HasOne(sc => sc.Cashier)
                .WithMany(s => s.CashierStores)
                .HasForeignKey(sc => sc.CashierId);

            modelBuilder.Entity<CashierStore>()
                .HasOne(sc => sc.Store)
                .WithMany(c => c.CashierStores)
                .HasForeignKey(sc => sc.StoreId);


            base.OnModelCreating(modelBuilder);
        }


        public override int SaveChanges()
        {
            ApplyAuditInfo();
            return base.SaveChanges();
        }

        public override Task<int> SaveChangesAsync(CancellationToken cancellationToken = default)
        {
            ApplyAuditInfo();
            return base.SaveChangesAsync(cancellationToken);
        }

        private void ApplyAuditInfo()
        {
            var entries = ChangeTracker.Entries<BaseEntity>();

            foreach (var entry in entries)
            {
                if (entry.State == EntityState.Added || entry.State == EntityState.Modified)
                {
                    entry.Entity.LastModified = TimeManagement.CustomTimeProvider.Now;
                }

                if (entry.State == EntityState.Deleted)
                {
                    entry.State = EntityState.Modified;
                    entry.Entity.IsDeleted = true;
                    entry.Entity.LastModified = TimeManagement.CustomTimeProvider.Now;
                }
            }
        }

        //public Data(DbContextOptions<Data> options) : base(options)
        //{

        //}

        protected override void OnConfiguring(DbContextOptionsBuilder optionsBuilder)
        {
            // Add connection string here (for non-DI scenarios)
            // optionsBuilder.UseSqlServer("Server=103.70.137.109\\SQLEXPRESS,1433;TrustServerCertificate=True;Database=AccuracyTestDb;User Id=appUser;Password=***************");
            //optionsBuilder.UseSqlServer("Data Source=(localdb)\\mssqllocaldb;Initial Catalog=POS1;Integrated Security=True;");
            // optionsBuilder.UseSqlServer("Data Source=.\\SQLEXPRESS;Initial Catalog=POSV2Restore4;Integrated Security=True;");
            // optionsBuilder.UseSqlServer("Data Source=(localdb)\\mssqllocaldb;Initial Catalog=POS1;Integrated Security=True;");
            //optionsBuilder.UseSqlServer("Data Source=(localdb)\\mssqllocaldb;Initial Catalog=POS1;Integrated Security=True;");
             optionsBuilder.UseSqlServer("Server=103.70.137.109\\SQLEXPRESS,1433;TrustServerCertificate=True;Database=AccuracyTestDb;User Id=appUser;Password=***************");

        }
    }

}
