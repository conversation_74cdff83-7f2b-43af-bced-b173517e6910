﻿using BackOffice.Models;
using Microsoft.AspNetCore.Mvc;
using POS.Core.Models;
using System.Net.Http.Headers;
using System.Runtime.InteropServices;
using System.Text.Json;
using TimeManagement;
using Microsoft.AspNetCore.SignalR;
using BackOffice.Hubs;
using Microsoft.Extensions.Configuration;
using BackOffice.Configuration;

namespace BackOffice.Controllers
{
    /// <summary>
    /// Controller for managing Morrisons product catalog synchronization.
    /// <PERSON>les fetching and updating product data from Morrisons API.
    /// </summary>
    public class MorrisonsProductController : Controller
    {
        private readonly HttpClient _httpClient;
        private readonly Data _data;
        private readonly IHubContext<SyncProgressHub> _hubContext;
        private readonly MorrisonsApiConfig _apiConfig;

        /// <summary>
        /// Initializes a new instance of the MorrisonsProductController.
        /// </summary>
        /// <param name="httpClient">HTTP client for making API requests</param>
        /// <param name="data">Database context for data operations</param>
        /// <param name="hubContext">SignalR hub context for progress updates</param>
        /// <param name="configuration">Configuration for API settings</param>
        public MorrisonsProductController(
            HttpClient httpClient, 
            Data data, 
            IHubContext<SyncProgressHub> hubContext,
            IConfiguration configuration)
        {
            _httpClient = httpClient;
            _data = data;
            _hubContext = hubContext;
            _apiConfig = configuration.GetSection("MorrisonsApi").Get<MorrisonsApiConfig>();
        }

        /// <summary>
        /// Displays the main view for Morrisons product management.
        /// </summary>
        /// <returns>The index view</returns>
        public IActionResult Index()
        {
            return View();
        }

        /// <summary>
        /// Fetches the complete product catalog from Morrisons API.
        /// </summary>
        /// <returns>A list of MorrisonsProductItem objects representing the product catalog</returns>
        /// <remarks>
        /// Makes an authenticated request to the Morrisons wholesale API to retrieve
        /// product information. Currently limited to 10,000 products.
        /// </remarks>
        public async Task<List<MorrisonsProductItem>> FetchProductCatalogAsync()
        {
            string url = $"{_apiConfig.ApiUrl}/{_apiConfig.CustomerId}/catalogues/{_apiConfig.CatalogueId}/products?start=0&limit={_apiConfig.ProductLimit}&apikey={_apiConfig.ApiKey}";
            string authToken = _apiConfig.AuthToken;

            HttpClientHandler handler = new HttpClientHandler
            {
                ServerCertificateCustomValidationCallback = (message, cert, chain, errors) => true
            };

            HttpClient httpClient = new HttpClient(handler);

            httpClient.DefaultRequestHeaders.Authorization = new AuthenticationHeaderValue("Basic", authToken);
            httpClient.DefaultRequestHeaders.Accept.Add(new MediaTypeWithQualityHeaderValue("application/json"));

            HttpResponseMessage response = await httpClient.GetAsync(url);

            if (response.IsSuccessStatusCode)
            {
                string json = await response.Content.ReadAsStringAsync();
                var products = JsonSerializer.Deserialize<MorrisonsProductResponse>(json, new JsonSerializerOptions
                {
                    PropertyNameCaseInsensitive = true
                });

                return products?.Items ?? new List<MorrisonsProductItem>();
            }

            return new List<MorrisonsProductItem>();
        }

        /// <summary>
        /// Synchronizes the local database with the full Morrisons product catalog.
        /// </summary>
        /// <returns>A view result indicating the completion of the synchronization</returns>
        /// <remarks>
        /// This method:
        /// - Fetches the complete product catalog from Morrisons
        /// - Creates or updates subcategories and divisions as needed
        /// - Creates new products or updates existing ones
        /// - Assigns VAT codes based on tax rates
        /// - Creates product-store associations for all stores of the brand
        /// 
        /// The method implements soft-delete pattern as per ADR-001.
        /// </remarks>
        public async Task<IActionResult> FetchFullCatalog()
        {
            List<SubCatagory> newSubCatagories = new List<SubCatagory>();
            List<Division> newDivisions = new List<Division>();
            List<Product> newProducts = new List<Product>();
            int errorCount = 0;
            
            try
            {
                var productsExts = await FetchProductCatalogAsync();
                int totalProducts = productsExts.Count;
                int processedCount = 0;

                await _hubContext.Clients.All.SendAsync("ReceiveProgress", 
                    "Starting product synchronization...", 0, totalProducts);

                var brand = _data.Brands.First(b => b.Name == "Morrison");
                Vat code0 = _data.Vats.FirstOrDefault(v => v.Code == "Code0");
                Vat code1 = _data.Vats.FirstOrDefault(v => v.Code == "Code1");
                Vat code2 = _data.Vats.FirstOrDefault(v => v.Code == "Code2");
                List<SubCatagory> existingSubCatagories = _data.SubCatagories.ToList();
                List<Division> existingDivisions = _data.Divisions.ToList();
                List<string> exisintProductBarcodes = _data.Products.Select(p => p.Barcode).ToList();

                foreach (var productExt in productsExts)
                {
                    try
                    {
                        if (exisintProductBarcodes.Contains(productExt.BarcodeEan)
                            || newProducts.Any(p => p.Barcode == productExt.BarcodeEan)) 
                        {
                            processedCount++;
                            await _hubContext.Clients.All.SendAsync("ReceiveProgress",
                                $"Skipping duplicate product {productExt.BarcodeEan} ({processedCount} of {totalProducts})",
                                processedCount, totalProducts);
                            continue;
                        }

                        var subCategory = existingSubCatagories.FirstOrDefault(s => s.Name == productExt.TradingGroup)
                            ?? newSubCatagories.FirstOrDefault(s => s.Name == productExt.TradingGroup)
                            ?? CreateNewSubCategory(productExt.TradingGroup, newSubCatagories);

                        var division = existingDivisions.FirstOrDefault(d => d.Name == productExt.Department)
                            ?? newDivisions.FirstOrDefault(d => d.Name == productExt.Department)
                            ?? CreateNewDivision(productExt.Department, subCategory, newDivisions);

                        if (_data.Products.All(p => p.Barcode != productExt.BarcodeEan))
                        {
                            var vat = DetermineVatCode(productExt, code0, code1, code2);
                            if (vat == null)
                            {
                                processedCount++;
                                await _hubContext.Clients.All.SendAsync("ReceiveProgress",
                                    $"Skipping product {productExt.BarcodeEan} - Invalid VAT ({processedCount} of {totalProducts})",
                                    processedCount, totalProducts);
                                continue;
                            }

                            var dbProduct = CreateNewProduct(productExt, brand.Id, vat, division);
                            newProducts.Add(dbProduct);
                            processedCount++;
                            await _hubContext.Clients.All.SendAsync("ReceiveProgress",
                                $"Added new product {productExt.Description} ({processedCount} of {totalProducts})",
                                processedCount, totalProducts);
                        }
                        else
                        {
                            UpdateExistingProduct(_data.Products.First(p => p.Barcode == productExt.BarcodeEan), productExt);
                            processedCount++;
                            await _hubContext.Clients.All.SendAsync("ReceiveProgress",
                                $"Updated existing product {productExt.Description} ({processedCount} of {totalProducts})",
                                processedCount, totalProducts);
                        }
                    }
                    catch (Exception ex)
                    {
                        errorCount++;
                        processedCount++;
                        await _hubContext.Clients.All.SendAsync("ReceiveError", 
                            $"Error processing product {productExt.BarcodeEan}: {ex.Message}");
                        await _hubContext.Clients.All.SendAsync("ReceiveProgress",
                            $"Error with product {productExt.BarcodeEan} ({processedCount} of {totalProducts})",
                            processedCount, totalProducts);
                        continue;
                    }
                }

                SaveChangesToDatabase(newSubCatagories, newDivisions, newProducts, brand);

                var result = new
                {
                    NewProducts = newProducts.Count,
                    ErrorCount = errorCount,
                    TotalProcessed = processedCount
                };

                await _hubContext.Clients.All.SendAsync("SyncComplete", result);
                
                return View("Index", result);
            }
            catch (Exception ex)
            {
                await _hubContext.Clients.All.SendAsync("SyncError", 
                    "Synchronization failed: " + ex.Message);
                return View("Index", new { Error = ex.Message });
            }
        }

        #region Helper Methods

        private SubCatagory CreateNewSubCategory(string tradingGroup, List<SubCatagory> newSubCatagories)
        {
            var subCategory = new SubCatagory
            {
                Name = tradingGroup,
                StockType = StockType.Stock,
                Catagory = Catagory.ShopSales,
            };
            newSubCatagories.Add(subCategory);
            return subCategory;
        }

        private Division CreateNewDivision(string department, SubCatagory subCategory, List<Division> newDivisions)
        {
            var division = new Division
            {
                Name = department,
                SubCatagory = subCategory,
            };
            newDivisions.Add(division);
            return division;
        }

        private Vat DetermineVatCode(MorrisonsProductItem product, Vat code0, Vat code1, Vat code2)
        {
            var vatRate = product.Prices.First(p => p.Type == "VAT").Value;
            return vatRate switch
            {
                20 => code0,
                5 => code1,
                0 => code2,
                _ => null
            };
        }

        private Product CreateNewProduct(MorrisonsProductItem productExt, int brandId, Vat vat, Division division)
        {
            return new Product
            {
                Barcode = productExt.BarcodeEan,
                BrandId = brandId,
                Description = productExt.Description,
                Name = productExt.Description,
                PLU = productExt.ItemId,
                SellingPrice = productExt.Prices.First(p => p.Type == "RRP" && p.Currency == "GBP").Value,
                Vat = vat,
                Division = division,
            };
        }

        private void UpdateExistingProduct(Product existingProduct, MorrisonsProductItem productExt)
        {
            existingProduct.Description = productExt.Description;
            existingProduct.SellingPrice = productExt.Prices.First(p => p.Type == "RRP" && p.Currency == "GBP").Value;
        }

        private void SaveChangesToDatabase(List<SubCatagory> newSubCatagories, List<Division> newDivisions, 
            List<Product> newProducts, Brand brand)
        {
            _data.SubCatagories.AddRange(newSubCatagories);
            _data.Divisions.AddRange(newDivisions);
            _data.Products.AddRange(newProducts);
            _data.SaveChanges();

            var storesForBrand = _data.Stores.Where(s => s.BrandId == brand.Id).ToList();
            foreach (var newProduct in newProducts)
            {
                foreach (var store in storesForBrand)
                {
                    _data.ProductStores.Add(new ProductStore
                    {
                        InventoryCount = 0,
                        Product = newProduct,
                        Store = store,
                    });
                }
            }
            _data.SaveChanges();
        }

        #endregion

        // Update function - fetches and updates existing products
        public async Task<IActionResult> UpdateCatalog()
        {
            // Call Morrisons API to get updated product data
            // Update existing records in the database
            return View(); // Return appropriate response
        }
    }
}
