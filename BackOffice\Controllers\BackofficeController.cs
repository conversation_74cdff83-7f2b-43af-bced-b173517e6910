﻿using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using POS.Core.Models;
using System.Security.Claims;
using System.Threading.Tasks;

namespace BackOffice.Controllers
{
    public class BackofficeController : Controller
    {
        private readonly Data _context;

        public BackofficeController(Data context)
        {
            _context = context;
        }

        public async Task<IActionResult> Index()
        {
            // Get the current user's ID
            var userId = int.Parse(User.Claims.FirstOrDefault(c => c.Type == ClaimTypes.NameIdentifier)?.Value);
            
            // Find the BackOffice user
            var backOffice = await _context.BackOffices
                .Include(b => b.Store)
                .FirstOrDefaultAsync(b => b.Id == userId);
            
            if (backOffice == null)
            {
                return NotFound();
            }
            
            // Pass the store to the view
            ViewBag.Store = backOffice.Store;
            
            return View();
        }

        public async Task<IActionResult> OnHoldTransactions()
        {
            // Get the current user's ID
            var userId = int.Parse(User.Claims.FirstOrDefault(c => c.Type == ClaimTypes.NameIdentifier)?.Value);
            
            // Find the BackOffice user
            var backOffice = await _context.BackOffices
                .Include(b => b.Store)
                .FirstOrDefaultAsync(b => b.Id == userId);
            
            if (backOffice == null || backOffice.StoreId == null)
            {
                return NotFound();
            }
            
            // Get on-hold transactions for this store
            var onHoldSales = await _context.Sales
                .Include(s => s.Shift)
                .ThenInclude(s => s.PosMachine)
                .Include(s => s.ProductStoreSales)
                .ThenInclude(pss => pss.ProductStore)
                .ThenInclude(ps => ps.Product)
                .Where(s => s.Shift.PosMachine.StoreId == backOffice.StoreId && s.IsOnHold)
                .ToListAsync();
            
            return View(onHoldSales);
        }

        public async Task<IActionResult> DeletedSales()
        {
            // Get the current user's ID
            var userId = int.Parse(User.Claims.FirstOrDefault(c => c.Type == ClaimTypes.NameIdentifier)?.Value);
            
            // Find the BackOffice user
            var backOffice = await _context.BackOffices
                .Include(b => b.Store)
                .FirstOrDefaultAsync(b => b.Id == userId);
            
            if (backOffice == null || backOffice.StoreId == null)
            {
                return NotFound();
            }
            
            // Get deleted sales for this store
            var deletedSales = await _context.Sales
                .Include(s => s.Shift)
                .ThenInclude(s => s.PosMachine)
                .Include(s => s.ProductStoreSales)
                .ThenInclude(pss => pss.ProductStore)
                .ThenInclude(ps => ps.Product)
                .Where(s => s.Shift.PosMachine.StoreId == backOffice.StoreId && s.IsDeleted)
                .ToListAsync();
            
            return View(deletedSales);
        }

        public async Task<IActionResult> VoidSales()
        {
            // Get the current user's ID
            var userId = int.Parse(User.Claims.FirstOrDefault(c => c.Type == ClaimTypes.NameIdentifier)?.Value);
            
            // Find the BackOffice user
            var backOffice = await _context.BackOffices
                .Include(b => b.Store)
                .FirstOrDefaultAsync(b => b.Id == userId);
            
            if (backOffice == null || backOffice.StoreId == null)
            {
                return NotFound();
            }
            
            // Get all sales for this store
            var storeSales = await _context.Sales
                .Include(s => s.Shift)
                .ThenInclude(s => s.PosMachine)
                .Where(s => s.Shift.PosMachine.StoreId == backOffice.StoreId)
                .ToListAsync();
            
            // For now, return all sales
            // In a real implementation, you would need to filter these based on your void criteria
            return View(storeSales);
        }

        public async Task<IActionResult> TerminalSales()
        {
            // Get the current user's ID
            var userId = int.Parse(User.Claims.FirstOrDefault(c => c.Type == ClaimTypes.NameIdentifier)?.Value);
            
            // Find the BackOffice user
            var backOffice = await _context.BackOffices
                .Include(b => b.Store)
                .FirstOrDefaultAsync(b => b.Id == userId);
            
            if (backOffice == null || backOffice.StoreId == null)
            {
                return NotFound();
            }
            
            // Get all terminals for this store
            var terminals = await _context.PosMachines
                .Where(p => p.StoreId == backOffice.StoreId)
                .ToListAsync();
            
            return View(terminals);
        }

        public async Task<IActionResult> TerminalSalesDetails(int id, DateTime? startDate = null, DateTime? endDate = null)
        {
            // Get the current user's ID
            var userId = int.Parse(User.Claims.FirstOrDefault(c => c.Type == ClaimTypes.NameIdentifier)?.Value);
            
            // Find the BackOffice user
            var backOffice = await _context.BackOffices
                .Include(b => b.Store)
                .FirstOrDefaultAsync(b => b.Id == userId);
            
            if (backOffice == null || backOffice.StoreId == null)
            {
                return NotFound();
            }
            
            // Get the terminal
            var terminal = await _context.PosMachines
                .FirstOrDefaultAsync(p => p.Id == id && p.StoreId == backOffice.StoreId);
            
            if (terminal == null)
            {
                return NotFound();
            }
            
            // Query for sales on this terminal
            var query = _context.Sales
                .Include(s => s.Shift)
                .ThenInclude(s => s.PosMachine)
                .Include(s => s.ProductStoreSales)
                .ThenInclude(pss => pss.ProductStore)
                .ThenInclude(ps => ps.Product)
                .Where(s => s.Shift.PosMachineId == id);
            
            // Apply date filters if provided
            if (startDate.HasValue)
            {
                query = query.Where(s => s.Date >= startDate.Value);
            }
            
            if (endDate.HasValue)
            {
                // Add one day to include the end date fully
                var nextDay = endDate.Value.AddDays(1);
                query = query.Where(s => s.Date < nextDay);
            }
            
            // Get the sales
            var sales = await query.ToListAsync();
            
            // Pass the terminal to the view
            ViewBag.Terminal = terminal;
            
            return View(sales);
        }
    }
}
