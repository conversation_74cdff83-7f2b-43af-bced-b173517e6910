﻿<?xml version="1.0" encoding="utf-8"?>
<nlog xmlns="http://www.nlog-project.org/schemas/NLog.xsd"
      xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
      autoReload="true">

	<!-- Define targets -->
	<targets>
		<!-- Write logs to a file -->
		<target name="file" xsi:type="File" fileName="logs/app.log"
				layout="${longdate} | ${level:uppercase=true} | ${message} ${exception}" />

		<!-- Write logs to the console -->
		<target name="console" xsi:type="Console" layout="${longdate} | ${level:uppercase=true} | ${message}" />
	</targets>

	<!-- Define logging rules -->
	<rules>
		<!-- Log everything from Debug level and above -->
		<logger name="*" minlevel="Debug" writeTo="file,console" />
	</rules>

</nlog>
