@model IEnumerable<POS.Core.Models.ProductStore>

@foreach (var productStore in Model)
{
    <tr>
        <td>
            <input type="hidden" name="ProductStoreId" value="@productStore.ProductStoreId" />
            @productStore.Product.Name
        </td>
        <td>
            @productStore.Product.SellingPrice.ToString("C")
        </td>
        <td>
            <input type="number" step="0.01" name="StoreSpecificPrice"
                   value="@(productStore.StoreSpecificPrice.HasValue ? productStore.StoreSpecificPrice.Value.ToString("0.00") : "")"
                   class="form-control" placeholder="N/A" />
        </td>
    </tr>
}