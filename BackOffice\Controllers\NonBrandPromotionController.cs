using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Mvc.Rendering;
using Microsoft.EntityFrameworkCore;
using POS.Core.Models;
using Microsoft.AspNetCore.Authorization;
using System.Security.Claims;
using TimeManagement;

namespace BackOffice.Controllers
{
    [Authorize(Roles = "BackOffice")]
    public class NonBrandPromotionController : Controller
    {
        private readonly Data _context;

        public NonBrandPromotionController(Data context)
        {
            _context = context;
        }

        public async Task<IActionResult> Index(int page = 1, int pageSize = 10)
        {
            var userId = int.Parse(User.Claims.FirstOrDefault(c => c.Type == ClaimTypes.NameIdentifier)?.Value);
            var storeId = _context.BackOffices.Find(userId).StoreId;

            var totalCount = await _context.NonBrandPromotions
                .Where(p => p.StoreId == storeId)
                .CountAsync();

            var promotions = await _context.NonBrandPromotions
                .Include(p => p.Products)
                .Where(p => p.StoreId == storeId)
                .OrderByDescending(p => p.StartTime)
                .Skip((page - 1) * pageSize)
                .Take(pageSize)
                .ToListAsync();

            ViewBag.CurrentPage = page;
            ViewBag.TotalPages = (int)Math.Ceiling((double)totalCount / pageSize);

            return View(promotions);
        }

        public async Task<IActionResult> Details(int id)
        {
            var userId = int.Parse(User.Claims.FirstOrDefault(c => c.Type == ClaimTypes.NameIdentifier)?.Value);
            var storeId = _context.BackOffices.Find(userId).StoreId;

            var promotion = await _context.NonBrandPromotions
                .Include(p => p.Products)
                .FirstOrDefaultAsync(p => p.Id == id && p.StoreId == storeId);

            if (promotion == null)
                return NotFound();

            return View(promotion);
        }

        public IActionResult Create()
        {
            var userId = int.Parse(User.Claims.FirstOrDefault(c => c.Type == ClaimTypes.NameIdentifier)?.Value);
            var storeId = _context.BackOffices.Find(userId).StoreId;

            var eligibleProducts = _context.Products
                .Where(p => !p.SpecialProduct && p.BrandId == null)
                .OrderBy(p => p.Name)
                .ToList();

            ViewBag.Products = new MultiSelectList(eligibleProducts, "Id", "Name");
            return View();
        }

        [HttpPost]
        [ValidateAntiForgeryToken]
        public async Task<IActionResult> Create(NonBrandPromotion promotion, int[] selectedProducts, string productPlus)
        {
            var userId = int.Parse(User.Claims.FirstOrDefault(c => c.Type == ClaimTypes.NameIdentifier)?.Value);
            var backoffice = _context.BackOffices.Find(userId);

            promotion.CreatedById = userId;
            promotion.StoreId = (int)backoffice.StoreId;
            
            // Process products from selectedProducts (from hidden select)
            if (selectedProducts != null && selectedProducts.Length > 0)
            {
                promotion.Products = await _context.Products
                    .Where(p => selectedProducts.Contains(p.Id) && !p.SpecialProduct && p.BrandId == null)
                    .ToListAsync();
            }
            // If no products were selected but PLUs were provided, try to find products by PLU
            else if (!string.IsNullOrWhiteSpace(productPlus))
            {
                var plus = productPlus.Split(new[] { ',', ' ', '\r', '\n' }, StringSplitOptions.RemoveEmptyEntries);
                promotion.Products = await _context.Products
                    .Where(p => plus.Contains(p.PLU) && !p.SpecialProduct && p.BrandId == null)
                    .ToListAsync();
            }

            _context.NonBrandPromotions.Add(promotion);
            await _context.SaveChangesAsync();
            return RedirectToAction(nameof(Index));
        }

        public async Task<IActionResult> Edit(int id)
        {
            var userId = int.Parse(User.Claims.FirstOrDefault(c => c.Type == ClaimTypes.NameIdentifier)?.Value);
            var storeId = _context.BackOffices.Find(userId).StoreId;

            var promotion = await _context.NonBrandPromotions
                .Include(p => p.Products)
                .FirstOrDefaultAsync(p => p.Id == id && p.StoreId == storeId);

            if (promotion == null)
                return NotFound();

            // Get the selected products with their PLUs
            var selectedProducts = promotion.Products
                .Select(p => new { id = p.Id, text = $"{p.PLU} - {p.Name}" })
                .ToList();

            ViewBag.SelectedProductList = selectedProducts;
            
            // Get all non-brand products with their PLUs
            var allProducts = _context.Products
                .Where(p => !p.SpecialProduct && p.BrandId == null)
                .Select(p => new { Id = p.Id, Name = $"{p.PLU} - {p.Name}" })
                .ToList();
            
            ViewBag.Products = new MultiSelectList(
                allProducts,
                "Id", "Name",
                promotion.Products.Select(p => p.Id));

            return View(promotion);
        }

        [HttpPost]
        [ValidateAntiForgeryToken]
        public async Task<IActionResult> Edit(int id, NonBrandPromotion promotion, int[] selectedProducts, string productPlus)
        {
            if (id != promotion.Id)
            {
                return NotFound();
            }

            try
            {
                var existingPromotion = await _context.NonBrandPromotions
                    .Include(p => p.Products)
                    .FirstOrDefaultAsync(p => p.Id == id);

                if (existingPromotion == null)
                {
                    return NotFound();
                }

                // Update promotion properties
                existingPromotion.Name = promotion.Name;
                existingPromotion.StartTime = promotion.StartTime;
                existingPromotion.EndTime = promotion.EndTime;
                existingPromotion.Type = promotion.Type;
                existingPromotion.PromotionPrice = promotion.PromotionPrice;
                existingPromotion.Quantity = promotion.Quantity;

                // Clear existing products
                existingPromotion.Products.Clear();

                // Process products from selectedProducts (from hidden select)
                if (selectedProducts != null && selectedProducts.Length > 0)
                {
                    var products = await _context.Products
                        .Where(p => selectedProducts.Contains(p.Id) && !p.SpecialProduct && p.BrandId == null)
                        .ToListAsync();
                    
                    foreach (var product in products)
                    {
                        existingPromotion.Products.Add(product);
                    }
                }
                // If no products were selected but PLUs were provided, try to find products by PLU
                else if (!string.IsNullOrWhiteSpace(productPlus))
                {
                    var plus = productPlus.Split(new[] { ',', ' ', '\r', '\n' }, StringSplitOptions.RemoveEmptyEntries);
                    var products = await _context.Products
                        .Where(p => plus.Contains(p.PLU) && !p.SpecialProduct && p.BrandId == null)
                        .ToListAsync();
                    
                    foreach (var product in products)
                    {
                        existingPromotion.Products.Add(product);
                    }
                }

                _context.Update(existingPromotion);
                await _context.SaveChangesAsync();
            }
            catch (DbUpdateConcurrencyException)
            {
                if (!NonBrandPromotionExists(promotion.Id))
                {
                    return NotFound();
                }
                else
                {
                    throw;
                }
            }
            return RedirectToAction(nameof(Index));
        }

        [HttpPost]
        [ValidateAntiForgeryToken]
        public async Task<IActionResult> Delete(int id)
        {
            var userId = int.Parse(User.Claims.FirstOrDefault(c => c.Type == ClaimTypes.NameIdentifier)?.Value);
            var storeId = _context.BackOffices.Find(userId).StoreId;

            var promotion = await _context.NonBrandPromotions
                .FirstOrDefaultAsync(p => p.Id == id && p.StoreId == storeId);

            if (promotion != null)
            {
                _context.NonBrandPromotions.Remove(promotion);
                await _context.SaveChangesAsync();
            }

            return RedirectToAction(nameof(Index));
        }

        private bool NonBrandPromotionExists(int id)
        {
            var userId = int.Parse(User.Claims.FirstOrDefault(c => c.Type == ClaimTypes.NameIdentifier)?.Value);
            var storeId = _context.BackOffices.Find(userId).StoreId;

            return _context.NonBrandPromotions.Any(e => e.Id == id && e.StoreId == storeId);
        }
    }
}
