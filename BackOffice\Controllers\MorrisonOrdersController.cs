﻿using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using POS.Core.Models;
using System.Security.Claims;
using TimeManagement;

namespace BackOffice.Controllers
{
    public class MorrisonOrdersController : Controller
    {
        private readonly Data _context;

        public MorrisonOrdersController(Data context)
        {
            _context = context;
        }

        // GET: MorrisonOrders
        public async Task<IActionResult> Index()
        {
            var userId = int.Parse(User.Claims.FirstOrDefault(c => c.Type == ClaimTypes.NameIdentifier)?.Value);
            var storeId = _context.BackOffices.Find(userId).StoreId;


            var orders = await _context.MorrisonOrders.Include(o => o.Store)
                .Where(m => m.StoreId == storeId).ToListAsync();
            return View(orders);
        }

        // GET: MorrisonOrders/Details/5
        public async Task<IActionResult> Details(int? id)
        {
            if (id == null) return NotFound();

            var order = await _context.MorrisonOrders
                .Include(o => o.Store)
                .Include(o => o.OrderItems)
                .FirstOrDefaultAsync(m => m.Id == id);
            if (order == null) return NotFound();

            return View(order);
        }

        // GET: MorrisonOrders/Create
        public IActionResult Create()
        {
            ViewBag.Stores = _context.Stores.ToList(); // Load store list
            return View();
        }

        // POST: MorrisonOrders/Create
        [HttpPost]
        [ValidateAntiForgeryToken]
        public async Task<IActionResult> Create(MorrisonOrder order)
        {
            if (ModelState.IsValid)
            {
                order.CreatedDate = CustomTimeProvider.Now;
                order.MorrisonOrderStatus = MorrisonOrderStatus.Created;
                _context.Add(order);
                await _context.SaveChangesAsync();
                return RedirectToAction(nameof(Index));
            }
            return View(order);
        }

        // GET: MorrisonOrders/Edit/5
        public async Task<IActionResult> Edit(int? id)
        {
            if (id == null) return NotFound();
            var order = await _context.MorrisonOrders.FindAsync(id);
            if (order == null) return NotFound();
            return View(order);
        }

        // POST: MorrisonOrders/Edit/5
        [HttpPost]
        [ValidateAntiForgeryToken]
        public async Task<IActionResult> Edit(int id, MorrisonOrder order)
        {
            if (id != order.Id) return NotFound();

            if (ModelState.IsValid)
            {
                _context.Update(order);
                await _context.SaveChangesAsync();
                return RedirectToAction(nameof(Index));
            }
            return View(order);
        }

        // GET: MorrisonOrders/Delete/5
        public async Task<IActionResult> Delete(int? id)
        {
            if (id == null) return NotFound();
            var order = await _context.MorrisonOrders.FindAsync(id);
            if (order == null) return NotFound();
            return View(order);
        }

        // POST: MorrisonOrders/Delete/5
        [HttpPost, ActionName("Delete")]
        [ValidateAntiForgeryToken]
        public async Task<IActionResult> DeleteConfirmed(int id)
        {
            var order = await _context.MorrisonOrders.FindAsync(id);
            if (order != null)
            {
                _context.MorrisonOrders.Remove(order);
                await _context.SaveChangesAsync();
            }
            return RedirectToAction(nameof(Index));
        }
    }
}
