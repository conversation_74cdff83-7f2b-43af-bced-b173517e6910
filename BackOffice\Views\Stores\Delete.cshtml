﻿@model POS.Core.Models.Store

@{
    ViewData["Title"] = "Delete Store";
}

<h1>Delete Store</h1>

<div>
    <h4>Store</h4>
    <hr />
    <dl class="row">
        <dt class="col-sm-2">Name</dt>
        <dd class="col-sm-10">@Model.StoreName</dd>
        <dt class="col-sm-2">Cashier</dt>
    </dl>
</div>

<form asp-action="DeleteConfirmed">
    <input type="hidden" asp-for="StoreId" />
    <button type="submit" class="btn btn-danger">Delete</button>
    <a asp-action="Index" class="btn btn-secondary">Cancel</a>
</form>

