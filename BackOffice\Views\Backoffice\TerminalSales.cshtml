@model IEnumerable<POS.Core.Models.PosMachine>

@{
    ViewData["Title"] = "Terminal Sales";
}

<h2>Terminal Sales</h2>

<div class="mb-3">
    <a asp-action="Index" class="btn btn-secondary">Back to Dashboard</a>
</div>

@if (Model.Any())
{
    <div class="row">
        @foreach (var terminal in Model)
        {
            <div class="col-md-4 mb-4">
                <div class="card">
                    <div class="card-header">
                        <h5>Terminal #@terminal.Id</h5>
                    </div>
                    <div class="card-body">
                        <p><strong>Store:</strong> @terminal.Store.StoreName</p>
                        <a asp-action="TerminalSalesDetails" asp-route-id="@terminal.Id" class="btn btn-primary">
                            View Sales
                        </a>
                    </div>
                </div>
            </div>
        }
    </div>
}
else
{
    <div class="alert alert-info">
        No terminals found for this store.
    </div>
}

