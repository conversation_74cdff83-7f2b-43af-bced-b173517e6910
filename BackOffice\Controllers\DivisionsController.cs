﻿using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using POS.Core.Models;

namespace BackOffice.Controllers
{
    public class DivisionsController : Controller
    {
        private readonly Data _context;

        public DivisionsController(Data context)
        {
            _context = context;
        }

        // GET: Divisions
        public async Task<IActionResult> Index()
        {
            var divisions = _context.Divisions.Include(d => d.SubCatagory);
            return View(await divisions.ToListAsync());
        }

        // GET: Divisions/Create
        public IActionResult Create()
        {
            ViewBag.SubCatagories = _context.SubCatagories.ToList();
            return View();
        }

        // POST: Divisions/Create
        [HttpPost]
        [ValidateAntiForgeryToken]
        public async Task<IActionResult> Create(Division division)
        {
            _context.Add(division);
            await _context.SaveChangesAsync();
            return RedirectToAction(nameof(Index));
        }

        // GET: Divisions/Edit/5
        public async Task<IActionResult> Edit(int id)
        {
            var division = await _context.Divisions.FindAsync(id);
            if (division == null) return NotFound();

            ViewBag.SubCatagories = _context.SubCatagories.ToList();
            return View(division);
        }

        // POST: Divisions/Edit/5
        [HttpPost]
        [ValidateAntiForgeryToken]
        public async Task<IActionResult> Edit(int id, Division division)
        {
            if (id != division.Id) return NotFound();

            _context.Update(division);
            await _context.SaveChangesAsync();
            return RedirectToAction(nameof(Index));


        }

        // GET: Divisions/Delete/5
        public async Task<IActionResult> Delete(int id)
        {
            var division = await _context.Divisions
                .Include(d => d.SubCatagory)
                .FirstOrDefaultAsync(m => m.Id == id);

            if (division == null) return NotFound();

            return View(division);
        }

        // POST: Divisions/Delete/5
        [HttpPost, ActionName("Delete")]
        [ValidateAntiForgeryToken]
        public async Task<IActionResult> DeleteConfirmed(int id)
        {
            var division = await _context.Divisions.FindAsync(id);
            if (division != null)
            {
                _context.Divisions.Remove(division);
                await _context.SaveChangesAsync();
            }
            return RedirectToAction(nameof(Index));
        }
    }
}
