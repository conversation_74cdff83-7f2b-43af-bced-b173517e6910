using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Hosting;
using Microsoft.Net.Http.Headers;
using POS.Core.Models;
using System.Net.Http.Headers;
using System.Text.Json;
using TimeManagement;

namespace BackOffice
{
    public class MorrisonOrderConfirmationService : BackgroundService
    {
        private readonly IServiceProvider _serviceProvider;
        private readonly ILogger<MorrisonOrderConfirmationService> _logger;

        public MorrisonOrderConfirmationService(
            IServiceProvider serviceProvider,
            ILogger<MorrisonOrderConfirmationService> logger)
        {
            _serviceProvider = serviceProvider;
            _logger = logger;
        }

        protected override async Task ExecuteAsync(CancellationToken stoppingToken)
        {
            while (!stoppingToken.IsCancellationRequested)
            {
                try
                {
                    await ProcessPendingConfirmations();
                }
                catch (Exception ex)
                {
                    _logger.LogError(ex, "Error processing Morrison order confirmations");
                }

                // Wait for 1 hour before next execution
                await Task.Delay(TimeSpan.FromHours(1), stoppingToken);
            }
        }

        private async Task ProcessPendingConfirmations()
        {
            _logger.LogInformation("Starting Morrison order confirmation process at {time}", CustomTimeProvider.Now);
            
            using var scope = _serviceProvider.CreateScope();
            var context = scope.ServiceProvider.GetRequiredService<Data>();
            
            var pendingOrders = await context.MorrisonOrders
                .Where(o => o.MorrisonOrderStatus == MorrisonOrderStatus.Created)
                .ToListAsync();
                
            _logger.LogInformation("Found {count} orders to confirm", pendingOrders.Count);
            
            foreach (var order in pendingOrders)
            {
                try
                {
                    await ConfirmOrder(order.GeneratedId);
                    order.MorrisonOrderStatus = MorrisonOrderStatus.Confirmed;
                    order.LastModified = CustomTimeProvider.Now;
                    await context.SaveChangesAsync();
                    _logger.LogInformation("Successfully confirmed order {orderId}", order.GeneratedId);
                }
                catch (Exception ex)
                {
                    _logger.LogError(ex, "Failed to confirm order {orderId}", order.GeneratedId);
                }
            }
        }

        private async Task ConfirmOrder(string orderId)
        {
            // TODO: Move this to config // 
            const string CustomerId = "samyltd";
            const string ApiKey = "DNfkuXuSFABqKhSl0gmUAzHgQqs1wPjH";
            const string authToken = "RE5ma3VYdVNGQUJxS2hTbDBnbVVBekhnUXFzMXdQakg6R0hmcERBemw0ODNHZVhIaA==";
            
            string url = $"https://sit-api.morrisons.com/wholesale/v1/customers/{CustomerId}/confirmations/{orderId}?apikey={ApiKey}";

            HttpClientHandler handler = new HttpClientHandler
            {
                ServerCertificateCustomValidationCallback = (message, cert, chain, errors) => true
            };

            using HttpClient httpClient = new HttpClient(handler);
            httpClient.DefaultRequestHeaders.Authorization = new AuthenticationHeaderValue("Basic", authToken);
            httpClient.DefaultRequestHeaders.Accept.Add(new MediaTypeWithQualityHeaderValue("application/json"));

            HttpResponseMessage response = await httpClient.GetAsync(url);
            
            if (!response.IsSuccessStatusCode)
            {
                throw new Exception($"API returned {response.StatusCode}: {await response.Content.ReadAsStringAsync()}");
            }
        }
    }
}