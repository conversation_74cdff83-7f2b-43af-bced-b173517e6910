﻿using Microsoft.EntityFrameworkCore;
using POS.Core.Models;

namespace BackOffice
{
    public class ReportsService
    {
        private readonly Data _context;

        public ReportsService(Data context)
        {
            _context = context;
        }



        public decimal SaleByCatagory(SubCatagory? subCatagory, DateTime startDate, DateTime endDate, Store? store)
        {
            if (subCatagory == null)
            {
                return _context.Sales
                      .Where(s =>
                      s.Date >= startDate && s.Date <= endDate
                      && s.IsOnHold == false)
                      .ToList().Sum(s => s.TotalValue);
            }
            else
            {
                return 0;
            }
        }
    }
}