﻿using Integral.Library.GuardianClient;
using Microsoft.EntityFrameworkCore;
using POS.Core.Models;
using POS.Printer;
using POS.WPF.Helpers;
using System;
using System.Collections.Generic;
using System.IO.Ports;
using System.Linq;
using System.Text;
using System.Text.RegularExpressions;
using System.Threading.Tasks;
using System.Windows;
using System.Windows.Controls;
using System.Windows.Data;
using System.Windows.Documents;
using System.Windows.Input;
using System.Windows.Media;
using System.Windows.Media.Imaging;
using System.Windows.Shapes;
using TimeManagement;
using WindowsInput;
using WindowsInput.Native;

namespace POS.WPF.Views
{
    /// <summary>
    /// Interaction logic for RefundWindow.xaml
    /// </summary>
    public partial class RefundWindow : Window
    {
        private SerialPort _serialPort;
        private string _scannedBarcode;
        private InputSimulator _inputSimulator;

        public RefundWindow()
        {
            InitializeComponent();
            InitializeScanner();
            LoadTodaysSales();
            Helpers.WindowStyleHelper.SetWindowStyle(this);
            _inputSimulator = new InputSimulator();
        }

        private void InitializeScanner()
        {

            if (ConfigurationHelper.ScannerType == "KEYBOARD")
            {
                barcodeTextBox.Visibility = Visibility.Visible;
                barcodeTextBox.Focus();
                //
            }
            else if (ConfigurationHelper.ScannerType == "HID")
            {

            }
            else
            {
                _serialPort = new SerialPort(ConfigurationHelper.ScannerPort, 9600, Parity.None, 8, StopBits.One)
                {
                    Handshake = Handshake.None,
                    ReadTimeout = 500,
                    WriteTimeout = 500
                };

                _serialPort.DataReceived += SerialPort_DataReceived;

                try
                {
                    _serialPort.Open(); // Open the port
                }
                catch (Exception ex)
                {
                    MessageBox.Show($"Error opening COM5: {ex.Message}");
                }
            }
        }

        private void SerialPort_DataReceived(object sender, SerialDataReceivedEventArgs e)
        {
            try
            {
                string scannedData = _serialPort.ReadExisting();
                Dispatcher.Invoke(() => ProcessInput(scannedData.Trim()));
            }
            catch (Exception ex)
            {
                Dispatcher.Invoke(() => MessageBox.Show($"Error reading data: {ex.Message}"));
            }
        }

        private void btnManualSearch_Click(object sender, RoutedEventArgs e)
        {
            ProcessInput(txtManualInput.Text.Trim());

            barcodeTextBox.Visibility = Visibility.Visible;
            barcodeTextBox.Focus();

        }

        private void txtManualInput_KeyDown(object sender, KeyEventArgs e)
        {
            if (e.Key == Key.Enter)
            {
                ProcessInput(txtManualInput.Text.Trim());
            }
        }

        private void ProcessInput(string input)
        {
            if (string.IsNullOrEmpty(input))
            {
                MessageBox.Show("Please enter a barcode or PLU.");
                return;
            }

            // Sanitize input
            input = Regex.Replace(input, @"\D", "");

            using (LocalData.LocalContext context = new LocalData.LocalContext())
            {
                try
                {
                    var sale = context.Sales
                        .Include(s => s.ProductStoreSales)
                            .ThenInclude(pss => pss.ProductStore)
                                .ThenInclude(ps => ps.Product)
                        .FirstOrDefault(s => s.Barcode == input ||
                                           s.ProductStoreSales.Any(pss => pss.ProductStore.Product.PLU == input));

                    if (sale != null)
                    {
                        _scannedBarcode = sale.Barcode;
                        labelReciptScanStatus.Content = $"Found: {sale.Barcode}";

                        var displayItems = sale.ProductStoreSales.Select(pss => new
                        {
                            DisplayText = $"{pss.ProductStore.Product.Name} - Qty: {pss.Quantity} @ {pss.Total / pss.Quantity:C} = {pss.Total:C}",
                            SaleItem = pss
                        }).ToList();

                        listProductSales.ItemsSource = displayItems;
                        //listProductSales.DisplayMemberPath = "DisplayText";

                        //if (_serialPort.IsOpen)
                        //{
                        //    _serialPort.DataReceived -= SerialPort_DataReceived;
                        //    _serialPort.Close();
                        //}
                    }
                    else
                    {
                        MessageBox.Show("No matching sale found.");
                    }
                }
                catch (Exception ex)
                {
                    MessageBox.Show($"Database error: {ex.Message}");
                }
            }
        }

        private void listProductSales_SelectionChanged(object sender, SelectionChangedEventArgs e)
        {
            quantityPanel.Visibility = listProductSales.SelectedItem != null
                ? Visibility.Visible
                : Visibility.Collapsed;
        }

        private async void btnProcessRefund_Click(object sender, RoutedEventArgs e)
        {
            if (listProductSales.SelectedItem == null)
            {
                MessageBox.Show("Please select a product to refund.");
                return;
            }

            if (!int.TryParse(txtQuantity.Text, out int refundQuantity) || refundQuantity <= 0)
            {
                MessageBox.Show("Please enter a valid quantity.");
                return;
            }

            dynamic selectedItem = listProductSales.SelectedItem;
            ProductStoreSale productStoreSale = selectedItem.SaleItem;

            if (refundQuantity > productStoreSale.Quantity)
            {
                MessageBox.Show($"Cannot refund more than purchased quantity ({productStoreSale.Quantity}).");
                return;
            }

            try
            {
                using (var context = new LocalData.LocalContext())
                {
                    // Reload the entity to ensure we have the latest data
                    var dbProductStoreSale = context.ProductStoreSales
                        .Include(pss => pss.ProductStore)
                        .First(pss => pss.ProductStoreSaleId == productStoreSale.ProductStoreSaleId);

                    decimal unitPrice = dbProductStoreSale.Total / dbProductStoreSale.Quantity;
                    decimal refundAmount = 0;
                    decimal totalReduction = 0;

                    if (!dbProductStoreSale.PriceOverriden &&
                        !dbProductStoreSale.StaffDiscount &&
                        !dbProductStoreSale.IsPromotion)
                    {
                        // Scenario 1: No special pricing
                        refundAmount = refundQuantity * unitPrice;
                        totalReduction = refundAmount;
                    }
                    else if (dbProductStoreSale.PriceOverriden)
                    {
                        refundAmount = (dbProductStoreSale.Total / dbProductStoreSale.Quantity) * refundQuantity;
                        totalReduction = refundAmount;
                    }
                    else if (dbProductStoreSale.StaffDiscount)
                    {
                        refundAmount = (dbProductStoreSale.Total / dbProductStoreSale.Quantity) * refundQuantity;
                        totalReduction = refundAmount;
                    }
                    else
                    {
                        if (dbProductStoreSale.IsPromotion)
                        {
                            MessageBox.Show("Promotion cannot be refunded.");
                            return;
                        }
                    }

                    // Check if this is a card refund
                    bool isCardRefund = chkCardRefund.IsChecked ?? false;
                    bool refundProcessed = true;

                    // Process card refund if selected
                    if (isCardRefund)
                    {
                        try
                        {
                            // Call the card refund processing method
                            refundProcessed = await ProcessCardRefund(refundAmount, ConfigurationHelper.PrinterName);
                        }
                        catch(Exception ex)
                        {
                            MessageBox.Show($"Card Interface error: {ex.Message}");
                        }
                        
                        if (!refundProcessed)
                        {
                            MessageBox.Show("Card refund failed. Please try again or process as cash refund.");
                            return;
                        }
                    }

                    // Update ProductStoreSale
                    dbProductStoreSale.Quantity -= refundQuantity;
                    dbProductStoreSale.Total -= totalReduction;

                    var sale = context.Sales.Include(s => s.ProductStoreSales)
                                            .ThenInclude(p => p.ProductStore)
                                            .ThenInclude(p => p.Product).First(s => s.SaleId == dbProductStoreSale.SaleId);

                    sale.TotalValue -= totalReduction;

                    // Create Refund record
                    var refund = new Refund
                    {
                        ProductStoreSaleId = dbProductStoreSale.ProductStoreSaleId,
                        Quantity = refundQuantity,
                        Amount = refundAmount,
                        Date = CustomTimeProvider.Now,
                        IsDeleted = false,
                        LastModified = CustomTimeProvider.Now
                    };

                    //// Create RefundPayment record to track payment method
                    //var paymentMethod = isCardRefund 
                    //    ? context.PaymentMethods.FirstOrDefault(pm => pm.Method == "Card")
                    //    : context.PaymentMethods.FirstOrDefault(pm => pm.Method == "Cash");

                    //if (paymentMethod != null)
                    //{
                    //    var refundPayment = new RefundPayment
                    //    {
                    //        Refund = refund,
                    //        PaymentMethodId = paymentMethod.Id,
                    //        PaymentMethod = paymentMethod,
                    //        Amount = refundAmount,
                    //        Date = CustomTimeProvider.Now,
                    //        IsDeleted = false,
                    //        LastModified = CustomTimeProvider.Now
                    //    };
                        
                    //    context.RefundPayments.Add(refundPayment);

                    //}

                    context.Refunds.Add(refund);
                    context.SaveChanges();

                    MessageBox.Show($"Refund processed successfully. Amount: {refundAmount:C}");

                    List<POS.Printer.Product> products = new List<POS.Printer.Product>();

                    foreach (var productStoreSale1 in sale.ProductStoreSales)
                    {
                        ProductPriceReductionType reductionType;

                        if (productStoreSale1.PriceOverriden)
                        {
                            reductionType = ProductPriceReductionType.PriceOverride;
                        }
                        else if (productStoreSale1.StaffDiscount)
                        {
                            reductionType = ProductPriceReductionType.StaffDiscount;
                        }
                        else if (productStoreSale1.IsPromotion)
                        {
                            reductionType = ProductPriceReductionType.Promotion;
                        }
                        else
                        {
                            reductionType = ProductPriceReductionType.None;
                        }

                        decimal reducedAmount;

                        if (productStoreSale.PriceOverriden)
                        {
                            reducedAmount = (decimal)productStoreSale.OverriddenDiscountAmount;
                        }
                        else if (productStoreSale.StaffDiscount)
                        {
                            reducedAmount = productStoreSale.StaffDiscountedAmount;
                        }
                        else
                        {
                            reducedAmount = 0;
                        }

                        products.Add(new Printer.Product($"{productStoreSale1.ProductStore.Product.Name} {productStoreSale1.Quantity}", productStoreSale1.Total, reductionType, reducedAmount));
                    }

                    //print receipt
                    POS.Printer.PrintData printData = new Printer.PrintData(
                        isCardRefund ? Printer.PaymentType.Card : Printer.PaymentType.Cash, 
                        null);

                    printData.StoreId = UiShift.StoreId.ToString();
                    printData.Time = sale.Date.ToString();
                    printData.CashierName = UiShift.CashierName;
                    printData.TransactionId = sale.SaleId.ToString();
                    printData.PaidAmount = "--";
                    printData.ChangeDue = "--";
                    printData.Products = products;
                    printData.Total = sale.TotalValue.ToString();
                    printData.VatNo = context.Stores.Find(UiShift.StoreId).VatNo;
                    printData.Barcode = sale.Barcode;
                    printData.StoreName = context.Stores.Find(UiShift.StoreId).StoreName;
                    printData.PosMachineId = UiShift.PosMachineId.ToString();
                    printData.CashierAlerts = GetCashierAlerts(sale.SaleId) ?? new List<string>();
                   // printData.IsRefund = true;
                   // printData.RefundAmount = refundAmount.ToString("C");

                    List<POS.Printer.VatEntry> vatEntries = GenerateVatEntries(sale.ProductStoreSales.ToList(), sale);
                    printData.VatEntries = vatEntries;

                    POS.Printer.Printer.Print(printData, ConfigurationHelper.Address, ConfigurationHelper.PrinterName);

                    this.Close();
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"Error processing refund: {ex.Message}");
            }
        }

        private List<string> GetCashierAlerts( int saleId)
        {
            using (LocalData.LocalContext context = new LocalData.LocalContext())
            {
                return context.ProductStoreSales
                    .Where(pss => pss.SaleId == saleId)
                    .Select(pss => pss.ProductStore.Product.Division.SubCatagory.CashierAlert)
                    .Where(alert => !string.IsNullOrEmpty(alert))
                    .Distinct()
                    .ToList();
            }
        }

        public static List<VatEntry> GenerateVatEntries( List<ProductStoreSale> productStoreSales, Sale sale)
        {
            using (LocalData.LocalContext dbContext = new LocalData.LocalContext())
            {
                return productStoreSales
                    .Select(productStoreSales =>
                    {
                        var productStore = dbContext.ProductStores.First(ps => ps.ProductStoreId == productStoreSales.ProductStoreId);

                        // Existing logic to fetch product/VAT data
                        var dbProduct = dbContext.Products.First(p => p.Id == productStore.ProductId);
                        var vatRate = dbContext.Vats.First(v => v.Id == dbProduct.VatId);

                        decimal totalPrice = sale.TotalValue;

                        decimal vatValue = vatRate.Value / 100;
                        decimal exVat = totalPrice / (1 + vatValue);
                        decimal vatAmount = totalPrice - exVat;

                        return new
                        {
                            Code = vatRate.Code,
                            Rate = vatRate.Rate,
                            ExVat = Math.Round(exVat, 2),
                            VatAmount = Math.Round(vatAmount, 2),
                            TotalPrice = Math.Round(totalPrice, 2)
                        };
                    })
                    // Group by VAT code and aggregate values
                    .GroupBy(x => x.Code)
                    .Select(g => new VatEntry(
                        g.Key,
                        g.First().Rate,  // Rate is consistent within a group
                        g.Sum(x => x.ExVat),
                        g.Sum(x => x.VatAmount),
                        g.Sum(x => x.TotalPrice)
                    ))
                    .ToList();
            }
        }


        private void RefreshSaleDisplay()
        {
            if (!string.IsNullOrEmpty(_scannedBarcode))
            {
                ProcessInput(_scannedBarcode);
            }
        }

        private void LoadTodaysSales()
        {
            using (var context = new LocalData.LocalContext())
            {
                var today = DateTime.Today;
                var sales = context.Sales
                    .Include(s => s.ProductStoreSales)
                    .Where(s => s.Date >= today && !s.IsDeleted)
                    .OrderByDescending(s => s.Date)
                    .ToList();

                salesListView.ItemsSource = sales;
            }
        }

        private void salesListView_SelectionChanged(object sender, SelectionChangedEventArgs e)
        {
            if (salesListView.SelectedItem is Sale selectedSale)
            {
                _scannedBarcode = selectedSale.Barcode;
                ProcessInput(selectedSale.Barcode);
            }
        }

        private void txtSearchSales_KeyDown(object sender, KeyEventArgs e)
        {
            if (e.Key == Key.Enter)
            {
                var searchText = txtSearchSales.Text.Trim();
                var today = DateTime.Today;

                using (var context = new LocalData.LocalContext())
                {
                    var filteredSales = context.Sales
                        .Include(s => s.ProductStoreSales)
                        .Where(s => s.Date >= today &&
                                   (s.Barcode.Contains(searchText) ||
                                    s.SaleId.ToString() == searchText))
                        .OrderByDescending(s => s.Date)
                        .ToList();

                    salesListView.ItemsSource = filteredSales;
                }
            }
        }

        private void btnRefreshSales_Click(object sender, RoutedEventArgs e)
        {
            LoadTodaysSales();
            txtSearchSales.Clear();
        }

        protected override void OnClosed(EventArgs e)
        {
            try
            {
                if (_serialPort.IsOpen)
                {
                    _serialPort.Close();
                }
                _serialPort.Dispose();
                base.OnClosed(e);
            }
            catch (Exception ex)
            {

            }
        }

        private void Button_Click(object sender, RoutedEventArgs e)
        {
            this.Close();
        }

        private void BarcodeTextBox_KeyDown(object sender, KeyEventArgs e)
        {
            // Check if the Enter key is pressed indicating the end of a barcode scan.
            if (e.Key == System.Windows.Input.Key.Enter)
            {
                // Retrieve and trim the scanned barcode value.
                string scannedBarcode = barcodeTextBox.Text.Trim();

                if (!string.IsNullOrEmpty(scannedBarcode))
                {
                    // Process the scanned barcode (e.g., search for the product)
                    ProcessInput(scannedBarcode);
                }

                // Clear the textbox for the next scan.
                barcodeTextBox.Clear();



                // Prevent further handling if needed.
                e.Handled = true;
            }
        }

        private void NumericKeypad_KeyPressed(object sender, string key)
        {
            txtQuantity.Focus();
            switch (key)
            {
                case "Backspace":
                    _inputSimulator.Keyboard.KeyPress(VirtualKeyCode.BACK);
                    break;
                case "%":
                    _inputSimulator.Keyboard.KeyPress(VirtualKeyCode.SHIFT, VirtualKeyCode.VK_5);
                    break;
                case "00":
                    _inputSimulator.Keyboard.KeyPress(VirtualKeyCode.VK_0);
                    _inputSimulator.Keyboard.KeyPress(VirtualKeyCode.VK_0);
                    break;
                case ".":
                    _inputSimulator.Keyboard.KeyPress(VirtualKeyCode.DECIMAL);
                    break;
                case "Clear":
                    barcodeTextBox.Visibility = Visibility.Visible;
                    barcodeTextBox.Focus();

                    txtQuantity.Text = "";

                    break;
                default:
                    if (int.TryParse(key, out var number))
                    {
                        var keyCode = (VirtualKeyCode)Enum.Parse(
                            typeof(VirtualKeyCode),
                            $"VK_{number}"
                        );
                        _inputSimulator.Keyboard.KeyPress(keyCode);
                    }
                    break;
            }

        }

        /// <summary>
        /// Process a card refund through the payment terminal
        /// </summary>
        /// <param name="amount">The amount to refund</param>
        /// <returns>True if the refund was successful, false otherwise</returns>
        private async Task<bool> ProcessCardRefund(decimal amount, string printerName)
        {
            bool isSuccess = false;
            var tillInfo = new TillInformation();
            var transInfo = new TransactionInfo();
            var transaction = new TransactionHook();

            // Set merchant details (matches your existing receipts)
            tillInfo.MerchantName = "Sage Pay Ireland";
            tillInfo.TillNumber = "POS1";
            tillInfo.Address1 = "50-51 Patrick Street";
            tillInfo.Address2 = "Dun Laoghaire";
            tillInfo.Address3 = "Co Dublin";
            tillInfo.PhoneNumber = "01-2311777";

            // Process refund (amount in cents)
            if (transaction.Process(
                TransactionHook.TRANSACTIONHOOK_TRANSACTIONTYPE.INT_TT_REFUND,
                (int)(amount * 100),
                ref tillInfo,
                ref transInfo))
            {
                // Refund succeeded - print receipt
                RefundRecipt.PrintRefundReceipt(transInfo, tillInfo, printerName);
                isSuccess = true;

                Console.WriteLine($"Refund successful. Auth Code: {transInfo.AuthorisationCode}");
            }
            else
            {
                Console.WriteLine($"Refund failed. Response Code: {transInfo.ResponseCode}");
            }

            return isSuccess;
        }

    }
}
