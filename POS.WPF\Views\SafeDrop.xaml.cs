﻿using Microsoft.EntityFrameworkCore.Metadata.Internal;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Windows;
using System.Windows.Controls;
using System.Windows.Data;
using System.Windows.Documents;
using System.Windows.Input;
using System.Windows.Media;
using System.Windows.Media.Imaging;
using System.Windows.Shapes;
using WindowsInput;
using WindowsInput.Native;

namespace POS.WPF.Views
{
    /// <summary>
    /// Interaction logic for SafeDrop.xaml
    /// </summary>
    public partial class SafeDrop : Window
    {
        private InputSimulator _inputSimulator;

        public static decimal? Amount { get; set; }

        public static bool Cancelled { get; set; }

        public SafeDrop()
        {
            InitializeComponent();
            Amount = null;
            _inputSimulator = new InputSimulator();
            Helpers.WindowStyleHelper.SetWindowStyle(this);

        }

        private void Button_Click(object sender, RoutedEventArgs e)
        {
            //get user confirmation
            var dialog = MessageBox.Show("Confirmed?", "Alart", MessageBoxButton.YesNo, MessageBoxImage.Question);
            if (dialog == MessageBoxResult.No)
            {
                Cancelled = true;
                return;
            }   

            Amount = decimal.Parse(txtAmount.Text);
            Cancelled = false;
            this.Close();
        }

        private void NumericKeypad_KeyPressed(object sender, string key)
        {
            txtAmount.Focus();

            switch (key)
            {
                case "Backspace":
                    _inputSimulator.Keyboard.KeyPress(VirtualKeyCode.BACK);
                    break;
                case "%":
                    _inputSimulator.Keyboard.KeyPress(VirtualKeyCode.SHIFT, VirtualKeyCode.VK_5);
                    break;
                case "00":
                    _inputSimulator.Keyboard.KeyPress(VirtualKeyCode.VK_0);
                    _inputSimulator.Keyboard.KeyPress(VirtualKeyCode.VK_0);
                    break;
                case ".":
                    _inputSimulator.Keyboard.KeyPress(VirtualKeyCode.DECIMAL);
                    break;
                default:
                    if (int.TryParse(key, out var number))
                    {
                        var keyCode = (VirtualKeyCode)Enum.Parse(
                            typeof(VirtualKeyCode),
                            $"VK_{number}"
                        );
                        _inputSimulator.Keyboard.KeyPress(keyCode);
                    }
                    break;
            }
        }

        //cancel
        private void Button_Click_1(object sender, RoutedEventArgs e)
        {
            Cancelled = true;
            this.Close();
        }
    }
}
