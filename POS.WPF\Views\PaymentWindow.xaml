﻿<Window x:Class="POS.WPF.Views.PaymentWindow"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        xmlns:local="clr-namespace:POS.WPF.Views"
        Title="Payment" Height="500" Width="800">
    <Grid Background="#004e7c">
        <Grid.ColumnDefinitions>
            <ColumnDefinition Width="2*"/>
            <ColumnDefinition Width="3*"/>
        </Grid.ColumnDefinitions>

        <!-- Left side: Payment Method Selection -->
        <ListView Name="PaymentMethodsListView" Margin="10" FontSize="24"
                  Background="{StaticResource DataGridBackground}"
                  Grid.Column="0">
            <ListView.View>
                <GridView>
                    <GridViewColumn Header="ID" DisplayMemberBinding="{Binding Id}" Width="80">
                        <GridViewColumn.HeaderContainerStyle>
                            <Style TargetType="GridViewColumnHeader">
                                <Setter Property="Background" Value="Green"/>
                                <Setter Property="Foreground" Value="White"/>
                                <Setter Property="FontSize" Value="16"/>
                            </Style>
                        </GridViewColumn.HeaderContainerStyle>
                    </GridViewColumn>
                    <GridViewColumn Header="Method" DisplayMemberBinding="{Binding Method}" Width="250">
                        <GridViewColumn.HeaderContainerStyle>
                            <Style TargetType="GridViewColumnHeader">
                                <Setter Property="Background" Value="Green"/>
                                <Setter Property="Foreground" Value="White"/>
                                <Setter Property="FontSize" Value="16"/>
                            </Style>
                        </GridViewColumn.HeaderContainerStyle>
                    </GridViewColumn>
                </GridView>
            </ListView.View>
            <ListView.ItemContainerStyle>
                <Style TargetType="ListViewItem">
                    <Setter Property="Background" Value="#0a688d"/>
                    <Setter Property="Foreground" Value="White"/>
                    <Setter Property="Margin" Value="0,2"/>
                </Style>
            </ListView.ItemContainerStyle>
        </ListView>

        <!-- Right side: Paid Amount Input & Keypad -->
        <StackPanel Grid.Column="1" Margin="10">
            <TextBlock Text="Enter Paid Cash Amount:" FontSize="24" Foreground="White" Margin="0,0,0,10"/>
            <TextBox x:Name="txtPaidAmount" 
                     FontSize="24" 
                     Height="50" 
                     Background="#0a688d" 
                     Foreground="White" 
                     Margin="10"
                     TextChanged="txtPaidAmount_TextChanged"/>

            <!-- Amount Buttons -->
            <WrapPanel HorizontalAlignment="Left" Margin="10">
                <Button Content="Exact Amount" FontSize="24" Margin="5" Padding="20,10" Background="#004e7c" Foreground="White" Click="ExactAmountButton_Click"/>
                <Button Content="£5" FontSize="24" Margin="5" Padding="20,10" Background="#004e7c" Foreground="White" Click="PresetAmountButton_Click"/>
                <Button Content="£10" FontSize="24" Margin="5" Padding="20,10" Background="#004e7c" Foreground="White" Click="PresetAmountButton_Click"/>
                <Button Content="£50" FontSize="24" Margin="5" Padding="20,10" Background="#004e7c" Foreground="White" Click="PresetAmountButton_Click"/>
                <Button Content="£100" FontSize="24" Margin="5" Padding="20,10" Background="#004e7c" Foreground="White" Click="PresetAmountButton_Click"/>
            </WrapPanel>

            <!-- Optional: You can add a numeric keypad user control if you already have one -->
            <local:NumericKeypad KeyPressed="NumericKeypad_KeyPressed" Margin="5"/>

            <!-- Action Buttons -->
            <WrapPanel HorizontalAlignment="Right" Margin="10">
                <Button Content="OK" 
                        Margin="10" 
                        Padding="20,10" 
                        FontSize="24"
                        Background="#004e7c" Foreground="White" 
                        Click="OkButton_Click"/>
                <Button Content="Cancel" 
                        Margin="10" 
                        Padding="20,10" 
                        FontSize="24"
                        Background="#004e7c" Foreground="White" 
                        Click="CancelButton_Click"/>
            </WrapPanel>
        </StackPanel>
    </Grid>
</Window>

