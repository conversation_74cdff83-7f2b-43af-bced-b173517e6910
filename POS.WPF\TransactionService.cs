﻿using POS.Core.Models;
using POS.WPF.Helpers;
using POS.WPF.ViewModels;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Text.RegularExpressions;
using System.Threading.Tasks;

namespace POS.WPF
{
    public class TransactionService
    {
        private readonly Data _context;
        private readonly MainViewModel _viewModel;

        public TransactionService(Data context, MainViewModel viewModel)
        {
            _context = context;
            _viewModel = viewModel;
        }

        public void ProcessBarcode(string barcode)
        {
            barcode = Regex.Replace(barcode, @"\D", "");
            var coreProduct = _context.Products.FirstOrDefault(p => p.Barcode == barcode);
            if (coreProduct == null) throw new InvalidOperationException("Product not found");

            var store = _context.Stores.First(s => s.StoreId == UiShift.StoreId);
            if (store.BrandId != coreProduct.BrandId)
                throw new InvalidOperationException("Invalid Product for the Store!");

            AddProductToTransaction(coreProduct, store);
        }

        private void AddProductToTransaction(Core.Models.Product coreProduct, Store store)
        {
            var productStore = _context.ProductStores
                .First(p => p.ProductId == coreProduct.Id && p.StoreId == store.StoreId);
            var uiProduct = _viewModel.Products.FirstOrDefault(p => p.PLU == coreProduct.PLU);

            var product = _context.Products.FirstOrDefault(p => p.PLU == coreProduct.PLU);

            if (uiProduct != null)
            {
                uiProduct.Quantity++;
            }
            else
            {
                var newProduct = new POS.WPF.Product
                {
                    ProductName = coreProduct.Name,
                    PLU = coreProduct.PLU,
                    UnitPrice = productStore.StoreSpecificPrice ?? product.SellingPrice,
                    Quantity = 1
                };
                _viewModel.Products.Add(newProduct);
            }
        }
    }
}
