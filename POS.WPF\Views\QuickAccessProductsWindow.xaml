<Window x:Class="POS.WPF.Views.QuickAccessProductsWindow"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
        xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
        xmlns:local="clr-namespace:POS.WPF.Views"
        mc:Ignorable="d"
        Title="Quick Access Products" Height="600" Width="800"
        WindowStartupLocation="CenterScreen">
    <Grid Background="#0a688d">
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
            <RowDefinition Height="Auto"/>
        </Grid.RowDefinitions>
        
        <TextBlock Grid.Row="0" Text="Quick Access Products" 
                   Foreground="White" FontSize="24" FontWeight="Bold" 
                   HorizontalAlignment="Center" Margin="0,15,0,15"/>
        
        <ScrollViewer Grid.Row="1" VerticalScrollBarVisibility="Auto">
            <WrapPanel x:Name="productsPanel" Margin="20"/>
        </ScrollViewer>
        
        <Button Grid.Row="2" Content="Close" 
                Background="#004e7c" Foreground="White" FontSize="16"
                Margin="20" Padding="15,5" HorizontalAlignment="Center"
                Click="CloseButton_Click"/>
    </Grid>
</Window>