//EnumExtensions.cs
-------------------
using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Linq;
using System.Reflection;
using System.Text;
using System.Threading.Tasks;

namespace POS.Printer
{
    public static class EnumExtensions
    {
        public static string GetDescription(this Enum value)
        {
            FieldInfo field = value.GetType().GetField(value.ToString());
            DescriptionAttribute attribute = field.GetCustomAttribute<DescriptionAttribute>();

            return attribute != null ? attribute.Description : value.ToString();
        }
    }
}


//Helper.cs
-----------
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace POS.Printer
{
    internal class Helper
    {
        public static string CreateMixedAlignmentLine(string leftText, string rightText, int totalWidth)
        {
            // Calculate the number of spaces to add between the left and right text
            int spaceCount = totalWidth - (leftText.Length + rightText.Length);
            if (spaceCount < 0) spaceCount = 1; // Ensure at least one space

            string spaces = new string(' ', spaceCount);
            return $"{leftText}{spaces}{rightText}";
        }


        public static string CreateFourColumnLine(string col1, string col2, string col3, string col4, int totalWidth)
        {
            // Define column widths (adjust as needed)
            int col1Width = 12;
            int col2Width = 12;
            int col3Width = 12;
            int col4Width = totalWidth - (col1Width + col2Width + col3Width);

            // Format each column
            col1 = col1.PadRight(col1Width); // Left-aligned
            col2 = col2.PadRight(col2Width); // Left-aligned
            col3 = col3.PadLeft(col3Width);  // Right-aligned
            col4 = col4.PadLeft(col4Width);  // Right-aligned

            // Combine columns
            return col1 + col2 + col3 + col4;
        }
    }
}


//Printer.cs
------------
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using ESCPOS_NET.Printers;
using ESCPOS_NET.Emitters;
using System.Threading.Tasks;
using Microsoft.VisualBasic;
using System.Dynamic;
using System.Reflection.Metadata;
using System.ComponentModel;

//
namespace POS.Printer
{

    public enum ProductPriceReductionType
    {
        
        None,
        [Description("Staff Discount")]
        StaffDiscount,
        [Description("Price Override")]
        PriceOverride
    }

    public record struct VatEntry(string VatCode,string VatRate, decimal ExVat, decimal Vat, decimal IncVat);
    public record struct Product(string ProductDescription, decimal Price, ProductPriceReductionType PriceReduction, decimal reductionAmount);

    public class PrintData
    {
        public string StoreId { get; set; }

        public string TransactionId { get; set; }

        public string CashierName { get; set; }

        public string Time { get; set; }

        public string Total { get; set; }

        public string Barcode { get; set; }

        public string PosMachineId { get; set; }


        public string VatNo { get; set; }

        public PaymentType PaymentType { get; set; }

        public string StoreName { get; set; }


        public List<VatEntry> VatEntries { get; set; }

        //for cash payements
        public string PaidAmount { get; set; }

        public string ChangeDue { get; set; }


        public List<Product> Products { get; set; }

        // Constructor to enforce PaymentType initialization
        public PrintData(PaymentType paymentType)
        {
            PaymentType = paymentType;
        }
    }


    public enum PaymentType
    {
        Cash = 1,
        Card = 2,
        Voucher = 3
    }


    public class Printer
    {
        public static void Print(PrintData obj,string printerName)
        {
            // Create an EPSON emitter
            var emitter = new EPSON();

            // Build commands
            var commands = new List<byte>();
            // Center and print the store address
            commands.AddRange(emitter.CenterAlign());
            commands.AddRange(emitter.PrintLine("Tudor Lodge Service Stn"));
            commands.AddRange(emitter.PrintLine("Marton Road"));
            commands.AddRange(emitter.PrintLine("Middlesbrough"));
            commands.AddRange(emitter.PrintLine("TS4 3SE"));
            commands.AddRange(emitter.PrintLine("TEL: 01642 323771"));
            commands.AddRange(emitter.PrintLine("")); // Add a blank line

            // Add "Duplicate" line with decoration
            commands.AddRange(emitter.PrintLine("==--==--==-- Duplicate --==--==--=="));

            commands.AddRange(emitter.PrintLine("")); // Blank line for spacing

            // Mixed alignment line 1
            string leftText1 = $"Store {obj.StoreId} POS {obj.PosMachineId}";
            string rightText1 = $"Trans {obj.TransactionId}";
            commands.AddRange(emitter.PrintLine(Helper.CreateMixedAlignmentLine(leftText1, rightText1, 40))); // 48 is the typical character width

            // Mixed alignment line 2
            string leftText2 = "Op Name";
            string rightText2 = obj.CashierName;
            commands.AddRange(emitter.PrintLine(Helper.CreateMixedAlignmentLine(leftText2, rightText2, 40))); // 48 is the typical character width

            commands.AddRange(emitter.PrintLine("")); // Add a blank line
            commands.AddRange(emitter.PrintLine("SALE"));




            foreach (var item in obj.Products)
            {
                commands.AddRange(emitter.PrintLine(Helper.CreateMixedAlignmentLine(item.ProductDescription, item.Price.ToString(), 40))); // 48 is the typical character width
                if(item.PriceReduction == ProductPriceReductionType.StaffDiscount || item.PriceReduction == ProductPriceReductionType.PriceOverride)
                {
                    commands.AddRange(emitter.PrintLine(Helper.CreateMixedAlignmentLine($"   {item.PriceReduction.GetDescription()}", $"-{item.Price.ToString()}", 40)));
                }
                commands.AddRange(emitter.PrintLine("----------------------------------------"));
            }
            commands.AddRange(emitter.PrintLine(Helper.CreateMixedAlignmentLine("Total", obj.Total, 40))); // 48 is the typical character width

            commands.AddRange(emitter.PrintLine("----------------------------------------"));
            commands.AddRange(emitter.PrintLine(Helper.CreateMixedAlignmentLine("Paid GBP", obj.PaidAmount, 40)));
            commands.AddRange(emitter.PrintLine(Helper.CreateMixedAlignmentLine("Change Due", obj.ChangeDue, 40)));
            commands.AddRange(emitter.PrintLine("----------------------------------------"));

            commands.AddRange(emitter.PrintLine(""));
            commands.AddRange(emitter.PrintLine(Helper.CreateMixedAlignmentLine($"Shop - {obj.StoreName}","", 40)));
            commands.AddRange(emitter.PrintLine(Helper.CreateMixedAlignmentLine("Vat No.", obj.VatNo, 40)));

            commands.AddRange(emitter.PrintLine(Helper.CreateFourColumnLine("VAT Rate", "Ex.VAT", "VAT", "Inc.VAT", 40)));
            if(obj.VatEntries.Any(v => v.VatCode == "Code0"))
            commands.AddRange(emitter.PrintLine(Helper.CreateFourColumnLine("20%", obj.VatEntries.First(v => v.VatCode == "Code0").ExVat.ToString(), obj.VatEntries.First(v => v.VatCode == "Code0").Vat.ToString(), obj.VatEntries.First(v => v.VatCode == "Code0").IncVat.ToString(), 40)));
            if(obj.VatEntries.Any(v => v.VatCode == "Code1"))
            commands.AddRange(emitter.PrintLine(Helper.CreateFourColumnLine("5%", obj.VatEntries.First(v => v.VatCode == "Code1").ExVat.ToString(), obj.VatEntries.First(v => v.VatCode == "Code1").Vat.ToString(), obj.VatEntries.First(v => v.VatCode == "Code1").IncVat.ToString(), 40)));
            if (obj.VatEntries.Any(v => v.VatCode == "Code2"))
            commands.AddRange(emitter.PrintLine(Helper.CreateFourColumnLine("0%", obj.VatEntries.First(v => v.VatCode == "Code2").ExVat.ToString(), obj.VatEntries.First(v => v.VatCode == "Code2").Vat.ToString(), obj.VatEntries.First(v => v.VatCode == "Code2").IncVat.ToString(), 40)));
            if (obj.VatEntries.Any(v => v.VatCode == "Exempt"))
            commands.AddRange(emitter.PrintLine(Helper.CreateFourColumnLine("0% (Exempt)", obj.VatEntries.First(v => v.VatCode == "Exempt").ExVat.ToString(), obj.VatEntries.First(v => v.VatCode == "Exempt").Vat.ToString(), obj.VatEntries.First(v => v.VatCode == "Exempt").IncVat.ToString(), 40)));

            commands.AddRange(emitter.PrintLine(""));




            // GS (0x1D) command to set barcode height
            commands.Add(0x1D); // GS
            commands.Add(0x68); // h
            commands.Add(100);  // Set height to 100 dots (adjust as needed)

            // GS (0x1D) command to set barcode width
            commands.Add(0x1D); // GS
            commands.Add(0x77); // w
            commands.Add(2);    // Width of the barcode (1 to 6, 2 is default)

            // GS (0x1D) command to print barcode
            commands.Add(0x1D); // GS
            commands.Add(0x6B); // k
            commands.Add(73);   // Barcode type: 73 = Code 128
            commands.Add((byte)obj.Barcode.Length); // Length of the data
            commands.AddRange(System.Text.Encoding.ASCII.GetBytes(obj.Barcode));

            // Add a line feed to separate the barcode from the numeric value
            commands.Add(0x0A); // LF (Line Feed)

            // Print the numeric value below the barcode
            commands.AddRange(System.Text.Encoding.ASCII.GetBytes(obj.Barcode));
            commands.Add(0x0A); // LF (Line Feed)

            commands.AddRange(emitter.PrintLine("")); // Add a blank line

            commands.AddRange(emitter.PrintLine("Thank you for shopping with us."));
            commands.AddRange(emitter.PrintLine("Please Come Again"));
            commands.AddRange(emitter.PrintLine("")); // Add a blank line

            // Add the cut command
            commands.Add(0x1D); // Group separator
            commands.Add(0x56); // Select cut mode
            commands.Add(0x01); // Partial cut (use 0x00 for full cut)

            // Get the raw byte array
            byte[] rawCommands = commands.ToArray();

            // Send commands to a Windows Printer
            bool isPrinted = RawPrinterHelper.SendBytesToPrinter(printerName, rawCommands);

            if (isPrinted)
            {
                Console.WriteLine("Print job sent to the printer successfully.");
            }
            else
            {
                Console.WriteLine("Failed to send print job to the printer.");
            }



        }

       


    }



}


//RawPrinterHelper.cs
---------------------
using System;
using System.Collections.Generic;
using System.Linq;
using System.Runtime.InteropServices;
using System.Text;
using System.Threading.Tasks;

namespace POS.Printer
{
    internal class RawPrinterHelper
    {
        // Structure and API declarations:
        [StructLayout(LayoutKind.Sequential, CharSet = CharSet.Unicode)]
        public class DOCINFOA
        {
            [MarshalAs(UnmanagedType.LPWStr)]
            public string pDocName;

            [MarshalAs(UnmanagedType.LPWStr)]
            public string pOutputFile;

            [MarshalAs(UnmanagedType.LPWStr)]
            public string pDataType;
        }

        [DllImport("winspool.drv", SetLastError = true, CharSet = CharSet.Unicode)]
        private static extern bool OpenPrinter(string printerName, out IntPtr printerHandle, IntPtr printerDefaults);

        [DllImport("winspool.drv", SetLastError = true, CharSet = CharSet.Unicode)]
        private static extern bool ClosePrinter(IntPtr printerHandle);

        [DllImport("winspool.drv", SetLastError = true, CharSet = CharSet.Unicode)]
        private static extern bool StartDocPrinter(IntPtr printerHandle, int level, [In] DOCINFOA docInfo);

        [DllImport("winspool.drv", SetLastError = true, CharSet = CharSet.Unicode)]
        private static extern bool EndDocPrinter(IntPtr printerHandle);

        [DllImport("winspool.drv", SetLastError = true, CharSet = CharSet.Unicode)]
        private static extern bool StartPagePrinter(IntPtr printerHandle);

        [DllImport("winspool.drv", SetLastError = true, CharSet = CharSet.Unicode)]
        private static extern bool EndPagePrinter(IntPtr printerHandle);

        [DllImport("winspool.drv", SetLastError = true, CharSet = CharSet.Unicode)]
        private static extern bool WritePrinter(IntPtr printerHandle, IntPtr buffer, int bufferLength, out int bytesWritten);

        /// <summary>
        /// Sends raw data to a printer.
        /// </summary>
        /// <param name="printerName">Name of the printer.</param>
        /// <param name="data">Byte array of raw data to send to the printer.</param>
        /// <returns>True if the data was successfully sent; otherwise, false.</returns>
        public static bool SendBytesToPrinter(string printerName, byte[] data)
        {
            IntPtr printerHandle = IntPtr.Zero;
            IntPtr unmanagedData = IntPtr.Zero;

            try
            {
                // Open the printer.
                if (!OpenPrinter(printerName, out printerHandle, IntPtr.Zero))
                {
                    return false;
                }

                // Prepare the document info.
                var docInfo = new DOCINFOA
                {
                    pDocName = "Raw Print Job",
                    pDataType = "RAW"
                };

                // Start a document.
                if (!StartDocPrinter(printerHandle, 1, docInfo))
                {
                    return false;
                }

                // Start a page.
                if (!StartPagePrinter(printerHandle))
                {
                    return false;
                }

                // Allocate unmanaged memory for the data and copy the data to it.
                unmanagedData = Marshal.AllocHGlobal(data.Length);
                Marshal.Copy(data, 0, unmanagedData, data.Length);

                // Write the data to the printer.
                if (!WritePrinter(printerHandle, unmanagedData, data.Length, out int bytesWritten))
                {
                    return false;
                }

                // End the page and the document.
                EndPagePrinter(printerHandle);
                EndDocPrinter(printerHandle);

                return true;
            }
            finally
            {
                // Free unmanaged memory and close the printer handle.
                if (unmanagedData != IntPtr.Zero)
                {
                    Marshal.FreeHGlobal(unmanagedData);
                }

                if (printerHandle != IntPtr.Zero)
                {
                    ClosePrinter(printerHandle);
                }
            }
        }
    }
}


//SafeDropPrinter.cs
--------------------
using ESCPOS_NET.Emitters;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace POS.Printer
{
    public class SafeDropPrinter
    {
        public static void Print(string storeId,string posMachineId
            ,string time, string amount, string cashierName,string safeDropId, string printerName)
        {

            // Create an EPSON emitter
            var emitter = new EPSON();

            // Build commands
            var commands = new List<byte>();

            // Mixed alignment line 1
            string leftText1 = $"Store {storeId} POS {posMachineId}";
            string rightText1 = "";
            commands.AddRange(emitter.PrintLine(Helper.CreateMixedAlignmentLine(leftText1, rightText1, 40))); // 48 is the typical character width

            // Mixed alignment line 2
            string leftText2 = "Op Name";
            string rightText2 = cashierName;
            commands.AddRange(emitter.PrintLine(Helper.CreateMixedAlignmentLine(leftText2, rightText2, 40))); // 48 is the typical character width

            commands.AddRange(emitter.PrintLine("")); // Add a blank line
            commands.AddRange(emitter.PrintLine("SAFE DROP"));
            commands.AddRange(emitter.PrintLine(Helper.CreateMixedAlignmentLine("Id", safeDropId, 40)));
            commands.AddRange(emitter.PrintLine(Helper.CreateMixedAlignmentLine("Amount", amount, 40)));
            commands.AddRange(emitter.PrintLine(Helper.CreateMixedAlignmentLine("Time", time, 40)));

            // Add the cut command
            commands.Add(0x1D); // Group separator
            commands.Add(0x56); // Select cut mode
            commands.Add(0x01); // Partial cut (use 0x00 for full cut)

            // Get the raw byte array
            byte[] rawCommands = commands.ToArray();

            // Send commands to a Windows Printer
            bool isPrinted = RawPrinterHelper.SendBytesToPrinter(printerName, rawCommands);

            if (isPrinted)
            {
                Console.WriteLine("Print job sent to the printer successfully.");
            }
            else
            {
                Console.WriteLine("Failed to send print job to the printer.");
            }

        }
    }
}


//Interfaces/IPrinter.cs
------------------------
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace POS.Printer.Interfaces
{
    public interface IPrinter
    {
        void PrintBill(string header, List<(string description, int quantity, decimal price)> items, string footer);
    }

}


//obj/Debug/net8.0/.NETCoreApp,Version=v8.0.AssemblyAttributes.cs
-----------------------------------------------------------------
// <autogenerated />
using System;
using System.Reflection;
[assembly: global::System.Runtime.Versioning.TargetFrameworkAttribute(".NETCoreApp,Version=v8.0", FrameworkDisplayName = ".NET 8.0")]


//obj/Debug/net8.0/POS.Printer.AssemblyInfo.cs
----------------------------------------------
//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated by a tool.
//     Runtime Version:4.0.30319.42000
//
//     Changes to this file may cause incorrect behavior and will be lost if
//     the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

using System;
using System.Reflection;

[assembly: System.Reflection.AssemblyCompanyAttribute("POS.Printer")]
[assembly: System.Reflection.AssemblyConfigurationAttribute("Debug")]
[assembly: System.Reflection.AssemblyFileVersionAttribute("1.0.0.0")]
[assembly: System.Reflection.AssemblyInformationalVersionAttribute("1.0.0+2a3f3ba78aaff5920b326ab3fe8f798394127ef4")]
[assembly: System.Reflection.AssemblyProductAttribute("POS.Printer")]
[assembly: System.Reflection.AssemblyTitleAttribute("POS.Printer")]
[assembly: System.Reflection.AssemblyVersionAttribute("1.0.0.0")]

// Generated by the MSBuild WriteCodeFragment class.



//obj/Debug/net8.0/POS.Printer.GlobalUsings.g.cs
------------------------------------------------
// <auto-generated/>
global using global::System;
global using global::System.Collections.Generic;
global using global::System.IO;
global using global::System.Linq;
global using global::System.Net.Http;
global using global::System.Threading;
global using global::System.Threading.Tasks;


//obj/Release/net8.0/.NETCoreApp,Version=v8.0.AssemblyAttributes.cs
-------------------------------------------------------------------
// <autogenerated />
using System;
using System.Reflection;
[assembly: global::System.Runtime.Versioning.TargetFrameworkAttribute(".NETCoreApp,Version=v8.0", FrameworkDisplayName = ".NET 8.0")]


//obj/Release/net8.0/POS.Printer.AssemblyInfo.cs
------------------------------------------------
//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated by a tool.
//     Runtime Version:4.0.30319.42000
//
//     Changes to this file may cause incorrect behavior and will be lost if
//     the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

using System;
using System.Reflection;

[assembly: System.Reflection.AssemblyCompanyAttribute("POS.Printer")]
[assembly: System.Reflection.AssemblyConfigurationAttribute("Release")]
[assembly: System.Reflection.AssemblyFileVersionAttribute("1.0.0.0")]
[assembly: System.Reflection.AssemblyInformationalVersionAttribute("1.0.0+3eab0c8ac7fb19007c5906847cec3338d1e57517")]
[assembly: System.Reflection.AssemblyProductAttribute("POS.Printer")]
[assembly: System.Reflection.AssemblyTitleAttribute("POS.Printer")]
[assembly: System.Reflection.AssemblyVersionAttribute("1.0.0.0")]

// Generated by the MSBuild WriteCodeFragment class.



//obj/Release/net8.0/POS.Printer.GlobalUsings.g.cs
--------------------------------------------------
// <auto-generated/>
global using global::System;
global using global::System.Collections.Generic;
global using global::System.IO;
global using global::System.Linq;
global using global::System.Net.Http;
global using global::System.Threading;
global using global::System.Threading.Tasks;


