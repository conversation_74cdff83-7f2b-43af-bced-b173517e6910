﻿using POS.Core.Models;
using System;
using System.Collections.Generic;
using System.Globalization;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Windows;
using System.Windows.Controls;
using System.Windows.Data;
using System.Windows.Documents;
using System.Windows.Input;
using System.Windows.Media;
using System.Windows.Media.Imaging;
using System.Windows.Shapes;

namespace POS.WPF.Views
{
    /// <summary>
    /// Interaction logic for OnHoldList.xaml
    /// </summary>
    public partial class OnHoldList : Window
    {

        public static Sale? SelectedSale { get; set; }

        public OnHoldList(List<Sale>? sales)
        {
            InitializeComponent();
            Helpers.WindowStyleHelper.SetWindowStyle(this);
            SelectedSale = null;

            foreach (var sale in sales)
            {
                // Create a new Button for each Sale
                var button = new Button
                {
                    Content = $"Time: {sale.Date}, Total: {sale.TotalValue.ToString("C", new CultureInfo("en-GB"))}",
                    Tag = sale, // Store the Sale object in the Tag property
                    Margin = new Thickness(0, 5, 0, 5)
                };

                // Attach Click event handler
                button.Click += SaleButton_Click;

                //TODO :SalesStackPanel
                // Add the Button to the StackPanel
                SalesStackPanel.Children.Add(button);
            }
        }

        private void SaleButton_Click(object sender, RoutedEventArgs e)
        {
            //confimbox
            var dialog = MessageBox.Show("Confirmed?", "Alart", MessageBoxButton.YesNo, MessageBoxImage.Question);
            if (dialog == MessageBoxResult.Yes)
            {
                if (sender is Button button && button.Tag is Sale sale)
                {
                    SelectedSale = sale; // Retain the clicked Sale

                }
                this.Close();
            }
           
        }

        private void CancelButton_Click(object sender, RoutedEventArgs e)
        {
            SelectedSale = null;
            this.Close();
        }
    }
}