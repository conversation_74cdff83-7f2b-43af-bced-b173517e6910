﻿using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using POS.Core.Models;

namespace BackOffice.Controllers
{
    [Authorize(Roles = "BackOffice,Admin")]
    public class BrandController : Controller
    {
        private readonly Data _context;
        private readonly IWebHostEnvironment _webHostEnvironment;

        public BrandController(Data context, IWebHostEnvironment webHostEnvironment)
        {
            _context = context;
            _webHostEnvironment = webHostEnvironment;
        }

        // GET: Brand
        public async Task<IActionResult> Index()
        {
            return View(await _context.Brands.ToListAsync());
        }

        // GET: Brand/Details/5
        public async Task<IActionResult> Details(int? id)
        {
            if (id == null) return NotFound();

            var brand = await _context.Brands
                .Include(b => b.Products)
                .Include(b => b.Stores)
                .FirstOrDefaultAsync(m => m.Id == id);

            if (brand == null) return NotFound();

            return View(brand);
        }

        // GET: Brand/Create
        public IActionResult Create()
        {
            return View();
        }

       

        // GET: Brand/Edit/5
        public async Task<IActionResult> Edit(int? id)
        {
            if (id == null) return NotFound();

            var brand = await _context.Brands.FindAsync(id);
            if (brand == null) return NotFound();

            return View(brand);
        }

        private IActionResult RedirectToUserHome()
        {
            if (User.IsInRole("BackOffice"))
            {
                return RedirectToAction("Index", "BackOffice");
            }
            return RedirectToAction("Index", "Home");
        }

        // POST: Brand/Create
        [HttpPost]
        [ValidateAntiForgeryToken]
        public async Task<IActionResult> Create([Bind("Id,Name")] Brand brand, IFormFile logoFile)
        {

            ModelState.Remove(nameof(Brand.BrandLogo));
            ModelState.Remove(nameof(Brand.Products));
            ModelState.Remove(nameof(Brand.Stores));


            if (ModelState.IsValid)
            {
                if (logoFile != null && logoFile.ContentType == "image/png")
                {
                    string fileName = Path.GetFileNameWithoutExtension(logoFile.FileName) + "_" + Guid.NewGuid() + ".png";
                    string path = Path.Combine(_webHostEnvironment.WebRootPath, "Images\\BrandLogos", fileName);

                    using (var stream = new FileStream(path, FileMode.Create))
                    {
                        await logoFile.CopyToAsync(stream);
                    }

                    brand.BrandLogo = fileName;
                }

                _context.Add(brand);
                await _context.SaveChangesAsync();
                return RedirectToUserHome();
            }

            return View(brand);
        }

        // POST: Brand/Edit/5
        [HttpPost]
        [ValidateAntiForgeryToken]
        public async Task<IActionResult> Edit(int id, [Bind("Id,Name,BrandLogo")] Brand brand, IFormFile logoFile)
        {
            if (id != brand.Id) return NotFound();

            ModelState.Remove(nameof(Brand.BrandLogo));
            ModelState.Remove(nameof(Brand.Products));
            ModelState.Remove(nameof(Brand.Stores));

            if (ModelState.IsValid)
            {
                try
                {
                    if (logoFile != null && logoFile.ContentType == "image/png")
                    {
                        string fileName = Path.GetFileNameWithoutExtension(logoFile.FileName) + "_" + Guid.NewGuid() + ".png";
                        string path = Path.Combine(_webHostEnvironment.WebRootPath, "Images\\BrandLogos", fileName);

                        using (var stream = new FileStream(path, FileMode.Create))
                        {
                            await logoFile.CopyToAsync(stream);
                        }

                        // Delete the old logo file if it exists
                        if (!string.IsNullOrEmpty(brand.BrandLogo))
                        {
                            string oldPath = Path.Combine(_webHostEnvironment.WebRootPath, "Images\\BrandLogos", brand.BrandLogo);
                            if (System.IO.File.Exists(oldPath)) System.IO.File.Delete(oldPath);
                        }

                        brand.BrandLogo = fileName;
                    }

                    _context.Update(brand);
                    await _context.SaveChangesAsync();
                }
                catch (DbUpdateConcurrencyException)
                {
                    if (!BrandExists(brand.Id)) return NotFound();
                    throw;
                }

                return RedirectToUserHome();
            }

            return View(brand);
        }

        // GET: Brand/Delete/5
        public async Task<IActionResult> Delete(int? id)
        {
            if (id == null) return NotFound();

            var brand = await _context.Brands.FirstOrDefaultAsync(m => m.Id == id);
            if (brand == null) return NotFound();

            return View(brand);
        }

        // POST: Brand/Delete/5
        [HttpPost, ActionName("Delete")]
        [ValidateAntiForgeryToken]
        public async Task<IActionResult> DeleteConfirmed(int id)
        {
            var brand = await _context.Brands.FindAsync(id);
            _context.Brands.Remove(brand);
            await _context.SaveChangesAsync();
            return RedirectToAction(nameof(Index));
        }

        private bool BrandExists(int id)
        {
            return _context.Brands.Any(e => e.Id == id);
        }
    }
}
