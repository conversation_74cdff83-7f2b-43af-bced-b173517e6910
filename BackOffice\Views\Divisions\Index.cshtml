﻿@model IEnumerable<POS.Core.Models.Division>

@{
    ViewData["Title"] = "Divisions";
}

<h2>Divisions</h2>
<a asp-action="Create" class="btn btn-primary">Create New</a>

<table class="table table-bordered mt-3">
    <thead>
        <tr>
            <th>Name</th>
            <th>SubCategory</th>
            <th>Actions</th>
        </tr>
    </thead>
    <tbody>
        @foreach (var item in Model)
        {
            <tr>
                <td>@item.Name</td>
                <td>@(item.SubCatagory?.Name ?? "N/A")</td>
                <td>
                    <a asp-action="Edit" asp-route-id="@item.Id" class="btn btn-warning btn-sm">Edit</a>
                    <a asp-action="Delete" asp-route-id="@item.Id" class="btn btn-danger btn-sm">Delete</a>
                </td>
            </tr>
        }
    </tbody>
</table>
