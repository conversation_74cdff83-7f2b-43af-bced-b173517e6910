using Microsoft.AspNetCore.Http;
using Microsoft.EntityFrameworkCore;
using POS.Core.Models;
using System.Security.Claims;
using System.Threading.Tasks;

namespace BackOffice.Middleware
{
    public class IpRestrictionMiddleware
    {
        private readonly RequestDelegate _next;

        public IpRestrictionMiddleware(RequestDelegate next)
        {
            _next = next;
        }

        public async Task InvokeAsync(HttpContext context, Data dbContext)
        {
            // Skip for unauthenticated users or users accessing auth-related paths
            if (!context.User.Identity.IsAuthenticated || 
                context.Request.Path.StartsWithSegments("/Auth"))
            {
                await _next(context);
                return;
            }

            var userRole = context.User.Claims.FirstOrDefault(c => c.Type == ClaimTypes.Role)?.Value;
            
            // Only check for Accountant and BackOffice roles
            if (userRole == "Accountant" || userRole == "BackOffice")
            {
                var userId = int.Parse(context.User.Claims.FirstOrDefault(c => c.Type == ClaimTypes.NameIdentifier)?.Value);
                var currentIp = context.Connection.RemoteIpAddress.ToString();
                
                // For local development
                if (currentIp == "::1" || currentIp == "127.0.0.1")
                {
                    currentIp = "localhost";
                }

                bool isAllowedIp = false;
                bool hasIpRestrictions = false;
                
                if (userRole == "Accountant")
                {
                    var user = await dbContext.Accountants
                        .Include(a => a.IpAddresses)
                        .FirstOrDefaultAsync(a => a.Id == userId);
                    
                    hasIpRestrictions = user?.IpAddresses?.Any() ?? false;
                    isAllowedIp = !hasIpRestrictions || (user?.IpAddresses?.Any(ip => ip.Address == currentIp) ?? false);
                }
                else if (userRole == "BackOffice")
                {
                    var user = await dbContext.BackOffices
                        .Include(b => b.IpAddresses)
                        .FirstOrDefaultAsync(b => b.Id == userId);
                    
                    hasIpRestrictions = user?.IpAddresses?.Any() ?? false;
                    isAllowedIp = !hasIpRestrictions || (user?.IpAddresses?.Any(ip => ip.Address == currentIp) ?? false);
                }

                if (!isAllowedIp)
                {
                    context.Response.Redirect("/Auth/AccessDenied");
                    return;
                }
            }

            await _next(context);
        }
    }
}
