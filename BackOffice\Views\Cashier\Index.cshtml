﻿@model IEnumerable<POS.Core.Models.Cashier>

@{
    ViewData["Title"] = "Cashiers";
}

<h1>Cashiers</h1>

<p>
    <a href="@Url.Action("Create")" class="btn btn-primary">Create New Cashier</a>
</p>

<table class="table">
    <thead>
        <tr>
            <th>Id</th>
            <th>Name</th>
            <th>Assigned Stores</th>
            <th>Actions</th>
        </tr>
    </thead>
    <tbody>
        @foreach (var cashier in Model)
        {
            <tr>
                <td>@cashier.Id</td>
                <td>@cashier.Name</td>
                <td>
                    @foreach (var cashierStore in cashier.CashierStores)
                    {
                        <span>@cashierStore.Store.StoreName</span>
                        <br />
                    }
                </td>
                <td>
                    <a href="@Url.Action("Edit", new { id = cashier.Id })" class="btn btn-warning">Edit</a>
                    <a href="@Url.Action("Delete", new { id = cashier.Id })" class="btn btn-danger">Delete</a>
                </td>
            </tr>
        }
    </tbody>
</table>
