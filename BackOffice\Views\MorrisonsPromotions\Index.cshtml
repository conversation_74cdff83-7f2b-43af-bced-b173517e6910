﻿@{
    ViewData["Title"] = "Upload Promotions File";
}

<div class="container">
    <h2>Morrisons Promotions Upload</h2>

    <div id="uploadForm">
        <form method="post" enctype="multipart/form-data" id="promotionsForm">
            <div class="form-group">
                <label for="file">Upload Excel File:</label>
                <input type="file" name="file" id="file" class="form-control-file" />
            </div>
            <button type="submit" class="btn btn-primary">Upload</button>
        </form>
    </div>

    <div id="syncProgress" style="display: none;" class="mt-4">
        <div class="progress">
            <div class="progress-bar" role="progressbar" style="width: 0%"></div>
        </div>
        <p id="statusMessage" class="text-info mt-2"></p>
    </div>

    <div id="syncResults" style="display: none;" class="mt-4">
        <div class="alert alert-success">
            <h4>Upload Complete</h4>
            <p>Total Promotions: <span id="totalPromotions">0</span></p>
            <p>Single Product Promotions: <span id="singleProductPromotions">0</span></p>
            <p>Multi-Product Promotions: <span id="multiProductPromotions">0</span></p>
            <p>Processed At: <span id="processedAt"></span></p>
        </div>
    </div>

    <div id="syncError" class="alert alert-danger mt-4" style="display: none">
    </div>
</div>

@section Scripts {
    <script src="~/lib/microsoft/signalr/signalr.min.js"></script>
    <script>
        $(document).ready(function() {
            const connection = new signalR.HubConnectionBuilder()
                .withUrl("/syncProgressHub")
                .withAutomaticReconnect()
                .build();

            connection.on("ReceiveProgress", (message, processed, total) => {
                const percentage = Math.round((processed / total) * 100);
                $("#syncProgress").show();
                $("#syncProgress .progress-bar")
                    .css("width", percentage + "%")
                    .attr("aria-valuenow", percentage)
                    .text(percentage + "%");
                $("#statusMessage").text(message);
            });

            connection.on("ReceiveError", (errorMessage) => {
                $("#syncProgress").hide();
                $("#syncError").show().text(errorMessage);
            });

            connection.on("SyncComplete", (result) => {
                $("#syncProgress").hide();
                $("#syncResults").show();
                $("#totalPromotions").text(result.totalPromotions);
                $("#singleProductPromotions").text(result.singleProductPromotions);
                $("#multiProductPromotions").text(result.multiProductPromotions);
                $("#processedAt").text(new Date(result.processedAt).toLocaleString());
            });

            connection.start()
                .then(() => console.log("SignalR Connected"))
                .catch(err => console.error("SignalR Connection Error: " + err));

            $("#promotionsForm").on("submit", function(e) {
                e.preventDefault();
                
                $("#syncResults").hide();
                $("#syncError").hide();
                
                var formData = new FormData();
                formData.append('file', $('#file')[0].files[0]);

                $.ajax({
                    url: '@Url.Action("UploadExcel")',
                    type: 'POST',
                    data: formData,
                    processData: false,
                    contentType: false,
                    success: function(result) {
                        console.log("Upload successful", result);
                    },
                    error: function(xhr, status, error) {
                        $("#syncError").show().text("Upload failed: " + error);
                    }
                });
            });
        });
    </script>
}
