using System;
using System.Linq;
using System.Threading.Tasks;
using Microsoft.EntityFrameworkCore;
using POS.Core.Models;
using POS.WPF.LocalData;

namespace POS.WPF.Services
{
    public class SyncService : ISyncService
    {
        private readonly LocalContext _localContext;
        private readonly ApiSyncClient _apiClient;

        public SyncService(string apiBaseUrl = null)
        {
            _localContext = new LocalContext();

            // Use the provided URL, or get from config, or use default
            _apiClient = new ApiSyncClient();
        }

        public async Task QueueForSync(string entityType, object entity, string operationType)
        {
            // Get the ID property from the entity
            var entityTypeObj = entity.GetType();
            var idProperty = entityTypeObj.GetProperty("Id") ?? entityTypeObj.GetProperty($"{entityTypeObj.Name}Id");

            if (idProperty == null)
                throw new InvalidOperationException($"Could not find ID property for entity type {entityTypeObj.Name}");

            var entityId = (int)idProperty.GetValue(entity);

            var pendingItem = new PendingSyncItem
            {
                EntityType = entityType,
                EntityId = entityId,
                CreatedAt = DateTime.Now,
                IsSynced = false,
                OperationType = operationType,
                SyncError = "",
                RetryCount = 0
            };

            _localContext.PendingSyncItems.Add(pendingItem);
            await _localContext.SaveChangesAsync();
        }

        public async Task ProcessPendingSyncItems()
        {
            var pendingItems = await _localContext.PendingSyncItems
                .Where(p => !p.IsSynced)
                .OrderBy(p => p.CreatedAt)
                .ToListAsync();

            foreach (var item in pendingItems)
            {
                try
                {
                    await ProcessItem(item);
                    item.IsSynced = true;
                    item.SyncedAt = DateTime.Now;
                    await _localContext.SaveChangesAsync();

                }
                catch (Exception ex)
                {
                    item.SyncError = ex.Message;
                    item.RetryCount++;
                }
            }
        }

        private async Task ProcessItem(PendingSyncItem item)
        {
            // Get the actual type from the assembly
            var type = AppDomain.CurrentDomain.GetAssemblies()
                .SelectMany(a => a.GetTypes())
                .FirstOrDefault(t => t.Name == item.EntityType);

            if (type == null)
                throw new ArgumentException($"Entity type '{item.EntityType}' not found");

            // Get the local entity
            var localEntity = await GetLocalEntity(type, item.EntityId);
            if (localEntity == null)
                throw new InvalidOperationException($"Local entity of type {item.EntityType} with ID {item.EntityId} not found");

            switch (item.OperationType.ToLower())
            {
                case "insert":
                    await HandleInsert(item.EntityType, localEntity);
                    break;
                case "update":
                    await HandleUpdate(item.EntityType, localEntity);
                    break;
                case "delete":
                    await HandleDelete(item.EntityType, localEntity);
                    break;
            }
        }

        private async Task<object> GetLocalEntity(Type type, int entityId)
        {

            object dbSet = null;

            if (type == typeof(SalePaymentMethod2))//special case
            {
                dbSet = _localContext.GetType().GetProperty("SalePaymentMethods")?.GetValue(_localContext);
            }
            else
            {
                dbSet = _localContext.GetType().GetProperty(type.Name + "s")?.GetValue(_localContext);
                if (dbSet == null)
                    throw new InvalidOperationException($"DbSet for type {type.Name} not found");
            }
            var findMethod = dbSet.GetType().GetMethod("Find", new[] { typeof(object[]) });
            return await Task.FromResult(findMethod.Invoke(dbSet, new object[] { new object[] { entityId } }));
        }

        private async Task HandleInsert(string entityType, object entity)
        {
            // Get the local ID before we modify the entity
            var localId = GetEntityId(entity);

            // Check if the entity is being tracked and detach it if it is
            var entry = _localContext.Entry(entity);
            if (entry.State != EntityState.Detached)
            {
                entry.State = EntityState.Detached;
            }

            // Update foreign keys using IdMappings
            await UpdateForeignKeys(entity);

            // Clear the ID to allow auto-generation on the remote database
            SetEntityId(entity, 0);

            // Add to remote via API
            try
            {
                // Log the entity data for debugging
                Console.WriteLine($"Sending entity of type {entityType} to API");

                // Serialize the entity to see what's being sent
                var jsonOptions = new System.Text.Json.JsonSerializerOptions
                {
                    WriteIndented = true,
                    ReferenceHandler = System.Text.Json.Serialization.ReferenceHandler.Preserve
                };
                var entityJson = System.Text.Json.JsonSerializer.Serialize(entity, jsonOptions);
                Console.WriteLine($"Entity data: {entityJson}");

                var remoteId = await _apiClient.CreateEntityAsync(entityType, entity);
                Console.WriteLine($"Successfully created entity with remote ID: {remoteId}");

                // Create ID mapping
                await CreateIdMapping(entityType, localId, remoteId);
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Failed to create entity: {ex.Message}");
                if (ex.InnerException != null)
                {
                    Console.WriteLine($"Inner exception: {ex.InnerException.Message}");
                }
                Console.WriteLine($"Stack trace: {ex.StackTrace}");

                // Re-throw the exception to be handled by the caller
                throw new Exception($"Failed to create entity of type {entityType}: {ex.Message}", ex);
            }
        }

        private async Task UpdateForeignKeys(object entity)
        {
            var entityType = entity.GetType();
            var properties = entityType.GetProperties()
                .Where(p => p.Name.EndsWith("Id") && p.Name != "Id" && p.Name != $"{entityType.Name}Id");

            foreach (var property in properties)
            {
                var localForeignKeyValue = (int?)property.GetValue(entity);
                if (localForeignKeyValue.HasValue)
                {
                    // Get the related entity type from the property name (e.g., "StoreDayId" -> "StoreDay")
                    var relatedEntityType = property.Name.Substring(0, property.Name.Length - 2);

                    // Get the remote ID from mappings
                    var mapping = await GetRemoteId(relatedEntityType, localForeignKeyValue.Value);
                    if (mapping != null)
                    {
                        // Update the foreign key with the remote ID
                        property.SetValue(entity, mapping.RemoteId);
                    }
                }
            }
        }

        private async Task HandleUpdate(string entityType, object entity)
        {
            var localId = GetEntityId(entity);
            var mapping = await GetRemoteId(entityType, localId);
            if (mapping == null) return;

            // Detach the entity from the local context if it's being tracked
            var localEntry = _localContext.Entry(entity);
            if (localEntry.State != EntityState.Detached)
            {
                localEntry.State = EntityState.Detached;
            }

            await UpdateForeignKeys(entity);

            // Update via API
            await _apiClient.UpdateEntityAsync(entityType, mapping.RemoteId, entity);
        }

        private async Task HandleDelete(string entityType, object entity)
        {
            var localId = GetEntityId(entity);

            // Check if the entity is being tracked and detach it if it is
            var entry = _localContext.Entry(entity);
            if (entry.State != EntityState.Detached)
            {
                entry.State = EntityState.Detached;
            }

            var mapping = await GetRemoteId(entityType, localId);
            if (mapping == null) return;

            // Delete via API
            await _apiClient.DeleteEntityAsync(entityType, mapping.RemoteId);
        }

        public async Task<IdMapping> GetRemoteId(string entityType, int localId)
        {
            // Only check local database
            var localMapping = await _localContext.IdMappings
                .FirstOrDefaultAsync(m => m.EntityType == entityType && m.LocalId == localId);

            return localMapping;
        }

        public async Task<IdMapping> CreateIdMapping(string entityType, int localId, int remoteId)
        {
            var mapping = new IdMapping
            {
                EntityType = entityType,
                LocalId = localId,
                RemoteId = remoteId,
                MappedAt = DateTime.Now,
            };

            // Add to local database only
            _localContext.IdMappings.Add(mapping);
            await _localContext.SaveChangesAsync();

            return mapping;
        }

        private int GetEntityId(object entity)
        {
            var type = entity.GetType();
            var idProperty = type.GetProperty("Id") ?? type.GetProperty($"{type.Name}Id");

            if (idProperty == null)
                throw new InvalidOperationException($"Could not find ID property for entity type {type.Name}");

            return (int)idProperty.GetValue(entity);
        }

        private void SetEntityId(object entity, int id)
        {
            var type = entity.GetType();
            var idProperty = type.GetProperty("Id") ?? type.GetProperty($"{type.Name}Id");

            if (idProperty == null)
                throw new InvalidOperationException($"Could not find ID property for entity type {type.Name}");

            idProperty.SetValue(entity, id);
        }
    }
}
