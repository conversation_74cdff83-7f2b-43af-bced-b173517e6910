using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace POS.Core.Models
{
    public class QuickAccessButton : BaseEntity
    {
        [Key]
        public int Id { get; set; }
        
        public string ButtonText { get; set; }
        
        public int ProductId { get; set; }
        
        [ForeignKey("ProductId")]
        public Product Product { get; set; }
        
        public int StoreId { get; set; }
        
        [ForeignKey("StoreId")]
        public Store Store { get; set; }
    }
}