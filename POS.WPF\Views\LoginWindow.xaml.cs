﻿using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Internal;
using NLog;
using POS.Core.Models;
using POS.WPF.Helpers;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Windows;
using System.Windows.Controls;
using System.Windows.Data;
using System.Windows.Documents;
using System.Windows.Input;
using System.Windows.Interop;
using System.Windows.Media;
using System.Windows.Media.Imaging;
using System.Windows.Shapes;
using TimeManagement;
using WindowsInput;
using WindowsInput.Native;
using POS.WPF.Views;
using System.Diagnostics.Metrics;

namespace POS.WPF
{
    public class TempColResult
    {
        public string PayloadJson { get; set; }
    }
    /// <summary>
    /// Interaction logic for LoginWindow.xaml
    /// </summary>
    public partial class LoginWindow : Window
    {
        private InputSimulator _inputSimulator;
        private static readonly Logger _logger = LogManager.GetCurrentClassLogger();

        public LoginWindow()
        {
          
           


            if (ConfigurationHelper.HideButtons)
            {
                DisableCloseButton = true;
            }
            InitializeComponent();
            _inputSimulator = new InputSimulator();
            LoadLogo();
            CreateInitialStartDate();
            Helpers.WindowStyleHelper.SetWindowStyle(this);
        }

        private const int SC_CLOSE = 0xF060;
        private const int WM_SYSCOMMAND = 0x0112;

        // Flag to enable/disable close button dynamically
        public bool DisableCloseButton { get; set; } = false;
        public bool txtUnGotFocusLast { get; private set; }

        protected override void OnSourceInitialized(EventArgs e)
        {
            base.OnSourceInitialized(e);
            IntPtr hwnd = new WindowInteropHelper(this).Handle;
            HwndSource source = HwndSource.FromHwnd(hwnd);
            source.AddHook(WndProc);
        }

        private IntPtr WndProc(IntPtr hwnd, int msg, IntPtr wParam, IntPtr lParam, ref bool handled)
        {
            if (DisableCloseButton && msg == WM_SYSCOMMAND && wParam.ToInt32() == SC_CLOSE)
            {
                handled = true; // Prevents the window from closing
            }
            return IntPtr.Zero;
        }

        // Method to toggle close button behavior dynamically
        public void SetCloseButtonState(bool disable)
        {
            DisableCloseButton = disable;
        }

        /// <summary>
        /// Initial Day Is created only if there is no existing days in StoreDay table
        /// </summary>
        void CreateInitialStartDate()
        {
            using (LocalData.LocalContext localData = new LocalData.LocalContext())
            {
                int posMachineId = int.Parse(ConfigurationHelper.PosMachineId);
                UiShift.PosMachineId = posMachineId;

                var PosMachine = localData.PosMachines.Include(p => p.Store)
                    .ThenInclude(s => s.Brand)
                    .Where(p => p.Id == posMachineId).FirstOrDefault();

                try
                {
                    var store = localData.Stores.Where(s => s.StoreId == PosMachine.Store.StoreId).FirstOrDefault();

                    if (localData.StoreDays.Count(s => s.StoreId == store.StoreId) == 0)
                    {
                        StoreDay newStoreDay = new StoreDay
                        {
                            StartTime = ConfigurationHelper.InitialDayStartTime,
                            StoreId = store.StoreId,
                            InProgress = true,
                            IsDeleted = false,
                            LastModified = CustomTimeProvider.Now
                        };
                        
                        // Save to local database
                        localData.StoreDays.Add(newStoreDay);
                        localData.SaveChanges();

                        // Create PendingSyncItem for syncing
                        var pendingSyncItem = new PendingSyncItem
                        {
                            EntityType = "StoreDay",
                            EntityId = newStoreDay.Id,
                            CreatedAt = CustomTimeProvider.Now,
                            IsSynced = false,
                            OperationType = "Insert",
                            SyncError = ""
                        };

                        // Add to PendingSyncItem for syncing
                        localData.PendingSyncItems.Add(pendingSyncItem);
                        localData.SaveChanges();

                        MessageBox.Show("Initial Day Created!");
                    }
                }
                catch (Exception ex)
                {
                    MessageBox.Show("Error Creating Initial Day!");
                    _logger.Error(ex);
                    return;
                }
            }
        }

        private void LoadLogo()
        {
            try
            {
                using (LocalData.LocalContext data = new LocalData.LocalContext())
                {
                    int posMachineId = int.Parse(ConfigurationHelper.PosMachineId);

                    var PosMachine = data.PosMachines.Include(p => p.Store)
                        .ThenInclude(s => s.Brand)
                        .Where(p => p.Id == posMachineId).FirstOrDefault();

                    string baseUrl = ConfigurationHelper.LogoUrl; // Replace with actual remote URL
                    string imageName = PosMachine.Store.Brand.BrandLogo; // Replace with actual image name
                    string fullUrl = $"{baseUrl}/{imageName}";

                    BitmapImage bitmap = new BitmapImage();
                    bitmap.BeginInit();
                    bitmap.UriSource = new Uri(fullUrl);
                    bitmap.CacheOption = BitmapCacheOption.OnLoad;
                    bitmap.EndInit();

                    imgLogo.Source = bitmap;
                }
            }
            catch (Exception ex)
            {
                _logger.Error(ex);
            }
        }

        static void SeedData(LocalData.LocalContext context)
        {
            //context.Database.EnsureDeleted();
            //context.Database.EnsureCreated();

            #region BaseLine Seed Data
            Brand brand1 = new Brand
            {
                Name = "Morrison",
                IsDeleted = false,
                LastModified = CustomTimeProvider.Now
            };
            Brand brand2 = new Brand
            {
                Name = "Budgens",
                IsDeleted = false,
                LastModified = CustomTimeProvider.Now
            };

            Vat vatStandard = new Vat
            {
                Code = "Code0",
                Value = 20,
                Rate = "20%",
                IsDeleted = false,
                LastModified = CustomTimeProvider.Now
            };
            Vat vatReduced = new Vat
            {
                Code = "Code1",
                Value = 5,
                Rate = "5%",
                IsDeleted = false,
                LastModified = CustomTimeProvider.Now
            };
            Vat vatZero = new Vat
            {
                Code = "Code2",
                Value = 0,
                Rate = "0%",
                IsDeleted = false,
                LastModified = CustomTimeProvider.Now
            };
            Vat vatExempt = new Vat
            {
                Code = "Excempt",
                Value = 0,
                Rate = "0%",
                IsDeleted = false,
                LastModified = CustomTimeProvider.Now
            };

            SubCatagory SubCatagoryHotFoods = new SubCatagory
            {
                Name = "HotFoods",
                Catagory = Catagory.ShopSales,
                StockType = StockType.Stock,
                IsDeleted = false,
                LastModified = CustomTimeProvider.Now
            };
            SubCatagory SubCatagoryNewsPapers = new SubCatagory
            {
                Name = "NewsPapers",
                Catagory = Catagory.ShopSales,
                StockType = StockType.Stock,
                IsDeleted = false,
                LastModified = CustomTimeProvider.Now
            };
            SubCatagory SubCatagoryPayPoint = new SubCatagory
            {
                Name = "PayPoint",
                Catagory = Catagory.Excempt,
                StockType = StockType.Stock,
                IsDeleted = false,
                LastModified = CustomTimeProvider.Now
            };
            SubCatagory ThirdParty = new SubCatagory
            {
                Name = "3rdParty",
                Catagory = Catagory.Excempt,
                StockType = StockType.Stock,
                IsDeleted = false,
                LastModified = CustomTimeProvider.Now
            };

            Division DivisionHotFoods = new Division
            {
                Name = "HotFoods",
                SubCatagory = SubCatagoryHotFoods,
                IsDeleted = false,
                LastModified = CustomTimeProvider.Now
            };
            Division DivisionNewsPapers = new Division
            {
                Name = "NewsPapers",
                SubCatagory = SubCatagoryNewsPapers,
                IsDeleted = false,
                LastModified = CustomTimeProvider.Now
            };
            Division DivisionPayPoint = new Division
            {
                Name = "PayPoint",
                SubCatagory = SubCatagoryPayPoint,
                IsDeleted = false,
                LastModified = CustomTimeProvider.Now
            };
            Division DivisionThirdParty = new Division
            {
                Name = "3rdParty",
                SubCatagory = ThirdParty,
                IsDeleted = false,
                LastModified = CustomTimeProvider.Now
            };

            POS.Core.Models.Product ProductPayPoint = new POS.Core.Models.Product()
            {
                Barcode = "2342342342342353532432424235345",
                Brand = null,
                Description = "",
                Division = DivisionPayPoint,
                Name = "PayPoint",
                PLU = "",
                PurchasePackSize = 0,
                PurchasePrice = 0,
                SellingPrice = 0,
                SpecialProduct = true,
                Vat = vatExempt,
                IsDeleted = false,
                LastModified = CustomTimeProvider.Now
            };

            POS.Core.Models.Product ProductNewsPapers = new POS.Core.Models.Product()
            {
                Barcode = "31324546574645345346564643545",
                Brand = null,
                Description = "",
                Division = DivisionNewsPapers,
                Name = "NewsPapers",
                PLU = "",
                PurchasePackSize = 0,
                PurchasePrice = 0,
                SellingPrice = 0,
                SpecialProduct = true,
                Vat = vatZero,
                IsDeleted = false,
                LastModified = CustomTimeProvider.Now
            };

            POS.Core.Models.Product ProductHotFoods = new POS.Core.Models.Product()
            {
                Barcode = "898757463453453456576574563445",
                Brand = null,
                Description = "",
                Division = DivisionHotFoods,
                Name = "HotFoods",
                PLU = "",
                PurchasePackSize = 0,
                PurchasePrice = 0,
                SellingPrice = 0,
                SpecialProduct = true,
                Vat = vatStandard,
                IsDeleted = false,
                LastModified = CustomTimeProvider.Now
            };

            POS.Core.Models.Product ThirdPartyProduct = new POS.Core.Models.Product()
            {
                Barcode = "3423454563536787667567678",
                Brand = null,
                Description = "",
                Division = DivisionThirdParty,
                Name = "ThirdParty",
                PLU = "",
                PurchasePackSize = 0,
                PurchasePrice = 0,
                SellingPrice = 0,
                SpecialProduct = true,
                Vat = vatStandard,
                IsDeleted = false,
                LastModified = CustomTimeProvider.Now
            };

            var paymentMethods = new List<PaymentMethod>
            {
                new PaymentMethod { Method = "Card",  IsDeleted = false,
                    LastModified = CustomTimeProvider.Now },
                new PaymentMethod { Method = "Cash",  IsDeleted = false,
                    LastModified = CustomTimeProvider.Now },
                new PaymentMethod { Method = "Voucher" ,  IsDeleted = false,
                    LastModified = CustomTimeProvider.Now},
                new PaymentMethod { Method = "Deliveroo",  IsDeleted = false,
                    LastModified = CustomTimeProvider.Now },
                new PaymentMethod { Method = "Uber",  IsDeleted = false,
                    LastModified = CustomTimeProvider.Now },
                new PaymentMethod { Method = "PP Card",  IsDeleted = false,
                    LastModified = CustomTimeProvider.Now }
            };

            var paidOutOptions = new List<PaidOutOption>
            {
                new PaidOutOption { Option = "SCRATCH CARD" ,  IsDeleted = false,
                    LastModified = CustomTimeProvider.Now},
                new PaidOutOption { Option = "NATIONAL LOTTERY" ,  IsDeleted = false,
                    LastModified = CustomTimeProvider.Now},
                new PaidOutOption { Option = "PP CREDIT" ,  IsDeleted = false,
                    LastModified = CustomTimeProvider.Now},
                new PaidOutOption { Option = "OTHERS",  IsDeleted = false,
                    LastModified = CustomTimeProvider.Now }
            };

            context.PaymentMethods.AddRange(paymentMethods);
            context.PaidOutOptions.AddRange(paidOutOptions);
            context.Brands.AddRange(brand1, brand2);
            context.Products.AddRange(ProductHotFoods, ProductNewsPapers, ProductPayPoint, ThirdPartyProduct);
            context.Vats.AddRange(vatStandard, vatReduced, vatZero);
            context.SaveChanges();
            Console.WriteLine("BaseLine Seed Data Seeded Successfully!");
            #endregion

            #region Dynamic Seed Data
            var store1 = new Store
            {
                StoreName = "Downtown Store",
                Brand = brand1,
                VatNo = "123 456 678",
                IsDeleted = false,
                LastModified = CustomTimeProvider.Now
            };
            var store2 = new Store
            {
                StoreName = "Uptown Store",
                Brand = brand2,
                VatNo = "987 654 321",
                IsDeleted = false,
                LastModified = CustomTimeProvider.Now
            };

            var cashier1 = new Cashier
            {
                Name = "John",
                Password = "John",
                Role = "Cashier",
                IsDeleted = false,
                LastModified = CustomTimeProvider.Now
            };
            var cashier2 = new Cashier
            {
                Name = "Jane",
                Password = "Jane",
                Role = "Cashier",
                IsDeleted = false,
                LastModified = CustomTimeProvider.Now
            };
            var accountant = new Accountant
            {
                Name = "Nick",
                Password = "Nick",
                Role = "Accountant",
                IsDeleted = false,
                LastModified = CustomTimeProvider.Now
            };
            var admin = new Admin
            {
                Name = "Bob",
                Password = "Bob",
                Role = "Admin",
                IsDeleted = false,
                LastModified = CustomTimeProvider.Now
            };

            PosMachine posMachine = new PosMachine
            {
                Store = store1,
                IsDeleted = false,
                LastModified = CustomTimeProvider.Now
            };
            PosMachine posMachine1 = new PosMachine
            {
                Store = store2,
                IsDeleted = false,
                LastModified = CustomTimeProvider.Now
            };

            cashier1.CashierStores.Add(new CashierStore { Store = store1, IsDeleted = false, LastModified = CustomTimeProvider.Now });
            cashier1.CashierStores.Add(new CashierStore { Store = store2, IsDeleted = false, LastModified = CustomTimeProvider.Now });

            cashier2.CashierStores.Add(new CashierStore { Store = store1, IsDeleted = false, LastModified = CustomTimeProvider.Now });
            cashier2.CashierStores.Add(new CashierStore { Store = store2, IsDeleted = false, LastModified = CustomTimeProvider.Now });

            context.Stores.AddRange(store1, store2);
            context.PosMachines.AddRange(posMachine, posMachine1);
            context.Cashiers.Add(cashier1);
            context.Cashiers.Add(cashier2);
            context.Accountants.Add(accountant);
            context.Admins.Add(admin);
            context.SaveChanges();
            cashier1.UserId = cashier1.Id.ToString("D4");
            cashier1.Password = cashier1.UserId;
            cashier2.UserId = cashier2.Id.ToString("D4");
            cashier2.Password = cashier2.UserId;
            context.SaveChanges();
            #endregion
        }


        private void Button_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                using (LocalData.LocalContext localData = new LocalData.LocalContext())
                {
                    int posMachineId = int.Parse(ConfigurationHelper.PosMachineId);
                    UiShift.PosMachineId = posMachineId;

                    var posMachine = GetPosMachine(posMachineId);
                    if (posMachine == null)
                    {
                        MessageBox.Show("POS Machine not found!");
                        return;
                    }

                    var store = GetStore(posMachine);
                    if (store == null)
                    {
                        MessageBox.Show("Store not found!");
                        return;
                    }

                    UiShift.StoreName = store.StoreName;

                    var cashier = AuthenticateCashier(store);
                    if (cashier == null) return;

                    UiShift.CashierId = cashier.Id;
                    UiShift.CashierName = cashier.Name;
                    UiShift.StoreId = store.StoreId;

                    ProcessStoreDay(store);

                    if (ProcessShift(posMachineId) == false)
                    {
                        MessageBox.Show("Different Cashier!");
                        return;
                    }

                    OpenMainWindow();
                }
            }
            catch (Exception ex)
            {
                _logger.Error(ex);
            }
        }

        /// <summary>
        /// Retrieves the POS machine details based on the given POS machine ID.
        /// </summary>
        /// <param name="data">The database context.</param>
        /// <param name="posMachineId">The ID of the POS machine.</param>
        /// <returns>The POS machine entity if found, otherwise null.</returns>
        private PosMachine GetPosMachine(int posMachineId)
        {
            using (LocalData.LocalContext data = new LocalData.LocalContext())
            {
                return data.PosMachines
                    .Include(p => p.Store)
                    .ThenInclude(s => s.Brand)
                    .FirstOrDefault(p => p.Id == posMachineId);
            }
        }

        /// <summary>
        /// Retrieves the store associated with the given POS machine.
        /// </summary>
        /// <param name="data">The database context.</param>
        /// <param name="posMachine">The POS machine entity.</param>
        /// <returns>The store entity if found, otherwise null.</returns>
        private Store GetStore(PosMachine posMachine)
        {
            using (LocalData.LocalContext data = new LocalData.LocalContext())
            {
                return data.Stores
        .Include(s => s.CashierStores
            .Where(sc => !sc.IsDeleted))
            .ThenInclude(sc => sc.Cashier)
        .FirstOrDefault(s => s.StoreId == posMachine.Store.StoreId);
            }
        }

        /// <summary>
        /// Authenticates the cashier using the provided username and password, and verifies if the cashier belongs to the given store.
        /// </summary>
        /// <param name="data">The database context.</param>
        /// <param name="store">The store entity.</param>
        /// <returns>The authenticated cashier if valid, otherwise null.</returns>
        private Cashier AuthenticateCashier(Store store)
        {
            using (LocalData.LocalContext data = new LocalData.LocalContext())
            {
                var cashier = data.Cashiers
                    .FirstOrDefault(c => c.UserId == txtUN.Text && c.Password == txtPW.Password);

                if (cashier == null)
                {
                    MessageBox.Show("Login Error!");
                    return null;
                }

                if (!store.CashierStores.Any(sc => !sc.IsDeleted && sc.CashierId == cashier.Id))
                {
                    MessageBox.Show("Cashier not found in this store!");
                    return null;
                }

                return cashier;
            }
        }

        /// <summary>
        /// Retrieves the ongoing StoreDay for the given store. If no StoreDay is in progress, a new one is created.
        /// </summary>
        /// <param name="data">The database context.</param>
        /// <param name="store">The store entity.</param>
        private void ProcessStoreDay(Store store)
        {
            if (UiShift.StoreDayId != null) return;

            using (LocalData.LocalContext localData = new LocalData.LocalContext())
            {
                var storeDay = localData.StoreDays.FirstOrDefault(s => s.StoreId == store.StoreId && s.InProgress);

                if (storeDay == null)
                {
                    storeDay = new StoreDay
                    {
                        StartTime = CustomTimeProvider.Now,
                        StoreId = store.StoreId,
                        InProgress = true,
                        IsDeleted = false,
                        LastModified = CustomTimeProvider.Now
                    };
                    
                    // Save to local database
                    localData.StoreDays.Add(storeDay);
                    localData.SaveChanges();

                    // Create PendingSyncItem for syncing
                    var pendingSyncItem = new PendingSyncItem
                    {
                        EntityType = "StoreDay",
                        EntityId = storeDay.Id,
                        CreatedAt = CustomTimeProvider.Now,
                        IsSynced = false,
                        OperationType = "Insert",
                        SyncError = ""
                    };

                    localData.PendingSyncItems.Add(pendingSyncItem);
                    localData.SaveChanges();
                }

                UiShift.StoreDayId = storeDay.Id;
                UiShift.StoreDay = storeDay;
            }
        }

        /// <summary>
        /// Manages the shift for the current cashier and POS machine. If no shift is in progress, a new one is created.
        /// </summary>
        /// <param name="data">The database context.</param>
        /// <param name="posMachineId">The ID of the POS machine.</param>
        /// <summary>
        /// Manages the shift for the current cashier and POS machine. 
        /// If no shift is in progress, a new one is created.
        /// If the cashier is different from the ongoing shift, it returns false.
        /// </summary>
        /// <param name="data">The database context.</param>
        /// <param name="posMachineId">The ID of the POS machine.</param>
        /// <returns>Returns true if the shift assignment is successful, otherwise false.</returns>
        private bool ProcessShift(int posMachineId)
        {
            using (LocalData.LocalContext localData = new LocalData.LocalContext())
            {
                if (UiShift.ShiftId == null) // First shift on this POS machine for the day
                {
                    var shift = localData.Shifts.FirstOrDefault(s => s.StoreDay.Id == UiShift.StoreDayId && s.InProgress);

                    if (shift == null)
                    {
                        shift = new Shift
                        {
                            StartTime = CustomTimeProvider.Now,
                            StoreDayId = UiShift.StoreDayId,
                            CashierId = UiShift.CashierId,
                            PosMachineId = posMachineId,
                            InProgress = true,
                            IsDeleted = false,
                            LastModified = CustomTimeProvider.Now
                             
                        };
                        
                        // Save to local database
                        localData.Shifts.Add(shift);
                        localData.SaveChanges();

                        // Create PendingSyncItem for syncing
                        var pendingSyncItem = new PendingSyncItem
                        {
                            EntityType = "Shift",
                            EntityId = shift.Id,
                            CreatedAt = CustomTimeProvider.Now,
                            IsSynced = false,
                            OperationType = "Insert",
                            SyncError = ""
                        };


                        localData.PendingSyncItems.Add(pendingSyncItem);
                        localData.SaveChanges();


                        UiShift.ShiftId = shift.Id;
                        UiShift.Shift = shift;
                        MessageBox.Show("New Shift Created!");
                        return true;
                    }

                    // Shift in progress but cashier mismatch
                    if (shift.CashierId != UiShift.CashierId)
                    {
                        MessageBox.Show("Different Cashier! Cannot continue this shift.");
                        return false;
                    }

                    // Returning to previous shift
                    UiShift.ShiftId = shift.Id;
                    UiShift.Shift = shift;
                    MessageBox.Show("Returned to Previous Shift!");
                    return true;
                }

                var existingShift = localData.Shifts.FirstOrDefault(s => s.Id == UiShift.ShiftId);

                // Existing shift but cashier mismatch
                if (existingShift?.CashierId != UiShift.CashierId)
                {
                    MessageBox.Show("Different Cashier! Cannot continue this shift.");
                    return false;
                }

                MessageBox.Show("Returned to Previous Shift!");
                return true;
            }
        }

        /// <summary>
        /// Opens the main window of the POS system and closes the current login window.
        /// </summary>
        private void OpenMainWindow()
        {
            MainWindow mainWindow = new MainWindow();
            mainWindow.Show();
            this.Close();
        }

        private void NumericKeypad_KeyPressed(object sender, string key)
        {
            if (txtUnGotFocusLast)
            {
                txtUN.Focus();
            }
            else
            {
                txtPW.Focus();
            }

            switch (key)
            {
                case "Backspace":
                    _inputSimulator.Keyboard.KeyPress(VirtualKeyCode.BACK);
                    break;
                case "%":
                    _inputSimulator.Keyboard.KeyPress(VirtualKeyCode.SHIFT, VirtualKeyCode.VK_5);
                    break;
                case "00":
                    _inputSimulator.Keyboard.KeyPress(VirtualKeyCode.VK_0);
                    _inputSimulator.Keyboard.KeyPress(VirtualKeyCode.VK_0);
                    break;
                case ".":
                    _inputSimulator.Keyboard.KeyPress(VirtualKeyCode.DECIMAL);
                    break;
                default:
                    if (int.TryParse(key, out var number))
                    {
                        var keyCode = (VirtualKeyCode)Enum.Parse(
                            typeof(VirtualKeyCode),
                            $"VK_{number}"
                        );
                        _inputSimulator.Keyboard.KeyPress(keyCode);
                    }
                    break;
            }
        }

        private void txtUN_GotFocus(object sender, RoutedEventArgs e)
        {

        }

        private void txtPW_GotFocus(object sender, RoutedEventArgs e)
        {

        }

        private void txtUN_GotFocus_1(object sender, RoutedEventArgs e)
        {

        }

        private void GotFocus(object sender, RoutedEventArgs e)
        {
            if ((sender as Control).Name == "txtUN")
            {
                txtUnGotFocusLast = true;
            }
            else if ((sender as Control).Name == "txtPW")
            {
                txtUnGotFocusLast = false;
            }
        }
    }
}
