﻿using System;
using System.Collections.Generic;
using System.IO.Ports;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace POS.WPF
{
    public class PaymentTerminalHandler
    {
        private SerialPort serialPort;

        public TransactionResult ProcessPayment(string comPort, int baudrate, decimal amount)
        {
            var result = new TransactionResult();

            // Configure serial port (adjust parameters as per terminal specs)
            serialPort = new SerialPort(comPort)
            {
                BaudRate = baudrate,
                Parity = Parity.None,
                DataBits = 8,
                StopBits = StopBits.One,
                Handshake = Handshake.RequestToSend,
                ReadTimeout = 30000, // 30 seconds
                WriteTimeout = 30000,
                 
                
            };

            try
            {
                serialPort.Open();

                // Construct payment command (example format; adjust per protocol)
                string command = BuildCommand(amount);

                // Send command
                byte[] data = Encoding.ASCII.GetBytes(command);
                serialPort.Write(data, 0, data.Length);

                // Read response
                string response = ReadResponse();
                ParseResponse(response, result);
            }
            catch (Exception ex)
            {
                result.IsSuccessful = false;
                result.ErrorMessage = ex.Message;
            }
            finally
            {
                if (serialPort?.IsOpen == true)
                    serialPort.Close();
            }

            return result;
        }

        private string BuildCommand(decimal amount)
        {
            // Example command format (STX + Amount + ETX + Checksum)
            // Replace with actual protocol requirements
            string amountStr = (amount * 100).ToString("00000000"); // Convert to lowest denomination
            string message = $"\x02{amountStr}\x03";
            byte checksum = CalculateChecksum(message);
            return message + checksum.ToString("X2"); // Append checksum as hex
        }

        private byte CalculateChecksum(string data)
        {
            // Example: XOR checksum (adjust per protocol)
            byte checksum = 0;
            foreach (char c in data)
            {
                checksum ^= (byte)c;
            }
            return checksum;
        }

        private string ReadResponse()
        {
            // Read until ETX or timeout
            StringBuilder response = new StringBuilder();
            bool endOfText = false;
            byte[] buffer = new byte[1];

            while (!endOfText)
            {
                int bytesRead = serialPort.Read(buffer, 0, 1);
                if (bytesRead > 0)
                {
                    char receivedChar = (char)buffer[0];
                    response.Append(receivedChar);

                    if (receivedChar == '\x03') // ETX
                    {
                        // Read checksum (assuming 1 byte)
                        bytesRead = serialPort.Read(buffer, 0, 1);
                        if (bytesRead > 0)
                        {
                            response.Append((char)buffer[0]);
                        }
                        endOfText = true;
                    }
                }
            }

            return response.ToString();
        }

        private void ParseResponse(string response, TransactionResult result)
        {
            // Example response format: STX + "APPROVED|AUTH123|123456|7890|VISA|..." + ETX + Checksum
            // Validate checksum first
            if (response.Length < 3)
            {
                result.IsSuccessful = false;
                result.ErrorMessage = "Invalid response length";
                return;
            }

            string messagePart = response.Substring(0, response.Length - 1);
            char receivedChecksum = response[response.Length - 1];

            byte expectedChecksum = CalculateChecksum(messagePart);
            if ((byte)receivedChecksum != expectedChecksum)
            {
                result.IsSuccessful = false;
                result.ErrorMessage = "Checksum error";
                return;
            }

            // Extract and parse the response data
            if (messagePart.StartsWith("\x02"))
            {
                string[] responseParts = messagePart.Substring(1).Split('|');

                if (responseParts.Length >= 1)
                {
                    result.IsSuccessful = responseParts[0] == "APPROVED";
                    result.ResponseCode = result.IsSuccessful ? 1 : 2; // 1=authorised, 2=declined
                }

                // Map response parts to TransactionResult properties
                // This mapping depends on your actual terminal's response format
                if (responseParts.Length >= 2) result.AuthorisationCode = responseParts[1];
                if (responseParts.Length >= 3) result.PanFirst6Digits = responseParts[2];
                if (responseParts.Length >= 4) result.PanLast4Digits = responseParts[3];
                if (responseParts.Length >= 5) result.SchemeName = responseParts[4];
                if (responseParts.Length >= 6) result.EmvApplicationLabel = responseParts[5];
                if (responseParts.Length >= 7) result.CustomerVerification = int.Parse(responseParts[6]);
                if (responseParts.Length >= 8) result.DataEntryMethod = int.Parse(responseParts[7]);
                if (responseParts.Length >= 9) result.CustomerReceipt = responseParts[8];

                // Generate a transaction reference if not provided by terminal
                result.TransactionRefNo = Guid.NewGuid().ToString();
            }
            else
            {
                result.IsSuccessful = false;
                result.ErrorMessage = "Invalid response format";
            }
        }
    }

    public class TransactionResult
    {
        public bool IsSuccessful { get; set; }
        public string AuthorisationCode { get; set; }
        public string TransactionRefNo { get; set; }
        public int ResponseCode { get; set; } // 0=aborted, 1=authorised, 2=declined, 3=cancelled
        public string PanFirst6Digits { get; set; }
        public string PanLast4Digits { get; set; }
        public string SchemeName { get; set; }
        public string EmvApplicationLabel { get; set; }
        public string EmvApplicationId { get; set; }
        public string MerchantNo { get; set; }
        public string TerminalId { get; set; }
        public string CurrencyCode { get; set; }
        public int CustomerVerification { get; set; } // 0=None, 1=Pin, 2=Signature, etc.
        public int CashbackAmount { get; set; } // In lowest denomination
        public int DataEntryMethod { get; set; } // 0=None, 1=Keyed, 2=Swipe, etc. *
        public int CardClass { get; set; } // 1=Credit/Debit, 2=Fuel Card
        public bool FuelBunkerCard { get; set; }
        public string CustomerReceipt { get; set; }
        public string Token { get; set; }
        public string ErrorMessage { get; set; }
    }

}
