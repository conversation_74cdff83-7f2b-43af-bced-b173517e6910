﻿using POS.Core.Models;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace POS.WPF.Helpers
{
    public static class UiShift
    {


        public static string CashierName { get; set; }
        public static int? CashierId { get; set; }
        public static int? ShiftId { get; set; }

        public static int? StoreId { get; set; }

        public static string StoreName { get; set; }

        public static int? PosMachineId { get; set; }

        public static int? StoreDayId { get; set; }

        public static StoreDay StoreDay { get; set; }

        public static Shift Shift { get; set; }

    }
}
