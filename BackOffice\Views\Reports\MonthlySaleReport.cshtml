﻿@using System.Globalization
@model IEnumerable<POS.BackOffice.ViewModels.MonthlySaleReportViewModel>
@{
    ViewBag.Title = "Monthly Sale Report";
    var last12Months = ViewBag.Last12Months as List<string>;
}

<h2>Monthly Sale Report</h2>

<table class="table table-bordered">
    <thead>
        <tr>
            <th>Sub Category</th>
            @foreach (var month in last12Months)
            {
                <th>@month</th>
            }
        </tr>
    </thead>
    <tbody>
        @foreach (var item in Model)
        {
            <tr>
                <td>@item.SubCategory</td>
                @foreach (var month in last12Months)
                {
                    <td>@item.MonthlySales[month].ToString("C", new CultureInfo("en-GB"))</td>
                }
            </tr>
        }
    </tbody>
</table>
