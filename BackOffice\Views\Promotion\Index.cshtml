﻿@model List<POS.Core.Models.Promotion>
@{
    int currentPage = ViewBag.CurrentPage;
    int totalPages = ViewBag.TotalPages;
}


<h2>Promotions</h2>

<a asp-action="Create" class="btn btn-success mb-3">Create New Promotion</a>

<table class="table table-bordered table-striped">
    <thead>
        <tr>
            <th>Name</th>
            <th>Identifier</th>
            <th>Type</th>
            <th>Price</th>
            <th>Quantity</th>
            <th>Start</th>
            <th>End</th>
            <th>Products</th>
            <th>Actions</th>
        </tr>
    </thead>
    <tbody>
        @foreach (var item in Model)
        {
            <tr>
                <td>@item.Name</td>
                <td>@item.Identifire</td>
                <td>@item.Type</td>
                <td>@item.PromotionPrice?.ToString("C")</td>
                <td>@item.Quantity</td>
                <td>@item.StartTime.ToShortDateString()</td>
                <td>@item.EndTime.ToShortDateString()</td>
                <td>
                    @string.Join(", ", item.Products.Select(p => p.Name))
                </td>
                <td>
                    <a asp-action="Edit" asp-route-id="@item.Id" class="btn btn-sm btn-primary">Edit</a>
                    <a asp-action="Details" asp-route-id="@item.Id" class="btn btn-sm btn-info">Details</a>
                    <a asp-action="Delete" asp-route-id="@item.Id" class="btn btn-sm btn-danger">Delete</a>
                </td>
            </tr>
        }
    </tbody>
</table>

<nav aria-label="Page navigation" class="mt-3">
    <ul class="pagination justify-content-center">

        @* First Page *@
        @if (currentPage > 1)
        {
            <li class="page-item">
                <a class="page-link" href="@Url.Action("Index", new { page = 1 })">First</a>
            </li>
        }

        @* Previous Page *@
        @if (currentPage > 1)
        {
            <li class="page-item">
                <a class="page-link" href="@Url.Action("Index", new { page = currentPage - 1 })">Previous</a>
            </li>
        }

        @{
            int startPage = Math.Max(1, currentPage - 2);
            int endPage = Math.Min(totalPages, currentPage + 2);
        }

        @for (int i = startPage; i <= endPage; i++)
        {
            <li class="page-item @(i == currentPage ? "active" : "")">
                <a class="page-link" href="@Url.Action("Index", new { page = i })">@i</a>
            </li>
        }

        @* Next Page *@
        @if (currentPage < totalPages)
        {
            <li class="page-item">
                <a class="page-link" href="@Url.Action("Index", new { page = currentPage + 1 })">Next</a>
            </li>
        }

        @* Last Page *@
        @if (currentPage < totalPages)
        {
            <li class="page-item">
                <a class="page-link" href="@Url.Action("Index", new { page = totalPages })">Last</a>
            </li>
        }
    </ul>
</nav>


