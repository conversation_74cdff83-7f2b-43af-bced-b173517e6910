﻿@model IEnumerable<POS.Core.Models.User>

<h2>User List</h2>

<p>
    <a asp-action="Create">Create New User</a>
</p>

<table class="table">
    <thead>
        <tr>
            <th>User Type</th>
            <th>User ID</th>
            <th>Name</th>
            <th>Password</th>
            <th>Actions</th>
        </tr>
    </thead>
    <tbody>
        @foreach (var user in Model)
        {
            <tr>
                <td>@user.GetType().Name</td>
                <td>@user.UserId</td>
                <td>@user.Name</td>
                <td>@user.Password</td>
                <td>
                    <a asp-action="Edit" asp-route-id="@user.Id">Edit</a> |
                    <a asp-action="Delete" asp-route-id="@user.Id">Delete</a>
                </td>
            </tr>
        }
    </tbody>
</table>