﻿@{
    ViewData["Title"] = "Upload Promotions File";
}

<h2>Upload Promotions File</h2>

@if (ViewData["Message"] != null)
{
    <div class="alert @(ViewData["Message"].ToString().Contains("Error") ? "alert-danger" : "alert-success") mt-3">
        @ViewData["Message"]
    </div>
}

<form asp-controller="BudgensPromotions" asp-action="UploadFile" method="post" enctype="multipart/form-data">
    <div class="mb-3">
        <input type="file" name="file" class="form-control" required />
    </div>
    <button type="submit" class="btn btn-primary">Upload</button>
</form>
