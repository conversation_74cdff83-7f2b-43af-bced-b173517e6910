﻿@model POS.Core.Models.Store

@{
    ViewData["Title"] = "Create Store";
}

<h1>Create Store</h1>

<form asp-action="Create">
    <div class="form-group">
        <label asp-for="StoreName" class="control-label"></label>
        <input asp-for="StoreName" class="form-control" />
        <span asp-validation-for="StoreName" class="text-danger"></span>
    </div>
    
    <div class="form-group">
        <label asp-for="BrandId" class="control-label">Brand</label>
        <select asp-for="BrandId" class="form-control" asp-items="@(new SelectList(ViewBag.Brands, "Id", "Name"))">
            <option value="">-- Select Brand --</option>
        </select>
        <span asp-validation-for="BrandId" class="text-danger"></span>
    </div>

    <div class="form-group">
        <label asp-for="CompanyId" class="control-label">Brand</label>
        <select asp-for="CompanyId" class="form-control" asp-items="@(new SelectList(ViewBag.Companies, "Id", "Name"))">
            <option value="">-- Company --</option>
        </select>
        <span asp-validation-for="CompanyId" class="text-danger"></span>
    </div>

    <div class="form-group">
        <label asp-for="StoreIdForAPI" class="control-label">API Store ID</label>
        <input asp-for="StoreIdForAPI" class="form-control" />
        <span asp-validation-for="StoreIdForAPI" class="text-danger"></span>
    </div>



    <div class="form-group">
        <label asp-for="AllowNegativeInventory"></label>
        <input type="checkbox" asp-for="AllowNegativeInventory" />
        <span asp-validation-for="AllowNegativeInventory"></span>
    </div>

    <button type="submit" class="btn btn-primary">Create</button>
    <a asp-action="Index" class="btn btn-secondary">Cancel</a>
</form>
