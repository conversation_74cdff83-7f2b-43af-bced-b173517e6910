﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace TimeManagement
{
    public class CustomTimeProvider
    {
        private static DateTime? _customTime;
        private static bool _useCustomTime = false;

        public static DateTime Now
        {
            get
            {
                if (_useCustomTime && _customTime.HasValue)
                {
                    return _customTime.Value;
                }
                return DateTime.UtcNow;
            }
        }

        public static void SetCustomTime(DateTime time)
        {
            _customTime = time;
            _useCustomTime = true;
        }

        public static void ResetToUtc()
        {
            _useCustomTime = false;
            _customTime = null;
        }
    }
}
