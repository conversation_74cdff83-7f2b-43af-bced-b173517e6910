﻿using RefactoredPrinter.Interfaces;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace RefactoredPrinter
{
    internal class ZeroVatStrategy : IVatStrategy
    {
        public string Code => throw new NotImplementedException();

        public decimal CalculateVat(decimal amount)
        {
            throw new NotImplementedException();
        }
    }
}
