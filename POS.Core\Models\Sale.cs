namespace POS.Core.Models
{
    public class Sale : BaseEntity
    {
        public int SaleId { get; set; }
        public DateTime Date { get; set; }
        public decimal TotalValue { get; set; }
        public decimal Cashback { get; set; }
        public bool IsOnHold { get; set; }
        public ICollection<ProductStoreSale> ProductStoreSales { get; set; }
        public ICollection<SpecialProductSale> SpecialProductSales { get; set; }

        // Navigation property for many-to-many
        public ICollection<SalePaymentMethod2> SalePaymentMethods { get; set; }

        public decimal? CashTendered { get; set; }
        public string? Barcode { get; set; }
        public Shift Shift { get; set; }
        public int ShiftId { get; set; }

       
      
    }

  




}
