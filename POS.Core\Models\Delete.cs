using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace POS.Core.Models
{
    public class DeletedSaleItem : BaseEntity
    {
        public int Id { get; set; }

        public string PLU { get; set; }

        public int Qty { get; set; }

        public decimal Total { get; set; }

        public int StoreId { get; set; }

        public int PosId { get; set; }

        public DateTime DeletedTime { get; set; }
       
      


    }

    public class Void : BaseEntity
    {
        public int Id { get; set; }

        public int StoreId { get; set; }

        public int PosId { get; set; }

        public DateTime VoidTime { get; set; }

        public ICollection<VoidSaleItem> VoidSaleItems { get; set; } = new List<VoidSaleItem>(); // Collection of voided sale items>
       
      
    }


    public class VoidSaleItem : BaseEntity
    {
        public int Id { get; set; }
        public string PLU { get; set; }

        public int Qty { get; set; }

        public decimal Total { get; set; }
       
      
    }
   

}
