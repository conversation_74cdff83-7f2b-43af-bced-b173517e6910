using System;

namespace POS.Core.Models
{
    public class IdMapping : BaseEntity
    {
        public string EntityType { get; set; }  // e.g., "Sale", "Product"
        public int LocalId { get; set; }        // ID in local SQLite database
        public int RemoteId { get; set; }       // ID in remote SQL Server database
        public DateTime MappedAt { get; set; }  // When the mapping was created
    }
} 