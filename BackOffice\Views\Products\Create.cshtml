﻿@model POS.Core.Models.Product

@{
    ViewData["Title"] = "Create Product";
}

<h1>Create Product</h1>

<form asp-action="Create">
    <div class="form-group">
        <label asp-for="Name" class="control-label"></label>
        <input asp-for="Name" class="form-control" />
    </div>
  
    <div class="form-group">
        <label asp-for="PLU" class="control-label"></label>
        <input asp-for="PLU" class="control-label" />

    </div>
    <div class="form-group">
        <label asp-for="Barcode" class="control-label"></label>
        <input asp-for="Barcode" class="form-control" />
    </div>
    <div class="form-group">
        <label asp-for="Description" class="control-label"></label>
        <textarea asp-for="Description" class="form-control"></textarea>
    </div>
    <div class="form-group">
        <label asp-for="PurchasePrice" class="control-label"></label>
        <input asp-for="PurchasePrice" class="form-control" />
    </div>
    <div class="form-group">
        <label asp-for="SellingPrice" class="control-label"></label>
        <input asp-for="SellingPrice" class="form-control" />
    </div>
    <div class="form-group">
        <label asp-for="PurchasePackSize" class="control-label"></label>
        <input asp-for="PurchasePackSize" class="form-control" />
    </div>
    <div class="form-group">
        <label class="control-label">Division</label>
        <select asp-for="DivisionId" class="form-control" asp-items="ViewBag.DivisionId"></select>
    </div>
    <div class="form-group">
        <label  class="control-label">Brand</label>
        <select asp-for="BrandId" class="form-control" asp-items="ViewBag.BrandId"></select>
    </div>
    <div class="form-group">
        <label  class="control-label">Vat</label>
        <select asp-for="VatId" class="form-control" asp-items="ViewBag.VatId"></select>
    </div>

    <button type="submit" class="btn btn-primary">Create</button>
</form>
