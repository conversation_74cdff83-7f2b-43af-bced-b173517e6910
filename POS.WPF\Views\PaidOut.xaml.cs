﻿using Microsoft.EntityFrameworkCore.Metadata.Internal;
using POS.Printer;
using POS.WPF.Helpers;
using POS.WPF.ViewModels;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Windows;
using System.Windows.Controls;
using System.Windows.Data;
using System.Windows.Documents;
using System.Windows.Input;
using System.Windows.Media;
using System.Windows.Media.Imaging;
using System.Windows.Shapes;
using TimeManagement;
using WindowsInput;
using WindowsInput.Native;
using System.Text.Json;
using POS.Core.Models;

namespace POS.WPF.Views
{
    /// <summary>
    /// Interaction logic for PaidOut.xaml
    /// </summary>
    /// 
    public partial class PaidOut : Window
    {
        private InputSimulator _inputSimulator;

        public PaidOut()
        {
            InitializeComponent();
            DataContext = new PaidOutOptionViewModel(); // Set the DataContext here
            _inputSimulator = new InputSimulator();
            Helpers.WindowStyleHelper.SetWindowStyle(this);
        }

        private async void SubmitButton_Click(object sender, RoutedEventArgs e)
        {
            var viewModel = DataContext as PaidOutOptionViewModel;
            if (viewModel.SelectedPaidOutOption != null && !string.IsNullOrEmpty(viewModel.Amount))
            {
                MessageBox.Show($"Selected Option: {viewModel.SelectedPaidOutOption.Option}, Amount: {viewModel.Amount}");

                // Offload database operations to a background task to keep UI responsive
                await Task.Run(async () =>
                {
                    using (POS.WPF.LocalData.LocalContext context = new POS.WPF.LocalData.LocalContext())
                    {
                        POS.Core.Models.PaidOut paidOut = new POS.Core.Models.PaidOut()
                        {
                            PaidOutOptionId = viewModel.SelectedPaidOutOption.Id,
                            Amount = decimal.Parse(viewModel.Amount),
                            ShiftId = (int)UiShift.ShiftId,
                            StoreId = (int)UiShift.StoreId,
                            Time = CustomTimeProvider.Now,
                            IsDeleted = false,
                            LastModified = CustomTimeProvider.Now
                        };

                        // Save to local database
                        context.PaidOuts.Add(paidOut);
                        await context.SaveChangesAsync();

                        // Create PendingSyncItem for syncing
                        var pendingSyncItem = new PendingSyncItem
                        {
                            EntityType = "PaidOut",
                            EntityId = paidOut.Id,
                            CreatedAt = CustomTimeProvider.Now,
                            IsSynced = false,
                            OperationType = "Insert",
                            SyncError = ""
                        };

                        // Add to PendingSyncItem for syncing
                        context.PendingSyncItems.Add(pendingSyncItem);
                        await context.SaveChangesAsync();

                        // Ensure that the printing operation is executed on the UI thread
                        Application.Current.Dispatcher.Invoke(() =>
                        {
                            PaidOutPrinter.Print(UiShift.StoreId.ToString(), viewModel.SelectedPaidOutOption.Option, paidOut.Time.ToString(), paidOut.Amount.ToString(),
                                UiShift.CashierName, paidOut.Id.ToString(), ConfigurationHelper.PrinterName);
                        });

                        // Close the form after the operation is complete
                        Application.Current.Dispatcher.Invoke(() => this.Close());
                    }
                });
            }
            else
            {
                MessageBox.Show("Please select an option and enter an amount.");
            }
        }


        private void NumericKeypad_KeyPressed(object sender, string key)
        {
            txtAmount.Focus();

            switch (key)
            {
                case "Backspace":
                    _inputSimulator.Keyboard.KeyPress(VirtualKeyCode.BACK);
                    break;
                case "%":
                    _inputSimulator.Keyboard.KeyPress(VirtualKeyCode.SHIFT, VirtualKeyCode.VK_5);
                    break;
                case "00":
                    _inputSimulator.Keyboard.KeyPress(VirtualKeyCode.VK_0);
                    _inputSimulator.Keyboard.KeyPress(VirtualKeyCode.VK_0);
                    break;
                case ".":
                    _inputSimulator.Keyboard.KeyPress(VirtualKeyCode.DECIMAL);
                    break;
                default:
                    if (int.TryParse(key, out var number))
                    {
                        var keyCode = (VirtualKeyCode)Enum.Parse(
                            typeof(VirtualKeyCode),
                            $"VK_{number}"
                        );
                        _inputSimulator.Keyboard.KeyPress(keyCode);
                    }
                    break;
            }
        }

        private void Button_Click(object sender, RoutedEventArgs e)
        {
            this.Close();
        }
    }
}