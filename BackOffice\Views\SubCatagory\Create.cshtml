﻿@model POS.Core.Models.SubCatagory

@{
    ViewData["Title"] = "Create Sub Category";
}

<h1>Create Sub Category</h1>

<form asp-action="Create">
    <div class="form-group">
        <label asp-for="Name" class="control-label"></label>
        <input asp-for="Name" class="form-control" />
        <span asp-validation-for="Name" class="text-danger"></span>
    </div>
    <div class="form-group">
        <label asp-for="Catagory" class="control-label"></label>
        <select asp-for="Catagory" class="form-control">
            @foreach (var catagory in Enum.GetValues(typeof(POS.Core.Models.Catagory)))
            {
                <option value="@catagory">@catagory</option>
            }
        </select>
    </div>
    <div class="form-group">
        <label asp-for="StockType" class="control-label"></label>
        <select asp-for="StockType" class="form-control">
            @foreach (var stockType in Enum.GetValues(typeof(POS.Core.Models.StockType)))
            {
                <option value="@stockType">@stockType</option>
            }
        </select>
    </div>
    
    <div class="form-group">
        <label asp-for="CashierAlert" class="control-label"></label>
        <input asp-for="CashierAlert" class="form-control" />
        <span asp-validation-for="CashierAlert" class="text-danger"></span>
    </div>
    <div class="form-group">
        <input type="submit" value="Create" class="btn btn-primary" />
    </div>
</form>
<p>
    <a asp-action="Index">Back to List</a>
</p>
