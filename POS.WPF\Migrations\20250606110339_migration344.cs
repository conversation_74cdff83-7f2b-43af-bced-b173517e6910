﻿using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace POS.WPF.Migrations
{
    /// <inheritdoc />
    public partial class migration344 : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropTable(
                name: "StoreSelectedProducts");

            migrationBuilder.CreateTable(
                name: "QuickAccessButtons",
                columns: table => new
                {
                    Id = table.Column<int>(type: "INTEGER", nullable: false)
                        .Annotation("Sqlite:Autoincrement", true),
                    ButtonText = table.Column<string>(type: "TEXT", nullable: false),
                    ProductId = table.Column<int>(type: "INTEGER", nullable: false),
                    StoreId = table.Column<int>(type: "INTEGER", nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_QuickAccessButtons", x => x.Id);
                    table.ForeignKey(
                        name: "FK_QuickAccessButtons_Products_ProductId",
                        column: x => x.ProductId,
                        principalTable: "Products",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Cascade);
                    table.ForeignKey(
                        name: "FK_QuickAccessButtons_Stores_StoreId",
                        column: x => x.StoreId,
                        principalTable: "Stores",
                        principalColumn: "StoreId",
                        onDelete: ReferentialAction.Cascade);
                });

            migrationBuilder.CreateIndex(
                name: "IX_QuickAccessButtons_ProductId",
                table: "QuickAccessButtons",
                column: "ProductId");

            migrationBuilder.CreateIndex(
                name: "IX_QuickAccessButtons_StoreId",
                table: "QuickAccessButtons",
                column: "StoreId");
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropTable(
                name: "QuickAccessButtons");

            migrationBuilder.CreateTable(
                name: "StoreSelectedProducts",
                columns: table => new
                {
                    Id = table.Column<int>(type: "INTEGER", nullable: false)
                        .Annotation("Sqlite:Autoincrement", true),
                    ProductId = table.Column<int>(type: "INTEGER", nullable: false),
                    StoreId = table.Column<int>(type: "INTEGER", nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_StoreSelectedProducts", x => x.Id);
                });
        }
    }
}
