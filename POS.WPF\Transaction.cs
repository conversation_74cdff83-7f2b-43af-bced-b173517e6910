﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using TimeManagement;

namespace POS.WPF
{
    public class Transaction
    {
        public List<TransactionItem> Items { get; set; } = new List<TransactionItem>();
        public DateTime Date { get; set; } = CustomTimeProvider.Now;
        public decimal GrandTotal => Items.Sum(item => item.Total);
    }
}
