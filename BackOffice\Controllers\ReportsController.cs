﻿using POS.BackOffice.ViewModels;
using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using POS.Core.Models;
using System.Globalization;
using Microsoft.AspNetCore.Authorization;
using TimeManagement;
using System.Security.Claims;


namespace BackOffice.Controllers
{
    [Authorize(Roles = "BackOffice,Admin,Accountant")]
    public class ReportsController : Controller
    {
        private readonly Data _context;

        public ReportsController(Data context)
        {
            _context = context;
        }

        private int? GetCurrentUserStoreId()
        {
            if (User.IsInRole("BackOffice"))
            {
                var userId = int.Parse(User.Claims.FirstOrDefault(c => c.Type == ClaimTypes.NameIdentifier)?.Value);
                return _context.BackOffices.Find(userId)?.StoreId;
            }
            return null;
        }

        public IActionResult DailySalesReport()
        {
            return View();
        }

        [HttpPost]
        public async Task<IActionResult> DailySalesReport(DateTime startDate, DateTime endDate)
        {
            var storeId = GetCurrentUserStoreId();
            
            // Regular product sales query
            var regularSalesQuery = _context.ProductStoreSales
                .Include(p => p.ProductStore)
                .ThenInclude(ps => ps.Product)
                .ThenInclude(p => p.Division)
                .ThenInclude(d => d.SubCatagory)
                .Include(p => p.Sale)
                .Where(pss => pss.Sale.Date >= startDate && pss.Sale.Date <= endDate);

            // Special product sales query
            var specialSalesQuery = _context.SpecialProductSales
                .Include(s => s.Product)
                .ThenInclude(p => p.Division)
                .ThenInclude(d => d.SubCatagory)
                .Include(s => s.Sale)
                .Where(sps => sps.Sale.Date >= startDate && sps.Sale.Date <= endDate);

            if (storeId.HasValue)
            {
                regularSalesQuery = regularSalesQuery.Where(pss => pss.ProductStore.StoreId == storeId.Value);
            }

            if (startDate > endDate)
            {
                ModelState.AddModelError("", "Start date must be earlier than end date.");
                return View();
            }

            // Generate date range
            var dates = Enumerable.Range(0, (endDate - startDate).Days + 1)
                                   .Select(offset => startDate.AddDays(offset))
                                   .ToList();

            // Fetch regular sales grouped by day and category
            var regularSalesData = await regularSalesQuery
                .GroupBy(pss => new { pss.Sale.Date.Date, pss.ProductStore.Product.Division.SubCatagory.Catagory })
                .Select(group => new
                {
                    Date = group.Key.Date,
                    Category = group.Key.Catagory,
                    TotalSales = group.Sum(g => g.Total)
                })
                .ToListAsync();

            // Fetch special sales grouped by day and category
            var specialSalesData = await specialSalesQuery
                .GroupBy(sps => new { sps.Sale.Date.Date, sps.Product.Division.SubCatagory.Catagory })
                .Select(group => new
                {
                    Date = group.Key.Date,
                    Category = group.Key.Catagory,
                    TotalSales = group.Sum(g => g.Amount)
                })
                .ToListAsync();

            // Combine regular and special sales data
            var combinedSalesData = regularSalesData
                .Concat(specialSalesData)
                .GroupBy(s => new { s.Date, s.Category })
                .Select(group => new
                {
                    Date = group.Key.Date,
                    Category = group.Key.Category,
                    TotalSales = group.Sum(s => s.TotalSales)
                })
                .ToList();

            // Create category list
            var categories = Enum.GetValues(typeof(Catagory)).Cast<Catagory>().ToList();

            // Prepare the report table
            var report = categories.Select(category => new
            {
                Category = category,
                Sales = dates.Select(date => combinedSalesData
                            .Where(s => s.Date == date && s.Category == category)
                            .Sum(s => s.TotalSales)).ToList()
            });

            ViewBag.Dates = dates;
            ViewBag.Report = report;

            return View();
        }




        public async Task<IActionResult> MonthlySaleReport()
        {
            try
            {
                var storeId = GetCurrentUserStoreId();
                var last12Months = Enumerable.Range(0, 12)
                                             .Select(i => CustomTimeProvider.Now.AddMonths(-i))
                                             .OrderBy(date => date)
                                             .Select(date => date.ToString("yyyy-MM", CultureInfo.InvariantCulture))
                                             .ToList();

                // Regular product sales query
                var regularSalesQuery = _context.ProductStoreSales
                    .Include(pss => pss.ProductStore)
                    .ThenInclude(ps => ps.Product)
                    .ThenInclude(ps => ps.Division)
                    .ThenInclude(p => p.SubCatagory)
                    .Include(pss => pss.Sale)
                    .Where(pss => pss.Sale.Date >= CustomTimeProvider.Now.AddYears(-1));

                // Special product sales query
                var specialSalesQuery = _context.SpecialProductSales
                    .Include(sps => sps.Product)
                    .ThenInclude(p => p.Division)
                    .ThenInclude(d => d.SubCatagory)
                    .Include(sps => sps.Sale)
                    .Where(sps => sps.Sale.Date >= CustomTimeProvider.Now.AddYears(-1));

                // Apply store filter for BackOffice users
                if (storeId.HasValue)
                {
                    regularSalesQuery = regularSalesQuery.Where(pss => pss.ProductStore.StoreId == storeId.Value);
                }

                // Fetch regular sales data
                var regularSalesData = await regularSalesQuery
                    .GroupBy(pss => new
                    {
                        SubCategory = pss.ProductStore.Product.Division.SubCatagory.Name,
                        Year = pss.Sale.Date.Year,
                        Month = pss.Sale.Date.Month
                    })
                    .Select(g => new
                    {
                        g.Key.SubCategory,
                        Year = g.Key.Year,
                        Month = g.Key.Month,
                        TotalSales = g.Sum(pss => pss.Total)
                    })
                    .ToListAsync();

                // Fetch special sales data
                var specialSalesData = await specialSalesQuery
                    .GroupBy(sps => new
                    {
                        SubCategory = sps.Product.Division.SubCatagory.Name,
                        Year = sps.Sale.Date.Year,
                        Month = sps.Sale.Date.Month
                    })
                    .Select(g => new
                    {
                        g.Key.SubCategory,
                        Year = g.Key.Year,
                        Month = g.Key.Month,
                        TotalSales = g.Sum(sps => sps.Amount)
                    })
                    .ToListAsync();

                // Combine regular and special sales data
                var combinedSalesData = regularSalesData
                    .Concat(specialSalesData)
                    .GroupBy(s => new { s.SubCategory, s.Year, s.Month })
                    .Select(g => new
                    {
                        g.Key.SubCategory,
                        Year = g.Key.Year,
                        Month = g.Key.Month,
                        TotalSales = g.Sum(s => s.TotalSales)
                    })
                    .ToList();

                // Format the date on the client side
                var formattedSalesData = combinedSalesData.Select(s => new
                {
                    s.SubCategory,
                    Month = new DateTime(s.Year, s.Month, 1).ToString("yyyy-MM", CultureInfo.InvariantCulture),
                    s.TotalSales
                }).ToList();

                var subCategories = formattedSalesData
                    .Select(s => s.SubCategory)
                    .Distinct()
                    .OrderBy(name => name)
                    .ToList();

                var report = subCategories.Select(subCategory => new MonthlySaleReportViewModel
                {
                    SubCategory = subCategory,
                    MonthlySales = last12Months.ToDictionary(
                        month => month,
                        month => formattedSalesData.FirstOrDefault(s => s.SubCategory == subCategory && s.Month == month)?.TotalSales ?? 0
                    )
                }).ToList();

                ViewBag.Last12Months = last12Months; // For headers in the view
                return View(report);
            }
            catch(Exception ex)
            {
                return null;
            }
        }




        public IActionResult MonthlySalesReportExtended()
        {
            return View();
        }

        [HttpPost]
        public async Task<IActionResult> MonthlySalesReportExtended(DateTime startDate, DateTime endDate)
        {
            var storeId = GetCurrentUserStoreId();
            
            var query = _context.ProductStoreSales
                .Include(pss => pss.ProductStore)
                    .ThenInclude(ps => ps.Product)
                    .ThenInclude(ps => ps.Division)
                        .ThenInclude(p => p.SubCatagory)
                .Include(pss => pss.Sale)
                .Where(pss => pss.Sale.Date >= startDate && pss.Sale.Date <= endDate);

            if (storeId.HasValue)
            {
                query = query.Where(pss => pss.ProductStore.StoreId == storeId.Value); // Fixed: Changed from Sale.StoreId
            }

            if (startDate > endDate)
            {
                ModelState.AddModelError("", "Start date must be earlier than end date.");
                return View();
            }

            // Generate the months in the date range
            var months = Enumerable.Range(0, ((endDate.Year - startDate.Year) * 12) + endDate.Month - startDate.Month + 1)
                                    .Select(offset => new DateTime(startDate.Year, startDate.Month, 1).AddMonths(offset))
                                    .ToList();

            // Fetch sales grouped by month, category, and product
            var salesData = await query
                .GroupBy(pss => new
                {
                    Month = new DateTime(pss.Sale.Date.Year, pss.Sale.Date.Month, 1),
                    Category = pss.ProductStore.Product.Division.SubCatagory.Catagory,
                    ProductName = pss.ProductStore.Product.Name
                })
                .Select(group => new
                {
                    group.Key.Month,
                    group.Key.Category,
                    group.Key.ProductName,
                    TotalSales = group.Sum(g => g.Total)
                })
                .ToListAsync();

            // Group data by category and product for report structure
            var categories = salesData
                .GroupBy(sd => sd.Category)
                .Select(categoryGroup => new
                {
                    Category = categoryGroup.Key,
                    Products = categoryGroup.GroupBy(p => p.ProductName)
                                            .Select(productGroup => new
                                            {
                                                ProductName = productGroup.Key,
                                                MonthlySales = months.Select(month => productGroup
                                                        .Where(s => s.Month == month)
                                                        .Sum(s => s.TotalSales)).ToList()
                                            }).ToList()
                });

            ViewBag.Months = months;
            ViewBag.Report = categories;

            return View();
        }





        public IActionResult DailySalesReportExtended()
        {
            return View();
        }

        [HttpPost]
        public async Task<IActionResult> DailySalesReportExtended(DateTime startDate, DateTime endDate)
        {
            var storeId = GetCurrentUserStoreId();
            
            var query = _context.ProductStoreSales
                .Include(pss => pss.ProductStore)
                    .ThenInclude(ps => ps.Product)
                    .ThenInclude(ps => ps.Division)
                        .ThenInclude(p => p.SubCatagory)
                .Include(pss => pss.Sale)
                .Where(pss => pss.Sale.Date >= startDate && pss.Sale.Date <= endDate);

            if (storeId.HasValue)
            {
                query = query.Where(pss => pss.ProductStore.StoreId == storeId.Value); // Fixed: Changed from Sale.StoreId
            }

            if (startDate > endDate)
            {
                ModelState.AddModelError("", "Start date must be earlier than end date.");
                return View();
            }

            // Generate the days in the date range
            var days = Enumerable.Range(0, (endDate - startDate).Days + 1)
                                  .Select(offset => startDate.AddDays(offset))
                                  .ToList();

            // Fetch sales grouped by day, category, and product
            var salesData = await query
                .GroupBy(pss => new
                {
                    Date = pss.Sale.Date.Date,
                    Category = pss.ProductStore.Product.Division.SubCatagory.Catagory,
                    ProductName = pss.ProductStore.Product.Name
                })
                .Select(group => new
                {
                    group.Key.Date,
                    group.Key.Category,
                    group.Key.ProductName,
                    TotalSales = group.Sum(g => g.Total)
                })
                .ToListAsync();

            // Group data by category and product for report structure
            var categories = salesData
                .GroupBy(sd => sd.Category)
                .Select(categoryGroup => new
                {
                    Category = categoryGroup.Key,
                    Products = categoryGroup.GroupBy(p => p.ProductName)
                                            .Select(productGroup => new
                                            {
                                                ProductName = productGroup.Key,
                                                DailySales = days.Select(day => productGroup
                                                        .Where(s => s.Date == day)
                                                        .Sum(s => s.TotalSales)).ToList()
                                            }).ToList()
                });

            ViewBag.Days = days;
            ViewBag.Report = categories;

            return View();
        }



        [HttpGet]
        public async Task<IActionResult> MonthlyProductQuantityReport()
        {
            var currentDate = CustomTimeProvider.Now;
            var startDate = new DateTime(currentDate.Year, currentDate.Month, 1).AddMonths(-11); // Start of 12 months ago
            var months = Enumerable.Range(0, 12)
                                    .Select(offset => startDate.AddMonths(offset))
                                    .ToList();

            // Fetch quantity grouped by month, subcategory, and product
            var salesData = await _context.ProductStoreSales
                .Include(pss => pss.ProductStore)
                    .ThenInclude(ps => ps.Product)
                    .ThenInclude(ps => ps.Division)
                        .ThenInclude(p => p.SubCatagory)
                .Include(pss => pss.Sale)
                .Where(pss => pss.Sale.Date >= startDate)
                .GroupBy(pss => new
                {
                    Month = new DateTime(pss.Sale.Date.Year, pss.Sale.Date.Month, 1),
                    SubCategory = pss.ProductStore.Product.Division.SubCatagory.Name,
                    ProductName = pss.ProductStore.Product.Name
                })
                .Select(group => new
                {
                    group.Key.Month,
                    group.Key.SubCategory,
                    group.Key.ProductName,
                    Quantity = group.Sum(g => g.Quantity)
                })
                .ToListAsync();

            // Group data by subcategory and product
            var subCategories = salesData
                .GroupBy(sd => sd.SubCategory)
                .Select(subCategoryGroup => new
                {
                    SubCategory = subCategoryGroup.Key,
                    Products = subCategoryGroup.GroupBy(p => p.ProductName)
                                               .Select(productGroup => new
                                               {
                                                   ProductName = productGroup.Key,
                                                   MonthlyQuantities = months.Select(month => productGroup
                                                        .Where(s => s.Month == month)
                                                        .Sum(s => s.Quantity)).ToList()
                                               }).ToList()
                });

            // Calculate totals
            var totalQuantities = months.Select(month => salesData
                                        .Where(s => s.Month == month)
                                        .Sum(s => s.Quantity)).ToList();

            ViewBag.Months = months;
            ViewBag.Report = subCategories;
            ViewBag.TotalQuantities = totalQuantities;

            return View();
        }


    }
}
