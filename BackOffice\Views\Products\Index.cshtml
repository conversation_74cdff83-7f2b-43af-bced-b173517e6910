﻿@model IEnumerable<POS.Core.Models.Product>

@{
    ViewData["Title"] = "Products";
}

<h1>Products</h1>

<p>
    <a asp-action="Create" class="btn btn-primary">Create New</a>
</p>

<table class="table table-striped">
    <thead>
        <tr>
            <th>Name</th>
            <th>PLU</th>
            <th>Barcode</th>
            <th>Brand</th>
            <th>Division</th>
            <th>Vat<th>

            <th></th>
        </tr>
    </thead>
    <tbody>
        @foreach (var item in Model)
        {
            if(item.SpecialProduct == true) continue;
            <tr>
                <td>@item.Name</td>
                <td>@item.PLU</td>
                <td>@item.Barcode</td>
                <td>@item.Brand?.Name</td>
                <td>@item.Division.Name</td>
                <td>@item.Vat.Rate</td>

                <td>
                    <a asp-action="Edit" asp-route-id="@item.Id" class="btn btn-sm btn-warning">Edit</a> |
                    <a asp-action="Delete" asp-route-id="@item.Id" class="btn btn-sm btn-danger">Delete</a>
                </td>
            </tr>
        }
    </tbody>
</table>
