﻿using Microsoft.EntityFrameworkCore.Metadata.Internal;
using POS.Core.Models;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Security.Cryptography.Xml;
using System.Text;
using System.Threading.Tasks;
using System.Windows;
using System.Windows.Controls;
using System.Windows.Data;
using System.Windows.Documents;
using System.Windows.Input;
using System.Windows.Media;
using System.Windows.Media.Imaging;
using System.Windows.Shapes;
using WindowsInput;
using WindowsInput.Native;

namespace POS.WPF.Views
{
    /// <summary>
    /// Interaction logic for PaymentWindow.xaml
    /// </summary>
    public partial class PaymentWindow : Window
    {
        public List<PaymentMethod> SelectedPaymentMethods { get; private set; }
        public decimal PaidAmount { get; private set; }
        public bool IsPaymentConfirmed { get; private set; }
        private decimal ExactAmount { get; set; }  // Added property for the exact amount passed to the constructor
        private InputSimulator _inputSimulator;

        public PaymentWindow(List<PaymentMethod> availablePaymentMethods, decimal? exactAmount = null)
        {
            InitializeComponent();
            SelectedPaymentMethods = new List<PaymentMethod>();
            PaymentMethodsListView.ItemsSource = availablePaymentMethods;
            _inputSimulator = new InputSimulator();
            Helpers.WindowStyleHelper.SetWindowStyle(this);

            // Set the exact amount if passed
            if (exactAmount.HasValue)
            {
                ExactAmount = exactAmount.Value;
            }
            
            // Add event handler for text changes
            txtPaidAmount.TextChanged += txtPaidAmount_TextChanged;
        }

        // Click handler for the Exact Amount button
        private void ExactAmountButton_Click(object sender, RoutedEventArgs e)
        {
            txtPaidAmount.Text = ExactAmount.ToString("F2");  // Display exact amount in the TextBox
            // The TextChanged event will handle enabling/disabling the payment methods list
        }

        // Click handler for preset amount buttons (£5, £10, £50, £100)
        private void PresetAmountButton_Click(object sender, RoutedEventArgs e)
        {
            var button = sender as Button;
            decimal amount = 0;

            switch (button.Content.ToString())
            {
                case "£5":
                    amount = 5;
                    break;
                case "£10":
                    amount = 10;
                    break;
                case "£50":
                    amount = 50;
                    break;
                case "£100":
                    amount = 100;
                    break;
            }

            txtPaidAmount.Text = amount.ToString("F2");
            // The TextChanged event will handle enabling/disabling the payment methods list
        }

        private void OkButton_Click(object sender, RoutedEventArgs e)
        {
            // Validate the paid amount.
            if (!decimal.TryParse(txtPaidAmount.Text, out decimal amount) && PaymentMethodsListView.SelectedItems.Count == 0)
            {
                MessageBox.Show("Please enter a valid amount.", "Validation Error", MessageBoxButton.OK, MessageBoxImage.Warning);
                return;
            }

            if(PaymentMethodsListView.SelectedItems.Count == 0 && amount < ExactAmount)
            {
                MessageBox.Show("Select another payment method (cash < amoung)");
                return;
            }


            if(amount == 0 && PaymentMethodsListView.SelectedItems.Count == 0)
            {
                MessageBox.Show("Select a payment method.");

                return;
            }
            
            PaidAmount = amount;
            SelectedPaymentMethods = PaymentMethodsListView.SelectedItems.Cast<PaymentMethod>().ToList();
            IsPaymentConfirmed = true;
            this.DialogResult = true;
            Close();
        }

        private void CancelButton_Click(object sender, RoutedEventArgs e)
        {
            IsPaymentConfirmed = false;
            this.DialogResult = false;
            Close();
        }

        private void NumericKeypad_KeyPressed(object sender, string key)
        {
            txtPaidAmount.Focus();

            switch (key)
            {
                case "Backspace":
                    _inputSimulator.Keyboard.KeyPress(VirtualKeyCode.BACK);
                    break;
                case "%":
                    _inputSimulator.Keyboard.KeyPress(VirtualKeyCode.SHIFT, VirtualKeyCode.VK_5);
                    break;
                case "00":
                    _inputSimulator.Keyboard.KeyPress(VirtualKeyCode.VK_0);
                    _inputSimulator.Keyboard.KeyPress(VirtualKeyCode.VK_0);
                    break;
                case ".":
                    _inputSimulator.Keyboard.KeyPress(VirtualKeyCode.DECIMAL);
                    break;
                default:
                    if (int.TryParse(key, out var number))
                    {
                        var keyCode = (VirtualKeyCode)Enum.Parse(
                            typeof(VirtualKeyCode),
                            $"VK_{number}"
                        );
                        _inputSimulator.Keyboard.KeyPress(keyCode);
                    }
                    break;
            }
            
            // The TextChanged event will handle enabling/disabling the payment methods list
        }

        // Add a method to handle text changes in the paid amount textbox
        private void txtPaidAmount_TextChanged(object sender, TextChangedEventArgs e)
        {
            // Try to parse the entered amount
            if (decimal.TryParse(txtPaidAmount.Text, out decimal enteredAmount))
            {
                // If entered amount is equal to or greater than the exact amount, disable payment methods list
                if (enteredAmount >= ExactAmount)
                {
                    PaymentMethodsListView.IsEnabled = false;
                    // Clear any existing selections
                    PaymentMethodsListView.SelectedItems.Clear();
                }
                else
                {
                    // Otherwise, enable payment methods list
                    PaymentMethodsListView.IsEnabled = true;
                }
            }
        }
    }

}

