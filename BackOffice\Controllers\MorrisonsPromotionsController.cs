﻿using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using OfficeOpenXml;
using POS.Core.Models;
using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Globalization;
using System.IO;
using System.Linq;
using System.Transactions;
using System.Threading.Tasks;
using Microsoft.Extensions.Logging;
using BackOffice.Configuration;
using Microsoft.Extensions.Options;
using Microsoft.AspNetCore.SignalR;
using BackOffice.Hubs;

namespace BackOffice.Controllers
{
    [Authorize(Roles = "BackOffice,Admin")]
    public class MorrisonsPromotionsController : Controller
    {
        private readonly MorrisonsPromotionsConfig _config;
        private readonly Data _data;
        private readonly ILogger<MorrisonsPromotionsController> _logger;
        private readonly IHubContext<SyncProgressHub> _hubContext;

        public MorrisonsPromotionsController(
            IOptions<MorrisonsPromotionsConfig> config,
            Data data,
            ILogger<MorrisonsPromotionsController> logger,
            IHubContext<SyncProgressHub> hubContext)
        {
            _config = config.Value;
            _data = data;
            _logger = logger;
            _hubContext = hubContext;
        }

        /// <summary>
        /// Displays the promotion upload interface.
        /// </summary>
        /// <returns>The index view for promotion uploads.</returns>
        public IActionResult Index()
        {
            return View();
        }

        /// <summary>
        /// Handles the upload and processing of an Excel file containing promotion data.
        /// </summary>
        /// <param name="file">The Excel file containing promotion information.</param>
        /// <returns>
        /// - BadRequest if no file is uploaded
        /// - StatusCode 500 if processing fails
        /// - Index view with success message if processing succeeds
        /// </returns>
        [HttpPost]
        public async Task<IActionResult> UploadExcel(IFormFile file)
        {
            if (!ValidateFile(file))
            {
                await _hubContext.Clients.All.SendAsync("SyncError", "Invalid file. Please upload a valid Excel file (max 10MB).");
                return BadRequest("Invalid file. Please upload a valid Excel file (max 10MB).");
            }

            try
            {
                await _hubContext.Clients.All.SendAsync("ReceiveProgress", "Starting promotion processing...", 0, 100);
                
                // Clear existing promotions outside the main processing loop
                await _hubContext.Clients.All.SendAsync("ReceiveProgress", "Clearing existing promotions...", 10, 100);
                ClearExistingMorrisonsPromotions();

                var filePath = SaveFileTemporarily(file);
                await _hubContext.Clients.All.SendAsync("ReceiveProgress", "Reading Excel file...", 20, 100);
                
                // Process in smaller batches instead of one large transaction
                var (promotions, errors) = await ReadExcelFileInBatches(filePath);
                
                //if (errors.Any())
                //{
                //    await _hubContext.Clients.All.SendAsync("SyncError", 
                //        $"Process failed with {errors.Count} errors. First error: {errors.First()}");
                //    return BadRequest(new { message = "Some rows failed to process", errors });
                //}

                System.IO.File.Delete(filePath);

                var result = new
                {
                    TotalPromotions = promotions,
                    ProcessedAt = DateTime.Now
                };

                await _hubContext.Clients.All.SendAsync("SyncComplete", result);
                return Json(result);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error processing promotion file");
                await _hubContext.Clients.All.SendAsync("SyncError", "Internal server error while processing promotions.");
                return StatusCode(500, "Internal server error while processing promotions.");
            }
        }

        private bool ValidateFile(IFormFile file)
        {
            if (file == null || file.Length == 0) return false;
            if (file.Length > _config.MaxFileSize) return false;
            
            var extension = Path.GetExtension(file.FileName).ToLowerInvariant();
            return extension == _config.AllowedFileExtension;
        }

        private string SaveFileTemporarily(IFormFile file)
        {
            var filePath = Path.GetTempFileName();
            using (var stream = new FileStream(filePath, FileMode.Create))
            {
                file.CopyTo(stream);
            }
            return filePath;
        }

        /// <summary>
        /// Removes all existing Morrisons promotions from the database.
        /// Does not affect the associated products.
        /// </summary>
        private void ClearExistingMorrisonsPromotions()
        {
            var existingPromotions = _data.Promotions
                .Include(pr => pr.Products)
                .Where(p => p.Products.Any(p => p.BrandId == 1))
                .ToList();

            _data.Promotions.RemoveRange(existingPromotions);
            _data.SaveChanges();
        }

        /// <summary>
        /// Processes an Excel file and converts its contents into Promotion entities.
        /// </summary>
        /// <param name="filePath">The path to the temporary Excel file.</param>
        /// <returns>A list of processed Promotion entities.</returns>
        /// <remarks>
        /// This method:
        /// - Deletes existing Morrisons promotions (BrandId == 1)
        /// - Processes both single product and multi-product (X for Y) promotions
        /// - Handles multiple date formats
        /// - Automatically links products based on barcode
        /// </remarks>
        private async Task<(int promotions, List<string> errors)> ReadExcelFileInBatches(string filePath)
        {
            var errors = new List<string>();
            string[] formats = { "M/d/yyyy", "MM/dd/yyyy", "d/M/yyyy", "dd/MM/yyyy" };
            int totalPromotions = 0;
            const int batchSize = 50; // Process 50 rows at a time

            ExcelPackage.LicenseContext = OfficeOpenXml.LicenseContext.NonCommercial;

            using (var package = new ExcelPackage(new FileInfo(filePath)))
            {
                var worksheet = package.Workbook.Worksheets[0];
                int rowCount = worksheet.Dimension?.Rows ?? 0;
                
                for (int batchStart = 2; batchStart <= rowCount; batchStart += batchSize)
                {
                    int batchEnd = Math.Min(batchStart + batchSize - 1, rowCount);
                    var batchPromotions = new List<Promotion>();
                    
                    // Process a batch of rows
                    for (int row = batchStart; row <= batchEnd; row++)
                    {
                        try
                        {
                            await _hubContext.Clients.All.SendAsync("ReceiveProgress", 
                                $"Processing row {row} of {rowCount}...", 
                                20 + (60 * row / rowCount), 100);

                            var barcode = worksheet.Cells[row, _config.ExcelColumns.Barcode].Value?.ToString().Trim();
                            if (string.IsNullOrEmpty(barcode))
                            {
                                errors.Add($"Row {row}: Empty barcode");
                                continue;
                            }

                            var product = _data.Products.FirstOrDefault(p => p.Barcode.ToString() == barcode);
                            if (product == null)
                            {
                                errors.Add($"Row {row}: Product with barcode {barcode} not found");
                                continue;
                            }

                            var startTime = ParseDate(worksheet.Cells[row, _config.ExcelColumns.StartDate].Text, formats, row, errors);
                            var endTime = ParseDate(worksheet.Cells[row, _config.ExcelColumns.EndDate].Text, formats, row, errors);

                            if (startTime >= endTime)
                            {
                                errors.Add($"Row {row}: Start date must be before end date");
                                continue;
                            }



                                if (worksheet.Cells[row, _config.ExcelColumns.PromotionName].Text.Trim() == "")
                                {
                                    var promotionPrice = decimal.Parse(worksheet.Cells[row, _config.ExcelColumns.PromotionPrice].Text.Replace("£", "").Trim());


                                    var promotion = new Promotion
                                    {
                                        Name = "",
                                        StartTime = (DateTime)startTime,
                                        EndTime = (DateTime)endTime,
                                        Products = new List<Product> { product },
                                        PromotionPrice = promotionPrice,
                                        Quantity = 0,
                                        Type = PromotionType.SingleProductPromotion,
                                        Identifire = ""
                                    };

                                    batchPromotions.Add(promotion);
                                }
                                else
                                {
                                    string name = worksheet.Cells[row, _config.ExcelColumns.PromotionName].Text.Trim();

                                    var promotionPrice = decimal.Parse(worksheet.Cells[row, _config.ExcelColumns.PromotionPrice].Text.Split(" ").Last().Replace("£", "").Trim());
                                    var quantity = int.Parse(worksheet.Cells[row, _config.ExcelColumns.PromotionPrice].Text.Split(" ").First().Replace("£", "").Trim());



                                    var ExistingPromotion = batchPromotions.FirstOrDefault(p => p.Name == name);

                                    if (ExistingPromotion != null)
                                    {
                                        ExistingPromotion.Products.Add(product);
                                    }
                                    else
                                    {
                                        var promotion = new Promotion
                                        {
                                            Name = name,
                                            StartTime = (DateTime)startTime,
                                            EndTime = (DateTime)endTime,
                                            Products = new List<Product> { product },
                                            PromotionPrice = promotionPrice,
                                            Quantity = quantity,
                                            Type = PromotionType.XForY,
                                            Identifire = ""
                                        };

                                        batchPromotions.Add(promotion);
                                    }
                                }
                        }
                        catch (Exception ex)
                        {
                            errors.Add($"Row {row}: {ex.Message}");
                            _logger.LogError(ex, "Error processing row {Row}", row);
                            await _hubContext.Clients.All.SendAsync("ReceiveError", $"Error in row {row}: {ex.Message}");
                        }
                    }
                    
                    // Save each batch in its own transaction
                    using (var scope = new TransactionScope(TransactionScopeAsyncFlowOption.Enabled))
                    {
                        _data.Promotions.AddRange(batchPromotions);
                        _data.SaveChanges();
                        scope.Complete();
                        totalPromotions += batchPromotions.Count;
                    }
                    
                    await _hubContext.Clients.All.SendAsync("ReceiveProgress", 
                        $"Saved batch {batchStart}-{batchEnd} of {rowCount}...", 
                        80 + (20 * batchEnd / rowCount), 100);
                }
            }
            
            return (totalPromotions, errors);
        }

        private DateTime? ParseDate(string dateText, string[] formats, int row, List<string> errors)
        {
            if (string.IsNullOrEmpty(dateText))
            {
                errors.Add($"Row {row}: Empty date");
                return null;
            }

            if (DateTime.TryParseExact(dateText.Trim(), formats, 
                CultureInfo.InvariantCulture, DateTimeStyles.None, out DateTime result))
            {
                return result;
            }

            errors.Add($"Row {row}: Invalid date format '{dateText}'");
            return null;
        }
    }
}
