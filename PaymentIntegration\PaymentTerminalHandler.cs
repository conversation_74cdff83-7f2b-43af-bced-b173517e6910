﻿using System;
using System.IO.Ports;
using System.Text;
using System.Threading;


namespace PaymentIntegration
{
    class PaymentTerminalHandler
    {
        private SerialPort serialPort;

        public bool ProcessPayment(string comPort,int baudrate, decimal amount)
        {
            bool isPaymentSuccessful = false;

            // Configure serial port (adjust parameters as per terminal specs)
            serialPort = new SerialPort(comPort)
            {
                BaudRate = baudrate,
                Parity = Parity.None,
                DataBits = 8,
                StopBits = StopBits.One,
                Handshake = Handshake.RequestToSend,
                ReadTimeout = 30000, // 30 seconds
                WriteTimeout = 30000
            };

            try
            {
                serialPort.Open();

                // Construct payment command (example format; adjust per protocol)
                string command = BuildCommand(amount);

                // Send command
                byte[] data = Encoding.ASCII.GetBytes(command);
                serialPort.Write(data, 0, data.Length);

                // Read response
                string response = ReadResponse();
                isPaymentSuccessful = ParseResponse(response);
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Error: {ex.Message}");
            }
            finally
            {
                if (serialPort.IsOpen)
                    serialPort.Close();
            }

            return isPaymentSuccessful;
        }

        private string BuildCommand(decimal amount)
        {
            // Example command format (STX + Amount + ETX + Checksum)
            // Replace with actual protocol requirements
            string amountStr = amount.ToString("F2"); // Format as 10.00
            string message = $"\x02{amountStr}\x03";
            byte checksum = CalculateChecksum(message);
            return message + checksum.ToString("X2"); // Append checksum as hex
        }

        private byte CalculateChecksum(string data)
        {
            // Example: XOR checksum (adjust per protocol)
            byte checksum = 0;
            foreach (char c in data)
            {
                checksum ^= (byte)c;
            }
            return checksum;
        }

        private string ReadResponse()
        {
            // Read until ETX or timeout
            StringBuilder response = new StringBuilder();
            bool endOfText = false;
            byte[] buffer = new byte[1];

            while (!endOfText)
            {
                int bytesRead = serialPort.Read(buffer, 0, 1);
                if (bytesRead > 0)
                {
                    char receivedChar = (char)buffer[0];
                    response.Append(receivedChar);

                    if (receivedChar == '\x03') // ETX
                    {
                        // Read checksum (assuming 1 byte)
                        bytesRead = serialPort.Read(buffer, 0, 1);
                        if (bytesRead > 0)
                        {
                            response.Append((char)buffer[0]);
                        }
                        endOfText = true;
                    }
                }
            }

            return response.ToString();
        }

        private bool ParseResponse(string response)
        {
            // Example response format: STX + "APPROVED" + ETX + Checksum
            // Validate checksum first
            if (response.Length < 3)
                return false;

            string messagePart = response.Substring(0, response.Length - 1);
            char receivedChecksum = response[response.Length - 1];

            byte expectedChecksum = CalculateChecksum(messagePart);
            if ((byte)receivedChecksum != expectedChecksum)
            {
                Console.WriteLine("Checksum error");
                return false;
            }

            // Extract status
            if (messagePart.StartsWith("\x02APPROVED"))
            {
                return true;
            }
            else if (messagePart.StartsWith("\x02DECLINED"))
            {
                Console.WriteLine("Payment declined");
                return false;
            }

            return false;
        }
    }
}



// Usage example
//class Program
//{
//    static void Main()
//    {
//        var handler = new PaymentTerminalHandler();
//        bool result = handler.ProcessPayment("COM3", 10.00m);
//        Console.WriteLine($"Payment Successful: {result}");
//    }
//}