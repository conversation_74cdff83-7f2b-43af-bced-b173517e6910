﻿using POS.Core.Models;
using System.ComponentModel;

namespace POS.WPF;

public class Product : INotifyPropertyChanged
{
    private int _quantity;
    private decimal _unitPrice;
    private Promotion _promotion;
    private NonBrandPromotion _nonBrandPromotion;

    private string _note;

    public event PropertyChangedEventHandler PropertyChanged;


    public bool IsPriceOverridden { get; set; }

    public bool IsStaffDiscount { get; set; }

    public decimal StaffDiscountedAmount { get; set; }

    public decimal OverriddenDiscountAmount { get; set; }

    public string ProductName { get; set; }

    public string PLU { get; set; }

    public string Barcode { get; set; }

    public string Note
    {
        get => _note;
        set
        {
            if (_note != value)
            {
                _note = value;
                PropertyChanged?.Invoke(this, new PropertyChangedEventArgs(nameof(Note)));
            }
        }
    }

    public bool IsSpecialProduct { get; set; }


    public Promotion Promition
    {

        get => _promotion;
        set
        {
            if (_promotion != value)
            {
                _promotion = value;
                Note = "Promotion";
            }

        }
    }

    public NonBrandPromotion NonBrandPromotion
    {

        get => _nonBrandPromotion;
        set
        {
            if (_nonBrandPromotion != value)
            {
                _nonBrandPromotion = value;
                Note = "Non Brand Promotion";
            }

        }
    }

    public decimal CalculateBuyOneGetOneFree(int quantity, decimal unitPrice)
    {
        if (quantity <= 0)
        {
            return 0; 
        }

        int pairs = quantity / 2; 
        int remainder = quantity % 2; 

        decimal totalPrice = (pairs * unitPrice) + (remainder * unitPrice);

        return totalPrice;
    }

   
    


    public decimal CalculateXForY(int promotionQuantity, decimal promotionPrice,
    decimal productQuantity, decimal normalUnitPrice)
    {
        if (promotionQuantity <= 0)
        {
            throw new ArgumentException("Promotion quantity must be a positive integer.", nameof(promotionQuantity));
        }

        decimal groups = Math.Floor(productQuantity / promotionQuantity);
        decimal remainder = productQuantity % promotionQuantity;
        return groups * promotionPrice + remainder * normalUnitPrice;
    }

    public decimal CalculateXForYNew(int promotionQuantity, decimal promotionPrice,
    decimal productQuantity, decimal normalUnitPrice)
    {
        if (promotionQuantity <= 0)
        {
            throw new ArgumentException("Promotion quantity must be a positive integer.", nameof(promotionQuantity));
        }

        decimal groups = Math.Floor(productQuantity / promotionQuantity);
        decimal remainder = productQuantity % promotionQuantity;
        return groups * promotionPrice + remainder * normalUnitPrice;
    }

    // Add this method to calculate group promotions
    public static decimal CalculateGroupPromotion(List<Product> products, Promotion promotion)
    {
        if (promotion == null || promotion.Type != PromotionType.XForY || promotion.Quantity <= 0)
            return 0;
        
        // Get total quantity of all products in this promotion
        int totalQuantity = products.Sum(p => p.Quantity);
        
        // Calculate how many complete promotion groups we have
        int groups = totalQuantity / promotion.Quantity;
        int remainder = totalQuantity % promotion.Quantity;
        
        // Calculate the total price
        decimal totalPromotionPrice = groups * (decimal)promotion.PromotionPrice;
        
        // Calculate the remainder price (using the most expensive items as remainder)
        var orderedProducts = products.OrderBy(p => p.UnitPrice).ToList();
        int remainingItems = remainder;
        decimal remainderPrice = 0;
        
        foreach (var product in orderedProducts)
        {
            int itemsToUse = Math.Min(remainingItems, product.Quantity);
            if (itemsToUse > 0)
            {
                remainderPrice += itemsToUse * product.UnitPrice;
                remainingItems -= itemsToUse;
            }
            
            if (remainingItems <= 0)
                break;
        }
        
        return totalPromotionPrice + remainderPrice;
    }

    public decimal UnitPrice
    {
        get => _unitPrice;
        set
        {
            if (_unitPrice != value)
            {
                _unitPrice = value;
                OnPropertyChanged(nameof(UnitPrice));
                UpdateTotal();
            }
        }
    }

    public int Quantity
    {
        get => _quantity;
        set
        {
            if (_quantity != value)
            {
                _quantity = value;

                if (_promotion != null)
                {
                    if (_promotion.Type == PromotionType.BuyOneGetOneFree)
                    {
                        OverriddenPrice = CalculateBuyOneGetOneFree(Quantity, UnitPrice);
                    }
                    else if (_promotion.Type == PromotionType.SingleProductPromotion)
                    {
                        OverriddenPrice = (decimal)_promotion.PromotionPrice * Quantity;
                    }
                    // Note: no XForY branch here
                }
                else if(_nonBrandPromotion != null)
                {
                    if (_nonBrandPromotion.Type == PromotionType.BuyOneGetOneFree)
                    {
                        OverriddenPrice = CalculateBuyOneGetOneFree(Quantity, UnitPrice);
                    }
                    else if (_nonBrandPromotion.Type == PromotionType.XForY)
                    {
                        OverriddenPrice = CalculateXForYNew(_nonBrandPromotion.Quantity, (decimal)_nonBrandPromotion.PromotionPrice, Quantity, UnitPrice);
                    }
                    else if (_nonBrandPromotion.Type == PromotionType.SingleProductPromotion)
                    {
                        // Apply promotion price for any quantity (including 1)
                        OverriddenPrice = (decimal)_nonBrandPromotion.PromotionPrice * Quantity;
                    }
                }

                OnPropertyChanged(nameof(Quantity));
                UpdateTotal();
            }
        }
    }

    private decimal? _overriddenPrice;


    /// <summary>
    /// this Property is used to track overridden prices ( promotion , staff discount and price override)
    /// this is returned as the Total value for a product when this is not null
    /// </summary>
    public decimal? OverriddenPrice
    {
        get => _overriddenPrice;
        set
        {
            if (_overriddenPrice != value)
            {
                _overriddenPrice = value;
                OnPropertyChanged(nameof(OverriddenPrice));
                OnPropertyChanged(nameof(Total)); // Trigger recalculation of Total
            }
        }
    }

    public decimal Total => OverriddenPrice ?? (UnitPrice * Quantity);


    public void UpdateTotal()
    {
       

        OnPropertyChanged(nameof(Total)); // Ensure Total gets notified
    }

    protected void OnPropertyChanged(string propertyName)
    {
        PropertyChanged?.Invoke(this, new PropertyChangedEventArgs(propertyName));
    }

    private int _subCategoryId;
    public int SubCategoryId
    {
        get => _subCategoryId;
        set
        {
            if (_subCategoryId != value)
            {
                _subCategoryId = value;
                OnPropertyChanged(nameof(SubCategoryId));
            }
        }
    }
}


