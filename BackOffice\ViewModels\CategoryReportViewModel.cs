﻿using POS.Core.Models;

namespace POS.BackOffice.ViewModels
{
    public class CategoryReportViewModel
    {
        public DateTime? StartDate { get; set; }
        public DateTime? EndDate { get; set; }
        public DateTime? SingleDate { get; set; }
        public string Period { get; set; }

        public List<SubCatagory> SubCatagories { get; set; }

        public List<Company> Companies { get; set; }

        public int SelectedStoreId { get; set; }
        public int SelectedCompanyId { get; set; }

        public int SelectedCategoryId { get; set; }
    }
}
