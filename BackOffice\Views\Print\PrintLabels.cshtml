﻿@using System.Globalization
@model List<BackOffice.ViewModels.ProductLabel>

@{
    Layout = null;
    const int labelsPerPage = 21;
    var pageCount = (int)Math.Ceiling((double)Model.Count / labelsPerPage);
}

<!DOCTYPE html>
<html>
<head>
    <style>
        @@page {
            size: A4;
            margin: 11mm 4mm 0mm 0mm;
        }

        body {
            margin: 0;
            padding: 0;
        }

        .page-break {
            page-break-after: always;
            padding: 10px;
            width: 8.27in;
            height: 11.69in;
            box-sizing: border-box;
        }

        .label-grid {
            display: grid;
            grid-template-columns: repeat(3, 1fr);
            grid-template-rows: repeat(7, 145px);
            gap: 10px 20px;
            height: 100%;
        }

        .label-item {
            border: 1px dashed #ccc;
            padding: 10px;
            box-sizing: border-box;
        }

        .product-name {
            font-size: 16px;
            font-weight: bold;
            text-align: center;
            margin-bottom: 10px;
            min-height: 40px;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .content-row {
            display: flex;
            justify-content: space-between;
            align-items: flex-start;
            flex: 1;
        }

        .barcode-container {
            display: flex;
            flex-direction: column;
            align-items: center;
        }

        .barcode {
            width: 120px;
            height: 50px;
        }

        .barcode-number {
            font-size: 10px;
            text-align: center;
            margin-top: 2px;
        }

        .price {
            font-size: 32px;
            font-weight: bold;
            text-align: right;
            margin-top: 10px;
        }

        .print-button {
            position: fixed;
            bottom: 20px;
            right: 20px;
            padding: 10px 20px;
            background-color: #007bff;
            color: white;
            border: none;
            border-radius: 5px;
            cursor: pointer;
            z-index: 1000;
        }

            .print-button:hover {
                background-color: #0056b3;
            }

        @@media print {
            .print-button {
                display: none;
            }

            body {
                margin: 0;
                padding: 0;
            }

            .label-item {
                border: none;
            }
        }
    </style>
</head>
<body>
    @for (int page = 0; page < pageCount; page++)
    {
        var items = Model.Skip(page * labelsPerPage).Take(labelsPerPage).ToList();
        <div class="page-break">
            <div class="label-grid">
                @foreach (var label in items)
                {
                    <div class="label-item">
                        <div class="product-name">@label.Name</div>
                        <div class="content-row">
                            <div class="barcode-container">
                                <svg class="barcode" data-barcode="@label.BarcodeNumber"></svg>
                                <div class="barcode-number">@label.BarcodeNumber</div>
                            </div>
                            <div class="price">@label.Price.ToString("C", new CultureInfo("en-GB"))</div>
                        </div>
                    </div>
                }
            </div>
        </div>
    }

    <button class="print-button" onclick="window.print()">Print Labels</button>

    <script src="https://cdn.jsdelivr.net/npm/jsbarcode@3.11.5/dist/JsBarcode.all.min.js"></script>
    <script>
        document.querySelectorAll('.barcode').forEach((barcode) => {
            const barcodeNumber = barcode.getAttribute('data-barcode');
            JsBarcode(barcode, barcodeNumber, {
                format: "CODE128",
                displayValue: false,
                width: 1,
                height: 50,
            });
        });
    </script>
</body>
</html>
