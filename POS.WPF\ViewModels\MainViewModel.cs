﻿using POS.Core.Models;
using POS.WPF.Helpers;
using System;
using System.Collections.Generic;
using System.Collections.ObjectModel;
using System.ComponentModel;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Windows.Input;
using TimeManagement;
using Microsoft.EntityFrameworkCore;

namespace POS.WPF.ViewModels
{
    public class MainViewModel : INotifyPropertyChanged
    {
        public ObservableCollection<Product> Products { get; set; }


        public MainViewModel()
        {
            Products = new ObservableCollection<Product>();
            Products.CollectionChanged += Products_CollectionChanged;

            IncreaseQuantityCommand = new RelayCommand<Product>(IncreaseQuantity);
            DecreaseQuantityCommand = new RelayCommand<Product>(DecreaseQuantity);
            DeleteProductCommand = new RelayCommand<Product>(DeleteProduct);
        }

        private void Products_CollectionChanged(object sender,
       System.Collections.Specialized.NotifyCollectionChangedEventArgs e)
        {
            if (e.NewItems != null)
            {
                foreach (Product p in e.NewItems)
                {
                    p.PropertyChanged += Product_PropertyChanged;
                }
            }
            if (e.OldItems != null)
            {
                foreach (Product p in e.OldItems)
                {
                    p.PropertyChanged -= Product_PropertyChanged;
                }
            }

            // Any time the collection itself changes, re-apply promotions:
            ApplyAllPromotions();
            UpdateTotals();
        }

        private void Product_PropertyChanged(object sender, PropertyChangedEventArgs e)
        {
            if (e.PropertyName == nameof(Product.Quantity) ||
            e.PropertyName == nameof(Product.Promition))
            {
                // Re-apply every group promotion across the entire collection
                ApplyAllPromotions();
                UpdateTotals();
            }

            if (e.PropertyName == nameof(Product.Total))
            {
                Console.WriteLine("Product total changed, updating totals...");
                UpdateTotals(); // Ensure totals are updated
            }
        }


        #region ── APPLY GROUP PROMOTIONS ──

        /// <summary>
        /// Go through all products in the cart, find each distinct XForY promotion,
        /// and recalculate OverriddenPrice for every product in that group.
        /// </summary>

        public void ApplyAllPromotions()
        {
            // 1) Clear any old override on XForY products
            foreach (var p in Products)
            {
                if (p.Promition != null && p.Promition.Type == PromotionType.XForY)
                    p.OverriddenPrice = null;
                // If you also support NonBrand XForY: clear p.OverriddenPrice similarly
            }

            // 2) Group products by promotion‐ID (not by object instance)
            var promosById = Products
                .Where(p => p.Promition != null && p.Promition.Type == PromotionType.XForY)
                .GroupBy(p => p.Promition.Id)
                .ToList();

            // 3) For each distinct Promotion.Id, collect those products and redistribute
            foreach (var grouping in promosById)
            {
                // grouping.Key is promotion.Id, but we need the actual Promotion object.
                // Take the first product's Promotion instance (they all share the same Id).
                var promo = grouping.First().Promition;
                var groupItems = grouping.ToList();

                if (groupItems.Count == 0)
                    continue;

                DistributeXForYAcrossProducts(groupItems, promo);
            }
            UpdateTotals();
        }




        /// <summary>
        /// Given a list of WPF Products that share the same XForY Promotion,
        /// set each product's OverriddenPrice so that exactly (groups * promotion.Quantity)
        /// items fetch the promotional unit price, and remainder fetch normal UnitPrice.
        /// 
        /// We "flatten" the quantities into an ordered list (largest‐quantity products first)
        /// so that if one product has enough quantity to form entire groups, it gets prioritized.
        /// </summary>





        /// <summary>
        /// Given a list of WPF Products that share the same XForY Promotion,
        /// set each product's OverriddenPrice so that exactly (groups * promo.Quantity)
        /// items fetch the promotional unit price, and remainder fetch normal UnitPrice.
        /// 
        /// We sort by descending UnitPrice so that the most expensive units go into
        /// the promotional slots first. This works no matter how large the quantities.
        /// </summary>


        private void DistributeXForYAcrossProducts(
    List<Product> productsInSamePromo,
    Promotion promo)
        {
            // 1) Read promotion parameters
            int promoQty = promo.Quantity;                   // e.g. 2 for "2 for 15"
            decimal promoPrice = (decimal)promo.PromotionPrice;    // e.g. 15m

            // 2) Sum total quantity across all products in this group
            int totalQuantity = productsInSamePromo.Sum(p => p.Quantity);
            if (totalQuantity <= 0)
            {
                // If nobody in the group has a positive quantity, clear any override and exit.
                foreach (var p in productsInSamePromo)
                    p.OverriddenPrice = null;
                return;
            }

            // 3) How many full "bundles" fit?
            int groups = totalQuantity / promoQty;           // e.g. if total=4 & promoQty=2 → groups=2
            int promoItemsCount = groups * promoQty;         // e.g. 2 * 2 = 4 units get promo

            // 4) Per‐unit price under the promotion:
            decimal perItemPromoUnitPrice = promoPrice / promoQty;    // e.g. 15m / 2 = 7.50

            // 5) Prioritize grouping same products first
            var productAssignedTotals = productsInSamePromo.ToDictionary(p => p, p => 0m);
            int remainingPromoItems = promoItemsCount;
            
            // Sort products by quantity descending to prioritize products with higher quantities
            var sortedProducts = productsInSamePromo.OrderByDescending(p => p.Quantity).ToList();
            
            // First pass: Try to use complete promotion groups from individual products
            foreach (var product in sortedProducts)
            {
                if (remainingPromoItems <= 0) break;
                
                // How many complete promotion groups can we make from this product?
                int productGroups = Math.Min(product.Quantity / promoQty, remainingPromoItems / promoQty);
                
                if (productGroups > 0)
                {
                    int itemsInPromo = productGroups * promoQty;
                    productAssignedTotals[product] += itemsInPromo * perItemPromoUnitPrice;
                    remainingPromoItems -= itemsInPromo;
                    
                    // Mark remaining items at regular price
                    int regularItems = product.Quantity - itemsInPromo;
                    if (regularItems > 0)
                    {
                        productAssignedTotals[product] += regularItems * product.UnitPrice;
                    }
                }
                else
                {
                    // Can't form a complete group from this product alone
                    productAssignedTotals[product] += product.Quantity * product.UnitPrice;
                }
            }
            
            // Second pass: Handle remaining promotion items across products
            if (remainingPromoItems > 0)
            {
                // Reset and rebuild from scratch for mixed groups
                productAssignedTotals = productsInSamePromo.ToDictionary(p => p, p => 0m);
                remainingPromoItems = promoItemsCount;
                
                // Build a flat list of all individual units, grouped by product
                var allUnits = new List<(Product product, decimal unitPrice)>();
                foreach (var p in sortedProducts)
                {
                    for (int i = 0; i < p.Quantity; i++)
                    {
                        allUnits.Add((p, p.UnitPrice));
                    }
                }
                
                // Assign promotion prices to the first promoItemsCount units
                for (int i = 0; i < allUnits.Count; i++)
                {
                    var (prod, price) = allUnits[i];
                    if (i < promoItemsCount)
                    {
                        productAssignedTotals[prod] += perItemPromoUnitPrice;
                    }
                    else
                    {
                        productAssignedTotals[prod] += price;
                    }
                }
            }

            // Set each product's OverriddenPrice to the calculated total
            foreach (var p in productsInSamePromo)
            {
                decimal assignedTotal = productAssignedTotals[p];
                if (assignedTotal > 0m)
                    p.OverriddenPrice = assignedTotal;
                else
                    p.OverriddenPrice = null;
            }
        }


        #endregion

        public ICommand IncreaseQuantityCommand { get; }
        public ICommand DecreaseQuantityCommand { get; }

        public ICommand DeleteProductCommand { get; }

        private decimal _subTotal;
        private decimal _discount;
        private decimal _tax;
        private decimal _totalAmount;

        public decimal SubTotal
        {
            get => _subTotal;
            private set
            {
                if (_subTotal != value)
                {
                    _subTotal = value;
                    OnPropertyChanged(nameof(SubTotal));
                }
            }
        }


        public decimal Discount
        {
            get => _discount;
            private set // Make setter private to prevent external changes
            {
                if (_discount != value)
                {
                    _discount = value;
                    OnPropertyChanged(nameof(Discount));
                }
            }
        }

        public decimal Tax
        {
            get => _tax;
            set
            {
                if (_tax != value)
                {
                    _tax = value;
                    OnPropertyChanged(nameof(Tax));
                    UpdateTotals();
                }
            }
        }

        public decimal Total { get; private set; }

        public decimal TotalAmount
        {
            get => _totalAmount;
            private set
            {
                if (_totalAmount != value)
                {
                    _totalAmount = value;
                    OnPropertyChanged(nameof(TotalAmount));
                }
            }
        }

        // Add a method to check if adding a product would exceed subcategory limits
        public bool WouldExceedSubCategoryLimit(int subCategoryId, int quantityToAdd = 1)
        {
            using (var context = new LocalData.LocalContext())
            {
                // Get the limit for this subcategory and store
                var limit = context.StoreSubCategoryLimits
                    .FirstOrDefault(sl => sl.StoreId == UiShift.StoreId && 
                                         sl.SubCategoryId == subCategoryId);
                
                // If no limit is set or limit is 0, return false (no limit exceeded)
                if (limit == null || limit.QuantityLimit <= 0)
                    return false;
                
                // Calculate current quantity of products from this subcategory in the cart
                var currentSubCategoryQuantity = Products
                    .Where(p => p.SubCategoryId == subCategoryId)
                    .Sum(p => p.Quantity);
                
                // Check if adding the specified quantity would exceed the limit
                return (currentSubCategoryQuantity + quantityToAdd) > limit.QuantityLimit;
            }
        }

        // Add a method to get the limit for a subcategory
        public int GetSubCategoryLimit(int subCategoryId)
        {
            using (var context = new LocalData.LocalContext())
            {
                var limit = context.StoreSubCategoryLimits
                    .FirstOrDefault(sl => sl.StoreId == UiShift.StoreId && 
                                         sl.SubCategoryId == subCategoryId);
                
                return limit?.QuantityLimit ?? 0;
            }
        }

        private void IncreaseQuantity(Product product)
        {
            if (product != null && product.IsSpecialProduct == false)
            {
                // If SubCategoryId is 0, try to fetch it from the database
                if (product.SubCategoryId == 0)
                {
                    using (var context = new LocalData.LocalContext())
                    {
                        var dbProduct = context.Products
                            .Include(p => p.Division)
                            .ThenInclude(d => d.SubCatagory)
                            .FirstOrDefault(p => p.PLU == product.PLU);
                        
                        if (dbProduct != null && dbProduct.Division != null && dbProduct.Division.SubCatagory != null)
                        {
                            product.SubCategoryId = dbProduct.Division.SubCatagory.Id;
                        }
                    }
                }
                
                // Check if increasing quantity would exceed subcategory limit
                if (product.SubCategoryId > 0 && WouldExceedSubCategoryLimit(product.SubCategoryId))
                {
                    var limit = GetSubCategoryLimit(product.SubCategoryId);
                    System.Windows.MessageBox.Show(
                        $"Cannot add more items from this subcategory. " +
                        $"Limit of {limit} has been reached.",
                        "Quantity Limit Reached", 
                        System.Windows.MessageBoxButton.OK, 
                        System.Windows.MessageBoxImage.Warning);
                    return;
                }
                
                product.Quantity++;
                UpdateTotals(); // Recalculate totals after changing quantity
            }
        }

        private void DecreaseQuantity(Product product)
        {
            if (product != null && product.Quantity > 0)
            {
                product.Quantity--;
                UpdateTotals(); // Recalculate totals after changing quantity
            }
        }

        private void DeleteProduct(Product product)
        {
            if (product != null)
            {
                using (LocalData.LocalContext data = new LocalData.LocalContext())
                {
                    var deleteItem = new DeletedSaleItem()
                    {
                        PLU = product.PLU,
                        Qty = product.Quantity,
                        StoreId = (int)UiShift.StoreId,
                        PosId = (int)UiShift.PosMachineId,
                        Total = product.Total,
                        DeletedTime = CustomTimeProvider.Now,
                        LastModified = CustomTimeProvider.Now,
                        IsDeleted = false
                    };

                    data.DeletedSaleItems.Add(deleteItem);

                    data.SaveChanges();

                    data.PendingSyncItems.Add(new PendingSyncItem()
                    {
                        CreatedAt = CustomTimeProvider.Now,
                        EntityType = "DeletedSaleItem",
                        OperationType = "Insert",
                        SyncError = "",
                        EntityId = deleteItem.Id,


                    });

                    data.SaveChanges();
                }

                Products.Remove(product);
                UpdateTotals(); // Recalculate totals after deleting a product
            }
        }


        #region ── UPDATE TOTAL/TAX/DISCOUNT ──

        public void UpdateTotals()
        {
            decimal newSubTotal = 0m;
            decimal newDiscount = 0m;
            decimal newTax = 0m;
            decimal newTotalAmount = 0m;

            foreach (var product in Products)
            {
                if (product.IsPriceOverridden)
                {
                    // If OverriddenPrice is set (e.g. from any promotion),
                    // we treat that as the line total:
                    newSubTotal += product.Total;
                    newTotalAmount += product.Total;
                }
                else if (product.Promition is Promotion)
                {
                    // If there's a non-XForY promotion (e.g. SingleProduct or BOGO),
                    // Total was already set in product.Quantity setter. Just add:
                    newSubTotal += product.Total;
                    newTotalAmount += product.Total;
                }
                else if (product.NonBrandPromotion is NonBrandPromotion)
                {
                    newSubTotal += product.Total;
                    newTotalAmount += product.Total;
                }
                else if (product.IsStaffDiscount)
                {
                    decimal originalTotal = product.UnitPrice * product.Quantity;
                    newSubTotal += originalTotal;
                    newDiscount += originalTotal * 0.2m;
                    newTotalAmount += originalTotal - (originalTotal * 0.2m);
                }
                else if (product.IsSpecialProduct)
                {
                    newSubTotal += product.Total;
                    newTotalAmount += product.Total;
                }
                else
                {
                    newSubTotal += product.UnitPrice * product.Quantity;
                    newTotalAmount += product.UnitPrice * product.Quantity;
                }
            }

            newTax = newSubTotal * 0.05m;       // 5% tax on SubTotal
            newTotalAmount = (newSubTotal - newDiscount);

            SubTotal = newSubTotal;
            Discount = newDiscount;
            Tax = newTax;
            TotalAmount = newTotalAmount;
        }



        #endregion


        public event PropertyChangedEventHandler PropertyChanged;
        protected void OnPropertyChanged(string propertyName)
        {
            PropertyChanged?.Invoke(this, new PropertyChangedEventArgs(propertyName));
        }



    }
}
