﻿using Microsoft.AspNetCore.Authentication.Cookies;
using Microsoft.AspNetCore.Authentication;
using Microsoft.AspNetCore.Mvc;
using System.Security.Claims;
using System;
using POS.Core.Models;
using Microsoft.EntityFrameworkCore;

namespace BackOffice.Controllers
{
    public class AuthController : Controller
    {
        private readonly Data _context;

        public AuthController(Data context)
        {
            _context = context;
        }

        [HttpGet]
        public IActionResult Login()
        {
            return View();
        }

        [HttpPost]
        public async Task<IActionResult> Login(string name, string password)
        {
            try
            {
                User? user = _context.Admins.FirstOrDefault(u => u.Name == name && u.Password == password);
                if (user == null)
                {
                    user = _context.Accountants
                        .Include(a => a.IpAddresses)
                        .FirstOrDefault(u => u.Name == name && u.Password == password);
                }
                if (user == null)
                {
                    user = _context.BackOffices
                        .Include(b => b.IpAddresses)
                        .FirstOrDefault(u => u.Name == name && u.Password == password);
                }

                if (user == null)
                {
                    ViewBag.Error = "Invalid credentials.";
                    return View();
                }

                // Check IP address restrictions for Accountant and BackOffice users
                if (user is Accountant accountant || user is POS.Core.Models.BackOffice backOffice)
                {
                    string currentIp = HttpContext.Connection.RemoteIpAddress.ToString();
                    
                    // For local development, handle localhost scenarios
                    if (currentIp == "::1" || currentIp == "127.0.0.1")
                    {
                        currentIp = "localhost";
                    }
                    
                    bool hasIpRestrictions = false;
                    bool isAllowedIp = false;
                    
                    if (user is Accountant acc)
                    {
                        hasIpRestrictions = acc.IpAddresses.Any();
                        isAllowedIp = !hasIpRestrictions || acc.IpAddresses.Any(ip => ip.Address == currentIp);
                    }
                    else if (user is POS.Core.Models.BackOffice bo)
                    {
                        hasIpRestrictions = bo.IpAddresses.Any();
                        isAllowedIp = !hasIpRestrictions || bo.IpAddresses.Any(ip => ip.Address == currentIp);
                    }
                    
                    if (!isAllowedIp)
                    {
                        ViewBag.Error = "Access denied. Your IP address is not authorized.";
                        return View();
                    }
                }

                var claims = new List<Claim>
                {
                    new Claim(ClaimTypes.Name, user.Name),
                    new Claim(ClaimTypes.Role, user.Role),
                    new Claim(ClaimTypes.NameIdentifier, user.Id.ToString())
                };

                // Add IP address to claims for middleware validation
                claims.Add(new Claim("UserIP", HttpContext.Connection.RemoteIpAddress.ToString()));

                var claimsIdentity = new ClaimsIdentity(claims, CookieAuthenticationDefaults.AuthenticationScheme);
                var authProperties = new AuthenticationProperties();

                await HttpContext.SignInAsync(
                    CookieAuthenticationDefaults.AuthenticationScheme,
                    new ClaimsPrincipal(claimsIdentity),
                    authProperties
                );
               
                if (user.Role == "BackOffice")
                {
                    return RedirectToAction("Index", "BackOffice");
                }

                return RedirectToAction("Index", "Home");
            }
            catch (Exception ex)
            {
                return Content(ex.Message + "\n" + ex.StackTrace);
            }
        }

        public async Task<IActionResult> Logout()
        {
            await HttpContext.SignOutAsync(CookieAuthenticationDefaults.AuthenticationScheme);
            return RedirectToAction("Login");
        }

        public IActionResult AccessDenied()
        {
            return View();
        }
    }
}
