using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Mvc.Rendering;
using Microsoft.EntityFrameworkCore;
using POS.Core.Models;
using TimeManagement;

namespace BackOffice.Controllers
{
    [Authorize(Roles = "BackOffice")]
    public class QuickAccessButtonsController : Controller
    {
        private readonly Data _context;

        public QuickAccessButtonsController(Data context)
        {
            _context = context;
        }

        // GET: QuickAccessButtons
        public async Task<IActionResult> Index()
        {
            var buttons = _context.QuickAccessButtons
                .Include(b => b.Product)
                .Include(b => b.Store);
            return View(await buttons.ToListAsync());
        }

        // GET: QuickAccessButtons/Create
        public IActionResult Create()
        {
            try
            {
                ViewData["ProductId"] = new SelectList(_context.Products, "Id", "Name");
                ViewData["StoreId"] = new SelectList(_context.Stores, "StoreId", "StoreName");
                return View();
            }
            catch (Exception ex)
            {
                return Content(ex.Message);
            }
        }

        // POST: QuickAccessButtons/Create
        [HttpPost]
        [ValidateAntiForgeryToken]
        public async Task<IActionResult> Create([Bind("Id,ButtonText,ProductId,StoreId")] QuickAccessButton button)
        {
            ModelState.Remove(nameof(button.Product));
            ModelState.Remove(nameof(button.Store));

            if (ModelState.IsValid)
            {
                _context.Add(button);
                await _context.SaveChangesAsync();
                return RedirectToAction(nameof(Index));
            }
            
            ViewData["ProductId"] = new SelectList(_context.Products, "Id", "Name", button.ProductId);
            ViewData["StoreId"] = new SelectList(_context.Stores, "StoreId", "Name", button.StoreId);
            return View(button);
        }

        // GET: QuickAccessButtons/Edit/5
        public async Task<IActionResult> Edit(int? id)
        {
            if (id == null)
                return NotFound();

            var button = await _context.QuickAccessButtons.FindAsync(id);
            if (button == null)
                return NotFound();

            ViewData["ProductId"] = new SelectList(_context.Products, "Id", "Name", button.ProductId);
            ViewData["StoreId"] = new SelectList(_context.Stores, "StoreId", "Name", button.StoreId);
            return View(button);
        }

        // POST: QuickAccessButtons/Edit/5
        [HttpPost]
        [ValidateAntiForgeryToken]
        public async Task<IActionResult> Edit(int id, [Bind("Id,ButtonText,ProductId,StoreId")] QuickAccessButton button)
        {
            if (id != button.Id)
                return NotFound();

            ModelState.Remove(nameof(button.Product));
            ModelState.Remove(nameof(button.Store));

            if (ModelState.IsValid)
            {
                try
                {
                    _context.Update(button);
                    await _context.SaveChangesAsync();
                }
                catch (DbUpdateConcurrencyException)
                {
                    if (!QuickAccessButtonExists(button.Id))
                        return NotFound();
                    else
                        throw;
                }
                return RedirectToAction(nameof(Index));
            }
            
            ViewData["ProductId"] = new SelectList(_context.Products, "Id", "Name", button.ProductId);
            ViewData["StoreId"] = new SelectList(_context.Stores, "StoreId", "Name", button.StoreId);
            return View(button);
        }

        // GET: QuickAccessButtons/Delete/5
        public async Task<IActionResult> Delete(int? id)
        {
            if (id == null)
                return NotFound();

            var button = await _context.QuickAccessButtons
                .Include(b => b.Product)
                .Include(b => b.Store)
                .FirstOrDefaultAsync(m => m.Id == id);
                
            if (button == null)
                return NotFound();

            return View(button);
        }

        // POST: QuickAccessButtons/Delete/5
        [HttpPost, ActionName("Delete")]
        [ValidateAntiForgeryToken]
        public async Task<IActionResult> DeleteConfirmed(int id)
        {
            var button = await _context.QuickAccessButtons.FindAsync(id);
            if (button != null)
            {
                _context.QuickAccessButtons.Remove(button);
                await _context.SaveChangesAsync();
            }
            return RedirectToAction(nameof(Index));
        }

        private bool QuickAccessButtonExists(int id)
        {
            return _context.QuickAccessButtons.Any(e => e.Id == id);
        }
    }
}