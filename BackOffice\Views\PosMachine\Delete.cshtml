﻿@model POS.Core.Models.PosMachine

@{
    ViewData["Title"] = "Delete POS Machine";
}

<h2>Are you sure you want to delete this?</h2>

<div>
    <p><strong>Store:</strong> @Model.Store?.StoreName</p>

    <form asp-action="Delete">
        <input type="hidden" asp-for="Id" />
        <button type="submit" class="btn btn-danger">Delete</button>
        <a asp-action="Index" class="btn btn-secondary">Cancel</a>
    </form>
</div>
