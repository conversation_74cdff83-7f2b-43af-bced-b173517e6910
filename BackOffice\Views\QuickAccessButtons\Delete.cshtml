@model POS.Core.Models.QuickAccessButton

@{
    ViewData["Title"] = "Delete Quick Access Button";
}

<h1>Delete Quick Access Button</h1>

<h3>Are you sure you want to delete this?</h3>
<div>
    <h4>Quick Access Button</h4>
    <hr />
    <dl class="row">
        <dt class="col-sm-2">
            Button Text
        </dt>
        <dd class="col-sm-10">
            @Html.DisplayFor(model => model.ButtonText)
        </dd>
        <dt class="col-sm-2">
            Product
        </dt>
        <dd class="col-sm-10">
            @Html.DisplayFor(model => model.Product.Name)
        </dd>
        <dt class="col-sm-2">
            Store
        </dt>
        <dd class="col-sm-10">
            @Html.DisplayFor(model => model.Store.StoreName)
        </dd>
    </dl>

    <form asp-action="Delete">
        <input type="hidden" asp-for="Id" />
        <input type="submit" value="Delete" class="btn btn-danger" /> |
        <a asp-action="Index" class="btn btn-secondary">Back to List</a>
    </form>
</div>
