﻿@model POS.Core.Models.Division

@{
    ViewData["Title"] = "Delete Division";
}

<h2>Are you sure you want to delete this?</h2>
<div>
    <h4>Division: @Model.Name</h4>
    <h5>SubCategory: @(Model.SubCatagory?.Name ?? "N/A")</h5>

    <form asp-action="Delete">
        <input type="hidden" asp-for="Id" />
        <button type="submit" class="btn btn-danger">Delete</button>
        <a asp-action="Index" class="btn btn-secondary">Cancel</a>
    </form>
</div>
