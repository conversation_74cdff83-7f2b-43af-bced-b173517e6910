﻿using BackOffice.ViewModels;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using POS.Core.Models;
using TimeManagement;
using System.IO;
using System.Linq;
using System.Collections.Generic;
using System.Threading.Tasks;
using System.Security.Claims;

namespace BackOffice.Controllers
{
    [Authorize(Roles = "BackOffice")]
    public class PrintController : Controller
    {
        private readonly Data _context;

        public PrintController(Data context)
        {
            _context = context;
        }

        // Helper method to get the current BackOffice user's store ID
        private int GetCurrentUserStoreId()
        {
            var userId = int.Parse(User.Claims.FirstOrDefault(c => c.Type == ClaimTypes.NameIdentifier)?.Value);
            var backOfficeUser = _context.BackOffices.Find(userId);
            return backOfficeUser?.StoreId ?? 0;
        }

        // Helper method to get the correct price for a product based on store
        private decimal GetProductPrice(Product product)
        {
            int storeId = GetCurrentUserStoreId();
            
            // Check if there's a store-specific price
            var storeProduct = _context.ProductStores
                .FirstOrDefault(ps => ps.ProductId == product.Id && ps.StoreId == storeId);
            
            // Return store-specific price if available, otherwise return the default price
            return storeProduct?.StoreSpecificPrice ?? product.SellingPrice;
        }

        // Load the initial
        // 


        public async Task<IActionResult> SelectProduct()
        {
            var brands = await _context.Brands.ToListAsync();
            ViewBag.Brands = brands;
            return View();
        }


        public ActionResult SelectProducts()
        {
            var model = new ProductSelectionViewModel
            {
                Brands = _context.Brands.ToList()
            };
            return View(model);
        }


        // Fetch products based on brand selection (AJAX)


        public JsonResult GetProductsByBrand(int brandId)
        {
            var products = _context.Products
                                   .Where(p => p.BrandId == brandId)
                                   .Select(p => new { p.Id, p.Name })
                                   .ToList();
            return Json(products);
        }

        


        public IActionResult SubmitProduct(int productId)
        {
            if (productId == 0)
            {
                TempData["Error"] = "Please select a product.";
                return RedirectToAction("SelectProduct");
            }

            var product = _context.Products.Find(productId);
            if (product == null)
            {
                TempData["Error"] = "Product not found.";
                return RedirectToAction("SelectProduct");
            }

            // Get the correct price based on store
            decimal price = GetProductPrice(product);

            ProductLabel label = new ProductLabel()
            {
                BarcodeNumber = product.Barcode.ToString(),
                Date = CustomTimeProvider.Now.Date.ToString("dd/MM/yyyy"),
                Name = product.Name,
                PLU = product.PLU.ToString(),
                Price = price,
                Volume = product.PurchasePackSize.ToString(),
            };

            return View("PrintLabels", label);
        }

        [HttpPost]
        public ActionResult ProcessSelectedProducts(ProductSelectionViewModel model)
        {
            List<ProductLabel> labels = new List<ProductLabel>();

            foreach (var productId in model.SelectedProductIds) 
            {
                var product = _context.Products.Find(productId);
                if (product == null) continue;

                decimal price = GetProductPrice(product);
                ProductLabel label = new ProductLabel()
                {
                    BarcodeNumber = product.Barcode.ToString(),
                    Date = CustomTimeProvider.Now.Date.ToString("dd/MM/yyyy"),
                    Name = product.Name,
                    PLU = product.PLU.ToString(),
                    Price = price,
                    Volume = product.PurchasePackSize.ToString(),
                };
                labels.Add(label);
            }


            return View("PrintLabels", labels);
        }




        public IActionResult PrintLabels(int productId)
        {
            var labels = new List<ProductLabel>
        {
             new ProductLabel { Name = "Ciroc Green Apple", Price = 34.99m,
                         BarcodeNumber = "123456789012", Volume = "70cl", PLU="34433", Date="01/01/2023"},
               new ProductLabel { Name = "Ciroc Green Apple", Price = 34.99m,
                         BarcodeNumber = "123456789012", Volume = "70cl", PLU="34433", Date="01/01/2023"},
               new ProductLabel { Name = "Ciroc Green Apple", Price = 34.99m,
                         BarcodeNumber = "123456789012", Volume = "70cl", PLU="34433", Date="01/01/2023"},
new ProductLabel { Name = "Ciroc Green Apple", Price = 34.99m,
                         BarcodeNumber = "123456789012", Volume = "70cl", PLU="34433", Date="01/01/2023"},
new ProductLabel { Name = "Ciroc Green Apple", Price = 34.99m,
                         BarcodeNumber = "123456789012", Volume = "70cl", PLU="34433", Date="01/01/2023"},
new ProductLabel { Name = "Ciroc Green Apple", Price = 34.99m,
                         BarcodeNumber = "123456789012", Volume = "70cl", PLU="34433", Date="01/01/2023"},
new ProductLabel { Name = "Ciroc Green Apple", Price = 34.99m,
                         BarcodeNumber = "123456789012", Volume = "70cl", PLU="34433", Date="01/01/2023"},
new ProductLabel { Name = "Ciroc Green Apple", Price = 34.99m,
                         BarcodeNumber = "123456789012", Volume = "70cl", PLU="34433", Date="01/01/2023"},
               new ProductLabel { Name = "Ciroc Green Apple", Price = 34.99m,
                         BarcodeNumber = "123456789012", Volume = "70cl", PLU="34433", Date="01/01/2023"},
               new ProductLabel { Name = "Ciroc Green Apple", Price = 34.99m,
                         BarcodeNumber = "123456789012", Volume = "70cl", PLU="34433", Date="01/01/2023"},
new ProductLabel { Name = "Ciroc Green Apple", Price = 34.99m,
                         BarcodeNumber = "123456789012", Volume = "70cl", PLU="34433", Date="01/01/2023"},
new ProductLabel { Name = "Ciroc Green Apple", Price = 34.99m,
                         BarcodeNumber = "123456789012", Volume = "70cl", PLU="34433", Date="01/01/2023"},
new ProductLabel { Name = "Ciroc Green Apple", Price = 34.99m,
                         BarcodeNumber = "123456789012", Volume = "70cl", PLU="34433", Date="01/01/2023"},
new ProductLabel { Name = "Ciroc Green Apple", Price = 34.99m,
                         BarcodeNumber = "123456789012", Volume = "70cl", PLU="34433", Date="01/01/2023"},


        };

            return View("PrintLabels", labels);
        }

        [HttpGet]
        public IActionResult UploadCsv()
        {
            return View();
        }

        [HttpPost]
        public async Task<IActionResult> UploadCsv(IFormFile file)
        {
            if (file == null || file.Length == 0)
            {
                TempData["Error"] = "Please select a CSV file.";
                return RedirectToAction("UploadCsv");
            }

            List<ProductLabel> labels = new List<ProductLabel>();
            
            using (var reader = new StreamReader(file.OpenReadStream()))
            {
                string line;
                // Skip header if exists
                line = await reader.ReadLineAsync();
                
                while ((line = await reader.ReadLineAsync()) != null)
                {
                    if (string.IsNullOrWhiteSpace(line)) continue;
                    
                    var columns = line.Split(',');
                    if (columns.Length == 0) continue;
                    
                    string barcode = columns[0].Trim();
                    
                    var product = await _context.Products
                        .FirstOrDefaultAsync(p => p.Barcode.ToString() == barcode);
                    
                    if (product != null)
                    {
                        decimal price = GetProductPrice(product);

                        ProductLabel label = new ProductLabel()
                        {
                            BarcodeNumber = product.Barcode.ToString(),
                            Date = CustomTimeProvider.Now.Date.ToString("dd/MM/yyyy"),
                            Name = product.Name,
                            PLU = product.PLU.ToString(),
                            Price = price,
                            Volume = product.PurchasePackSize.ToString(),
                        };
                        labels.Add(label);
                    }
                }
            }

            if (labels.Count == 0)
            {
                TempData["Error"] = "No valid products found in the CSV file.";
                return RedirectToAction("UploadCsv");
            }

            TempData["Success"] = $"{labels.Count} product labels generated from CSV.";
            return View("PrintLabels", labels);
        }
    }
}


