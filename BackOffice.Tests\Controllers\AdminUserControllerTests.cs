using BackOffice.Controllers;
using BackOffice.ViewModels;
using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using Moq;
using POS.Core.Models;
using System.Collections.Generic;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;
using Xunit;

public class AdminUserControllerTests
{
    private readonly Mock<Data> _mockContext;
    private readonly AdminUserController _controller;
    private readonly List<User> _users;
    private readonly List<Store> _stores;
    private readonly List<IpAddress> _ipAddresses;

    public AdminUserControllerTests()
    {
        _users = new List<User>();
        _stores = new List<Store>();
        _ipAddresses = new List<IpAddress>();
        
        _mockContext = new Mock<Data>();

        // Setup Users DbSet
        var userDbSet = GetQueryableMockDbSet(_users);
        _mockContext.Setup(c => c.Users).Returns(userDbSet.Object);
        _mockContext.Setup(c => c.Admins).Returns(userDbSet.Object);
        _mockContext.Setup(c => c.Accountants).Returns(userDbSet.Object);
        _mockContext.Setup(c => c.BackOffices).Returns(userDbSet.Object);

        // Setup Stores DbSet
        var storeDbSet = GetQueryableMockDbSet(_stores);
        _mockContext.Setup(c => c.Stores).Returns(storeDbSet.Object);

        // Setup IpAddresses DbSet
        var ipAddressDbSet = GetQueryableMockDbSet(_ipAddresses);
        _mockContext.Setup(c => c.IpAddresses).Returns(ipAddressDbSet.Object);

        // Setup SaveChangesAsync
        _mockContext.Setup(x => x.SaveChangesAsync(It.IsAny<CancellationToken>()))
            .ReturnsAsync(1);

        _controller = new AdminUserController(_mockContext.Object);
    }

    private Mock<DbSet<T>> GetQueryableMockDbSet<T>(List<T> sourceList) where T : class
    {
        var queryable = sourceList.AsQueryable();
        var dbSet = new Mock<DbSet<T>>();

        dbSet.As<IQueryable<T>>().Setup(m => m.Provider).Returns(queryable.Provider);
        dbSet.As<IQueryable<T>>().Setup(m => m.Expression).Returns(queryable.Expression);
        dbSet.As<IQueryable<T>>().Setup(m => m.ElementType).Returns(queryable.ElementType);
        dbSet.As<IQueryable<T>>().Setup(m => m.GetEnumerator()).Returns(() => queryable.GetEnumerator());

        dbSet.Setup(d => d.Add(It.IsAny<T>())).Callback<T>((s) => sourceList.Add(s));
        dbSet.Setup(d => d.AddRange(It.IsAny<IEnumerable<T>>())).Callback<IEnumerable<T>>((s) => sourceList.AddRange(s));
        dbSet.Setup(d => d.Remove(It.IsAny<T>())).Callback<T>((s) => sourceList.Remove(s));

        // Setup async operations
        dbSet.Setup(x => x.FindAsync(It.IsAny<object[]>()))
            .Returns<object[]>(async (objects) => await Task.FromResult(sourceList.FirstOrDefault()));

        return dbSet;
    }

    [Fact]
    public async Task Create_ValidAdmin_CreatesUserAndReturnsRedirect()
    {
        // Arrange
        var model = new UserCreateViewModel 
        { 
            Name = "TestAdmin",
            Password = "password123",
            Role = "Admin"
        };

        // Act
        var result = await _controller.Create(model);

        // Assert
        var redirectResult = Assert.IsType<RedirectToActionResult>(result);
        Assert.Equal("Index", redirectResult.ActionName);
        Assert.Single(_users);
        var createdUser = _users.First() as Admin;
        Assert.NotNull(createdUser);
        Assert.Equal("TestAdmin", createdUser.Name);
        Assert.Equal("Admin", createdUser.Role);
    }

    [Fact]
    public async Task Create_BackOfficeWithoutStore_ReturnsViewWithError()
    {
        // Arrange
        var model = new UserCreateViewModel 
        { 
            Name = "TestBackOffice",
            Password = "password123",
            Role = "BackOffice",
            StoreId = null
        };

        _stores.Add(new Store { StoreId = 1, StoreName = "Test Store" });

        // Act
        var result = await _controller.Create(model);

        // Assert
        var viewResult = Assert.IsType<ViewResult>(result);
        Assert.False(_controller.ModelState.IsValid);
        Assert.Contains(_controller.ModelState["StoreId"].Errors, 
            e => e.ErrorMessage == "Store is required for BackOffice users.");
    }

    [Fact]
    public async Task Create_AccountantWithIpAddresses_CreatesUserWithIpAddresses()
    {
        // Arrange
        var model = new UserCreateViewModel 
        { 
            Name = "TestAccountant",
            Password = "password123",
            Role = "Accountant",
            IpAddresses = new List<string> { "***********", "***********" }
        };

        // Act
        var result = await _controller.Create(model);

        // Assert
        var redirectResult = Assert.IsType<RedirectToActionResult>(result);
        Assert.Equal("Index", redirectResult.ActionName);
        
        Assert.Single(_users);
        var createdUser = _users.First() as Accountant;
        Assert.NotNull(createdUser);
        Assert.Equal("TestAccountant", createdUser.Name);
        Assert.Equal("Accountant", createdUser.Role);

        Assert.Equal(2, _ipAddresses.Count);
        Assert.Contains(_ipAddresses, ip => ip.Address == "***********");
        Assert.Contains(_ipAddresses, ip => ip.Address == "***********");
    }

    [Fact]
    public async Task Edit_ValidUser_UpdatesUserAndReturnsRedirect()
    {
        // Arrange
        var existingUser = new Admin 
        { 
            Id = 1,
            Name = "OldName",
            Password = "oldpassword",
            Role = "Admin"
        };
        _users.Add(existingUser);

        var model = new UserEditViewModel
        {
            Id = 1,
            Name = "NewName",
            Role = "Admin"
        };

        _mockContext.Setup(m => m.Users.Find(It.IsAny<int>()))
            .Returns(existingUser);

        // Act
        var result = await _controller.Edit(model);

        // Assert
        var redirectResult = Assert.IsType<RedirectToActionResult>(result);
        Assert.Equal("Index", redirectResult.ActionName);
        Assert.Equal("NewName", existingUser.Name);
    }

    [Fact]
    public async Task Index_ReturnsViewWithUsers()
    {
        // Arrange
        var users = new List<User>
        {
            new Admin { Id = 1, Name = "Admin1", Role = "Admin" },
            new Accountant { Id = 2, Name = "Accountant1", Role = "Accountant" }
        };
        _users.AddRange(users);

        // Act
        var result = await _controller.Index();

        // Assert
        var viewResult = Assert.IsType<ViewResult>(result);
        var model = Assert.IsAssignableFrom<IEnumerable<User>>(viewResult.Model);
        Assert.Equal(2, model.Count());
    }
}

