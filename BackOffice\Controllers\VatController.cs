﻿using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using POS.Core.Models;
using BackOffice.ViewModels;
using System.Security.Claims;

namespace BackOffice.Controllers
{
    public class VatController : Controller
    {
        private readonly Data _context;

        public VatController(Data context)
        {
            _context = context;
        }

        // GET: Vat
        public async Task<IActionResult> Index()
        {
            return View(await _context.Vats.ToListAsync());
        }

        // GET: Vat/Details/5
        public async Task<IActionResult> Details(int? id)
        {
            if (id == null)
                return NotFound();

            var vat = await _context.Vats.FirstOrDefaultAsync(m => m.Id == id);
            if (vat == null)
                return NotFound();

            return View(vat);
        }

        // GET: Vat/Create
        public IActionResult Create()
        {
            return View();
        }

        // POST: Vat/Create
        [HttpPost]
        [ValidateAntiForgeryToken]
        public async Task<IActionResult> Create([Bind("Id,Code,Value")] Vat vat)
        {
            ModelState.Remove(nameof(vat.Products));

            if (ModelState.IsValid)
            {
                _context.Add(vat);
                await _context.SaveChangesAsync();
                return RedirectToAction(nameof(Index));
            }
            return View(vat);
        }

        // GET: Vat/Edit/5
        public async Task<IActionResult> Edit(int? id)
        {
            if (id == null)
                return NotFound();

            var vat = await _context.Vats.FindAsync(id);
            if (vat == null)
                return NotFound();

            return View(vat);
        }

        // POST: Vat/Edit/5
        [HttpPost]
        [ValidateAntiForgeryToken]
        public async Task<IActionResult> Edit(int id, [Bind("Id,Code,Value")] Vat vat)
        {
            if (id != vat.Id)
                return NotFound();

            ModelState.Remove(nameof(vat.Products));



            if (ModelState.IsValid)
            {
                try
                {
                    _context.Update(vat);
                    await _context.SaveChangesAsync();
                }
                catch (DbUpdateConcurrencyException)
                {
                    if (!VatExists(vat.Id))
                        return NotFound();
                    else
                        throw;
                }
                return RedirectToAction(nameof(Index));
            }
            return View(vat);
        }

        // GET: Vat/Delete/5
        public async Task<IActionResult> Delete(int? id)
        {
            if (id == null)
                return NotFound();

            var vat = await _context.Vats.FirstOrDefaultAsync(m => m.Id == id);
            if (vat == null)
                return NotFound();

            return View(vat);
        }

        // POST: Vat/Delete/5
        [HttpPost, ActionName("Delete")]
        [ValidateAntiForgeryToken]
        public async Task<IActionResult> DeleteConfirmed(int id)
        {
            var vat = await _context.Vats.FindAsync(id);
            if (vat != null)
            {
                _context.Vats.Remove(vat);
                await _context.SaveChangesAsync();
            }
            return RedirectToAction(nameof(Index));
        }

        private bool VatExists(int id)
        {
            return _context.Vats.Any(e => e.Id == id);
        }

        [Authorize(Roles = "Admin,BackOffice")]
        public async Task<IActionResult> Report()
        {
            return View(new List<VatReportViewModel>());
        }

        [HttpPost]
        [Authorize(Roles = "Admin,BackOffice")]
        public async Task<IActionResult> Report(DateTime startDate, DateTime endDate)
        {
            // Validate dates
            if (startDate > endDate)
            {
                ModelState.AddModelError("", "Start date must be before end date.");
                return View(new List<VatReportViewModel>());
            }

            // Get store ID for BackOffice users
            int? storeId = null;
            if (User.IsInRole("BackOffice"))
            {
                var userId = int.Parse(User.Claims.FirstOrDefault(c => c.Type == ClaimTypes.NameIdentifier)?.Value);
                storeId = _context.BackOffices.Find(userId)?.StoreId;
            }
            
            // Get store information for the report header
            string companyName = "";
            string vatNumber = "";
            string storeName = "";
            
            if (storeId.HasValue)
            {
                var store = _context.Stores.Find(storeId.Value);
                if (store != null)
                {
                    vatNumber = store.VatNo;
                    storeName = store.StoreName;
                    companyName = store.Company?.Name ?? "";
                }
            }
            
            var query = _context.Sales
                .Where(s => s.Date >= startDate && s.Date <= endDate);

            // Apply store filter for BackOffice users
            if (storeId.HasValue)
            {
                query = query.Where(s => s.ProductStoreSales
                    .Any(pss => pss.ProductStore.StoreId == storeId.Value));
            }

            // Get regular product sales
            var regularSales = query
                .SelectMany(s => s.ProductStoreSales)
                .Select(pss => new
                {
                    pss.Total,
                    SubCategory = pss.ProductStore.Product.Division.SubCatagory,
                    Vat = pss.ProductStore.Product.Vat,
                    IsSpecial = false
                })
                .Where(x => x.Vat != null);

            // Get special product sales
            var specialSales = query
                .SelectMany(s => s.SpecialProductSales)
                .Select(sps => new
                {
                    Total = sps.Amount,
                    SubCategory = sps.Product.Division.SubCatagory,
                    Vat = sps.Product.Vat,
                    IsSpecial = true
                })
                .Where(x => x.Vat != null);

            // Combine both regular and special sales
            var combinedSales = regularSales.Concat(specialSales);

            var result = combinedSales
                .GroupBy(x => new { x.Vat.Code, x.SubCategory.Name, x.IsSpecial })
                .Select(g => new
                {
                    VatCode = g.Key.Code,
                    SubCategoryName = g.Key.Name,
                    IsSpecial = g.Key.IsSpecial,
                    SalesIncVat = g.Sum(x => x.Total),
                    SalesExcVat = g.Sum(x => x.Total / (1 + x.Vat.Value/100)),
                    VatAmount = g.Sum(x => x.Total - (x.Total / (1 + x.Vat.Value/100)))
                })
                .ToList();

            var reportData = result
                .GroupBy(d => d.VatCode)
                .Select(g => new VatReportViewModel
                {
                    VatCode = g.Key,
                    SubCategories = g.Select(sc => new VatReportSubCategory
                    {
                        SubCategoryName = sc.SubCategoryName,
                        IsSpecial = sc.IsSpecial,
                        SalesIncVat = sc.SalesIncVat,
                        SalesExcVat = sc.SalesExcVat,
                        VatAmount = sc.VatAmount
                    }).ToList()
                })
                .ToList();

            // Add the header information to ViewBag
            ViewBag.CompanyName = companyName;
            ViewBag.VatNumber = vatNumber;
            ViewBag.StoreName = storeName;
            ViewBag.StartDate = startDate;
            ViewBag.EndDate = endDate;
            
            // Calculate totals for each VAT code
            var vatCodeTotals = new List<VatCodeTotal>();
            decimal grandTotalIncVat = 0;
            decimal grandTotalExcVat = 0;
            decimal grandTotalVatAmount = 0;
            
            foreach (var vatGroup in reportData)
            {
                decimal totalIncVat = vatGroup.SubCategories.Sum(sc => sc.SalesIncVat);
                decimal totalExcVat = vatGroup.SubCategories.Sum(sc => sc.SalesExcVat);
                decimal totalVatAmount = vatGroup.SubCategories.Sum(sc => sc.VatAmount);
                
                vatGroup.TotalIncVat = totalIncVat;
                vatGroup.TotalExcVat = totalExcVat;
                vatGroup.TotalVatAmount = totalVatAmount;
                
                vatCodeTotals.Add(new VatCodeTotal
                {
                    VatCode = vatGroup.VatCode,
                    TotalIncVat = totalIncVat,
                    TotalExcVat = totalExcVat,
                    TotalVatAmount = totalVatAmount
                });
                
                grandTotalIncVat += totalIncVat;
                grandTotalExcVat += totalExcVat;
                grandTotalVatAmount += totalVatAmount;
            }
            
            ViewBag.VatCodeTotals = vatCodeTotals;
            ViewBag.GrandTotalIncVat = grandTotalIncVat;
            ViewBag.GrandTotalExcVat = grandTotalExcVat;
            ViewBag.GrandTotalVatAmount = grandTotalVatAmount;
            
            return View(reportData);
        }
    }
}
