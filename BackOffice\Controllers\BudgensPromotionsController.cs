﻿using Microsoft.AspNetCore.Components.Web;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using POS.Core.Models;
using System;
using System.Collections.Generic;
using System.Globalization;
using System.IO;
using System.Net.Http;
using System.Text.RegularExpressions;
using System.Threading.Tasks;

namespace BackOffice.Controllers
{
    public class BudgensPromotionsController : Controller
    {
        public IActionResult Index()
        {
            return View();
        }

        private readonly Data _data;

        public BudgensPromotionsController(HttpClient httpClient, Data data)
        {
            _data = data;
        }


        [HttpPost]
        public async Task<IActionResult> UploadFile(IFormFile file)
        {
            List<Promotion> promotions = new List<Promotion>();

            try
            {

                if (file == null || file.Length == 0)
                {
                    ViewData["Message"] = "Please select a file.";
                    return View("Index");
                }

                var lines = new List<string>();

                try
                {
                    using (var reader = new StreamReader(file.OpenReadStream()))
                    {
                        Promotion promotion = null;



                        string line;
                        while ((line = reader.ReadLine()) != null)
                        {
                            try
                            {
                                if (line.StartsWith("01"))
                                {
                                    if (promotion != null)
                                    {
                                        promotions.Add(promotion);
                                    }



                                    promotion = new Promotion();

                                    //extract the text that starts after word "ADD"
                                    //and ends before a whitespace which is more than
                                    //a single space
                                    string[] parts = line.Split(new string[] { "ADD" }, StringSplitOptions.None);
                                    if (parts.Length > 1)
                                    {
                                        string textAfterAdd = parts[1].Trim();
                                        int firstSpaceIndex = textAfterAdd.IndexOf("  ");
                                        if (firstSpaceIndex != -1)
                                        {
                                            promotion.Name = textAfterAdd.Substring(0, firstSpaceIndex);
                                        }


                                    }


                                    //take substrings of line
                                    //by splitting it by the space character
                                    //then take the 5th substring from the end

                                    string[] parts2 = line.Split(' ');
                                    if (parts2.Length >= 5)
                                    {
                                        string promotionTypeno = parts2[parts2.Length - 5];
                                        if (promotionTypeno == "3")
                                        {
                                            promotion.Type = PromotionType.XForY;
                                        }
                                        else if (promotionTypeno == "4")
                                        {
                                            promotion.Type = PromotionType.BuyOneGetOneFree;
                                        }

                                    }




                                    //extract the last occurance of a number of the format 00000002.50
                                    //8 digits before the decimal point.
                                    //here 2.50 has to be extracted
                                    string pattern = @"\b\d{8}\.\d{2}\b";
                                    MatchCollection matches = Regex.Matches(line, pattern);
                                    if (matches.Count > 0)
                                    {
                                        string lastMatch = matches[matches.Count - 1].Value;
                                        promotion.PromotionPrice = decimal.Parse(lastMatch.Substring(lastMatch.IndexOf('.') - 1));
                                    }



                                    //time string
                                    string timepattern = @"\b\d{8} \d{4} \d{8} \d{4}\b";

                                    Match match = Regex.Match(line, timepattern);

                                    if (match.Success)
                                    {
                                        string timeString = match.Value;

                                        string startDayString = timeString.Split(" ")[0];
                                        string startTimeString = timeString.Split(" ")[1];
                                        string endDayString = timeString.Split(" ")[2];
                                        string endTimeString = timeString.Split(" ")[3];

                                        string startinput = $"{startDayString} {startTimeString}";
                                        string endinput = $"{endDayString} {endTimeString}";

                                        string format = "yyyyMMdd HHmm"; // Define the format

                                        DateTime startTime = DateTime.ParseExact(startinput, format, CultureInfo.InvariantCulture);
                                        DateTime endTime = DateTime.ParseExact(endinput, format, CultureInfo.InvariantCulture);

                                        promotion.StartTime = startTime;
                                        promotion.EndTime = endTime;
                                    }
                                    else
                                    {
                                    }

                                    promotion.Identifire = line.Split(' ').ToList()[1];


                                }
                                if (line.StartsWith("02"))
                                {
                                    //extract the last number after a space
                                    string[] parts = line.Split(' ');
                                    if (parts.Length > 0)
                                    {
                                        string lastPart = parts[parts.Length - 1];
                                        if (int.TryParse(lastPart, out int result))
                                        {
                                            promotion.Quantity = result;
                                        }
                                    }

                                }
                                if (line.StartsWith("03"))
                                {
                                    //extract the substring with 13 digits
                                    //its space before and after it

                                    string pattern = @"\s\d{13}\s";
                                    Match match = Regex.Match(line, pattern);
                                    if (match.Success)
                                    {
                                        string barcode = match.Value.Trim();

                                        var product =_data.Products.FirstOrDefault(p => p.Barcode == barcode);
                                        if (product != null)
                                        {
                                            promotion.Products.Add(product);
                                        }

                                    }

                                    


                                }
                            }
                            catch (Exception ex)
                            {
                                Console.WriteLine($"Error parsing line: {ex.Message}");
                            }
                        }
                    }


                    //TODO : have to filter by promotion id
                    foreach (var promotion in promotions)
                    {
                        if (_data.Promotions.Any(p => p.Identifire == promotion.Identifire))
                            continue;

                        _data.Promotions.Add(promotion);
                    }

                    _data.SaveChanges();


                    ViewData["Message"] = "Process Successful!";
                    return View("Index");
                }
                catch (Exception ex)
                {
                    ViewData["Message"] = "Error reading file: " + ex.Message;
                    return View("Index");

                }

            }
            catch (Exception ex)
            {
                ViewData["Message"] = "Error : " + ex.Message;
                return View("Index");
            }




        }
    }
}
