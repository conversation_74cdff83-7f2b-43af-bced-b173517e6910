@{
    ViewData["Title"] = "Upload CSV for Labels";
}

<div class="container mt-4">
    <h2>Upload CSV File for Product Labels</h2>
    
    @if (TempData["Error"] != null)
    {
        <div class="alert alert-danger">@TempData["Error"]</div>
    }
    
    <div class="card mt-3">
        <div class="card-body">
            <p>Upload a CSV file with product barcodes in the first column.</p>
            <form method="post" enctype="multipart/form-data">
                <div class="form-group">
                    <label for="file">Select CSV File:</label>
                    <input type="file" id="file" name="file" class="form-control-file" accept=".csv" required />
                </div>
                <button type="submit" class="btn btn-primary mt-3">Upload and Generate Labels</button>
            </form>
        </div>
    </div>
    
    <div class="mt-3">
        <a href="@Url.Action("SelectProduct")" class="btn btn-secondary">Back to Product Selection</a>
    </div>
</div>