//Accountant.cs
---------------
using System.ComponentModel.DataAnnotations.Schema;
using System.Text.Json;

namespace POS.Core.Models
{
    public class Accountant : User
    {
        public List<IpAddress> IpAddresses { get; set; } = new List<IpAddress>();
    }

    

}


//Admin.cs
----------
namespace POS.Core.Models
{
    public class Admin : User
    {
    }

  

}


//BackOffice.cs
---------------
using System.ComponentModel.DataAnnotations.Schema;
using System.Text.Json;

namespace POS.Core.Models
{
    public class BackOffice : User
    {
        public List<IpAddress> IpAddresses { get; set; } = new List<IpAddress>();

        public Store? Store { get; set; }

        public int? StoreId { get; set; }
    }

  

}




//BaseEntity.cs
---------------
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace POS.Core.Models
{
    public abstract class BaseEntity
    {
        public bool IsDeleted { get; set; } = false;
        public DateTime? LastModified { get; set; }
    }
}


//Brand.cs
----------
using System.ComponentModel.DataAnnotations;

namespace POS.Core.Models
{
    public class Brand :BaseEntity
    {
        public int Id { get; set; }
        public string Name { get; set; }

        [Display(Name = "Brand Logo")]
        public string? BrandLogo { get; set; } // Stores the file path of the logo

        public ICollection<Product> Products { get; set; }

        public ICollection<Store> Stores  { get; set; }

       
      
    }

   

}


//Cashier.cs
------------
namespace POS.Core.Models
{
    public class Cashier : User
    {
        public ICollection<CashierStore> CashierStores { get; set; } = new HashSet<CashierStore>();
    }

  

}


//CashierStore.cs
-----------------
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace POS.Core.Models
{
    public class CashierStore : BaseEntity
    {
        public Cashier Cashier { get; set; }

        public int CashierId { get; set; }

        public Store Store { get; set; }
        public int StoreId { get; set; }


    }
}


//Catagory.cs
-------------
namespace POS.Core.Models
{
    public enum Catagory
    {
        Fuel,
        ShopSales,
        Excempt
    }


}


//Company.cs
------------
namespace POS.Core.Models
{
    public class Company : BaseEntity
    {
        public int Id { get; set; }
        public string  Name { get; set; }

        public string Address { get; set; }

        public ICollection<Store> Stores { get; set; }

       
      
    }

  

}


//Data.cs
---------
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.SqlServer.Migrations.Internal;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Runtime.Serialization.Json;
using System.Security.Principal;
using System.Text;
using System.Threading.Tasks;

namespace POS.Core.Models
{
    public class Data : DbContext
    {
        public DbSet<Store> Stores { get; set; }
        public DbSet<Product> Products { get; set; }

        public DbSet<Brand> Brands { get; set; }

        public DbSet<ProductStore> ProductStores { get; set; }
        public DbSet<Sale> Sales { get; set; }

        public DbSet<StoreDay> StoreDays { get; set; }

        public DbSet<ProductStoreSale> ProductStoreSales { get; set; }

        public DbSet<SpecialProductSale> SpecialProductSales { get; set; }
        public DbSet<Cashier> Cashiers { get; set; }

        public DbSet<User> Users { get; set; }

        public DbSet<Admin> Admins { get; set; }
        public DbSet<BackOffice> BackOffices { get; set; }
        public DbSet<Accountant> Accountants { get; set; }


        public DbSet<IpAddress> IpAddresses { get; set; }


        public DbSet<SafeDrop> SafeDrops { get; set; }

        public DbSet<PaymentMethod> PaymentMethods { get; set; }

        public DbSet<Shift> shifts { get; set; }

        public DbSet<PaidOut> PaidOuts { get; set; }

        public DbSet<PaidOutOption> PaidOutOptions { get; set; }

        public DbSet<Company> Companies { get; set; }

        public DbSet<Vat> Vats { get; set; }

        public DbSet<SubCatagory> SubCatagories { get; set; }

        public DbSet<PosMachine> PosMachines { get; set; }

        public DbSet<Division> Divisions { get; set; }

        public DbSet<Refund> Refunds { get; set; }

        public DbSet<EmailSubscription> EmailSubscriptions { get; set; }

        public DbSet<NightEmail> NightEmails { get; set; }


        public DbSet<Promotion> Promotions { get; set; }

        public DbSet<SalePaymentMethod2> SalePaymentMethods { get; set; }

        public DbSet<NextOrderProduct> NextOrderProducts { get; set; }

        public DbSet<MorrisonOrder> MorrisonOrders { get; set; }

        public DbSet<MorrisonsOrderItem> MorrisonsOrderItems { get; set; }

        public DbSet<VoidSaleItem> VoidSaleItems { get; set; }

        public DbSet<Void> Voids { get; set; }

        public DbSet<DeletedSaleItem> DeletedSaleItems { get; set; }

        protected override void OnModelCreating(ModelBuilder modelBuilder)
        {
         //   modelBuilder.Entity<ProductStore>()
         //.HasIndex(ps => new { ps.StoreId, ps.ProductId })
         //.IsUnique();

            modelBuilder.Entity<SalePaymentMethod2>()
       .HasKey(spm => new { spm.SaleId, spm.PaymentMethodId });

            modelBuilder.Entity<SalePaymentMethod2>()
                .HasOne(spm => spm.Sale)
                .WithMany(s => s.SalePaymentMethods)
                .HasForeignKey(spm => spm.SaleId);

            modelBuilder.Entity<SalePaymentMethod2>()
                .HasOne(spm => spm.PaymentMethod)
                .WithMany(pm => pm.SalePaymentMethods)
                .HasForeignKey(spm => spm.PaymentMethodId);

            modelBuilder.Entity<CashierStore>()
        .HasKey(cs => new { cs.StoreId, cs.CashierId });

            modelBuilder.Entity<CashierStore>()
                .HasOne(sc => sc.Cashier)
                .WithMany(s => s.CashierStores)
                .HasForeignKey(sc => sc.CashierId);

            modelBuilder.Entity<CashierStore>()
                .HasOne(sc => sc.Store)
                .WithMany(c => c.CashierStores)
                .HasForeignKey(sc => sc.StoreId);


            base.OnModelCreating(modelBuilder);
        }


        public override int SaveChanges()
        {
            ApplyAuditInfo();
            return base.SaveChanges();
        }

        public override Task<int> SaveChangesAsync(CancellationToken cancellationToken = default)
        {
            ApplyAuditInfo();
            return base.SaveChangesAsync(cancellationToken);
        }

        private void ApplyAuditInfo()
        {
            var entries = ChangeTracker.Entries<BaseEntity>();

            foreach (var entry in entries)
            {
                if (entry.State == EntityState.Added || entry.State == EntityState.Modified)
                {
                    entry.Entity.LastModified = TimeManagement.CustomTimeProvider.Now;
                }

                if (entry.State == EntityState.Deleted)
                {
                    entry.State = EntityState.Modified;
                    entry.Entity.IsDeleted = true;
                    entry.Entity.LastModified = TimeManagement.CustomTimeProvider.Now;
                }
            }
        }

        //public Data(DbContextOptions<Data> options) : base(options)
        //{

        //}

        protected override void OnConfiguring(DbContextOptionsBuilder optionsBuilder)
        {
            // Add connection string here (for non-DI scenarios)
            optionsBuilder.UseSqlServer("Server=103.70.137.109\\SQLEXPRESS,1433;TrustServerCertificate=True;Database=POS6;User Id=appUser;Password=***************");
            //optionsBuilder.UseSqlServer("Data Source=(localdb)\\mssqllocaldb;Initial Catalog=POS1;Integrated Security=True;");

        }
    }

}


//Delete.cs
-----------
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace POS.Core.Models
{
    public class DeletedSaleItem : BaseEntity
    {
        public int Id { get; set; }

        public string PLU { get; set; }

        public int Qty { get; set; }

        public decimal Total { get; set; }

        public int StoreId { get; set; }

        public int PosId { get; set; }

        public DateTime DeletedTime { get; set; }
       
      


    }

    public class Void : BaseEntity
    {
        public int Id { get; set; }

        public int StoreId { get; set; }

        public int PosId { get; set; }

        public DateTime VoidTime { get; set; }

        public ICollection<VoidSaleItem> VoidSaleItems { get; set; } = new List<VoidSaleItem>(); // Collection of voided sale items>
       
      
    }


    public class VoidSaleItem : BaseEntity
    {
        public int Id { get; set; }
        public string PLU { get; set; }

        public int Qty { get; set; }

        public decimal Total { get; set; }
       
      
    }
   

}


//Division.cs
-------------
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace POS.Core.Models
{
    public class Division : BaseEntity
    {
        public int Id { get; set; }
        public string Name { get; set; }
        public SubCatagory SubCatagory { get; set; }

        public int SubCatagoryId { get; set; }


        public ICollection<Product> Products { get; set; } = new List<Product>(); // Navigation Property <==>


       

      
    }

}


//EmailSubscription.cs
----------------------
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace POS.Core.Models
{
    public class EmailSubscription : BaseEntity
    {
        public int Id { get; set; }
        public string Email { get; set; }
       
      
    }
  

}


//IdMapping.cs
--------------
using System;

namespace POS.Core.Models
{
    public class IdMapping : BaseEntity
    {
        public string EntityType { get; set; }  // e.g., "Sale", "Product"
        public int LocalId { get; set; }        // ID in local SQLite database
        public int RemoteId { get; set; }       // ID in remote SQL Server database
        public DateTime MappedAt { get; set; }  // When the mapping was created
    }
} 

//IpAddress.cs
--------------
namespace POS.Core.Models
{
    public class IpAddress : BaseEntity
    {
        public int Id { get; set; }
        public string Address { get; set; }
        public int UserId { get; set; }
        public User User { get; set; }
       
      
    }


    

}


//MorrisonOrder.cs
------------------
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace POS.Core.Models
{
    public class MorrisonOrder : BaseEntity
    {
        public int Id { get; set; }

        public string GeneratedId { get; set; }

        public List<MorrisonsOrderItem> OrderItems { get; set; } = new List<MorrisonsOrderItem>();

        public DateTime CreatedDate  { get; set; }

        public DateTime ExpectedDeliveryDate { get; set; }

        public Store Store { get; set; }

        public int StoreId { get; set; }

        public MorrisonOrderStatus MorrisonOrderStatus { get; set; }

       
      
    }


    public enum MorrisonOrderStatus
    {
        Created,
        Confirmed,
        Delivered
    }
   

}


//MorrisonsOrderItem.cs
-----------------------
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace POS.Core.Models
{
    public class MorrisonsOrderItem : BaseEntity
    {
        public int Id { get; set; }

        public string Barcode { get; set; }

        public int Quantity { get; set; }

        public MorrisonOrder MorrisonOrder { get; set; }

        public int MorrisonOrderId { get; set; }

       
      
    }
    

}


//NextOrderProduct.cs
---------------------
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace POS.Core.Models
{
    public class NextOrderProduct : BaseEntity
    {
        public int Id { get; set; }
        public string Barcode { get; set; }
        public int Quantity { get; set; }

        public Store Store { get; set; }
        public int  StoreId { get; set; }

       
      
    }


}


//NightEmail.cs
---------------
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace POS.Core.Models
{
    public class NightEmail : BaseEntity
    {
        public int Id { get; set; }

        public bool Enabled { get; set; }

       
      
    }
  

}


//PaidOut.cs
------------
namespace POS.Core.Models
{
    public class PaidOut : BaseEntity
    {
        public int Id { get; set; }
        public decimal Amount { get; set; }
        public DateTime Time { get; set; }
        public int ShiftId { get; set; }
        public Shift Shift { get; set; }

        public Store Store { get; set; }
        public int? StoreId { get; set; }

        public PaidOutOption PaidOutOption { get; set; }
        public int? PaidOutOptionId { get; set; }
       
      


    }

    

}


//PaidOutOption.cs
------------------
namespace POS.Core.Models
{
    public class PaidOutOption : BaseEntity
    {
        public int Id { get; set; }
        public string Option { get; set; }
       
      
    }



}


//PaymentMethod.cs
------------------
namespace POS.Core.Models
{
    public class PaymentMethod : BaseEntity
    {
        public int Id { get; set; }
        public string Method { get; set; }

        // Navigation property for many-to-many
        public ICollection<SalePaymentMethod2> SalePaymentMethods { get; set; }
       
      
    }

  

}


//PendingSyncItem.cs
--------------------
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace POS.Core.Models
{
    public class PendingSyncItem
    {
        public int Id { get; set; }
        public string EntityType { get; set; } // e.g., "Sale", "Product", etc.
        public string PayloadJson { get; set; } // Entity serialized to JSON
        public DateTime CreatedAt { get; set; } = DateTime.Now;
        public bool IsSynced { get; set; } = false;
        public DateTime? SyncedAt { get; set; }
        public string SyncError { get; set; }
        public int RetryCount { get; set; }
        public string OperationType { get; set; } // "Insert", "Update", "Delete"
    }
}


//PosMachine.cs
---------------
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace POS.Core.Models
{
    public class PosMachine : BaseEntity
    {
        public int Id { get; set; }

        public Store Store { get; set; }

        public int StoreId { get; set; }

       
      
    }
    

}


//Product.cs
------------
using System.Runtime.CompilerServices;

namespace POS.Core.Models
{
    public class Product : BaseEntity
    {
        public int Id { get; set; }
        public string Name { get; set; }

        public string PLU { get; set; }

        public string Barcode { get; set; }

        public string Description { get; set; }

        public decimal PurchasePrice { get; set; }

        public decimal SellingPrice { get; set; }

        public decimal PurchasePackSize { get; set; }


        public Division Division { get; set; }

        public int DivisionId { get; set; }


        public Vat Vat { get; set; }

        public int VatId { get; set; }


        public Brand? Brand { get; set; }
        public int? BrandId { get; set; }

        /// <summary>
        /// This is for Hot foods , Paypoint and News Papers
        /// </summary>
        public bool SpecialProduct { get; set; }

        public Promotion? Promotion { get; set; }

        public int? PromotionId { get; set; }

        public ICollection<ProductStore> ProductStores { get; set; } = new List<ProductStore>();

       

      
    }


}


//ProductStore.cs
-----------------
using System.Reflection.Metadata.Ecma335;

namespace POS.Core.Models
{
    public class ProductStore : BaseEntity
    {
        public int ProductStoreId { get; set; }
        public int ProductId { get; set; }
        public Product Product { get; set; }
        public int StoreId { get; set; }
        public Store Store { get; set; }
        public int InventoryCount { get; set; }

        // Navigation Properties
        public ICollection<ProductStoreSale> ProductStoreSales { get; set; }

        /// <summary>
        /// Store specific price
        /// </summary>
        public decimal? StoreSpecificPrice { get; set; }


       

      

    }


}


//ProductStoreSale.cs
---------------------
using Microsoft.EntityFrameworkCore.Metadata;

namespace POS.Core.Models
{
    public class ProductStoreSale : BaseEntity
    {
        public int ProductStoreSaleId { get; set; }
        public int ProductStoreId { get; set; }
        public ProductStore ProductStore { get; set; }
        public int SaleId { get; set; }
        public Sale Sale { get; set; }
        public int Quantity { get; set; } // Quantity of products sold

        public bool PriceOverriden { get; set; }

        public bool StaffDiscount { get; set; }

        

        public bool IsPromotion { get; set; }

        public Promotion? Promotion { get; set; }

        public int? PromotionId { get; set; }

        public decimal Total { get; set; } // unit price * quantity or overriden price
        public decimal StaffDiscountedAmount { get; set; }
        public decimal OverriddenDiscountAmount { get; set; }

       
      
    }

   

}


//Promotion.cs
--------------
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace POS.Core.Models
{
    public enum PromotionType
    {
        XForY,
        BuyOneGetOneFree,
        SingleProductPromotion
    }

    public class Promotion : BaseEntity
    {
        public int Id { get; set; }

        /// <summary>
        /// got from the textfile
        /// </summary>
        public string Identifire { get; set; }

        public string Name { get; set; }
        public PromotionType Type { get; set; }
        public ICollection<Product> Products { get; set; } = new List<Product>();
        public DateTime StartTime { get; set; }
        public DateTime EndTime { get; set; }

        /// <summary>
        /// For promotions like "2 Coke for $3" , this will be $3
        /// </summary>
        public decimal? PromotionPrice { get; set; } 
        public int Quantity { get; set; } // For promotions like "Buy 2, Get 1 Free"


       

      
    }
 

}


//Refund.cs
-----------
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace POS.Core.Models
{
    public class Refund : BaseEntity
    {
        public int Id { get; set; }
        public ProductStoreSale ProductStoreSale { get; set; }
        public int  ProductStoreSaleId { get; set; }

        public int Quantity { get; set; }

        public decimal Amount { get; set; }

        public DateTime Date { get; set; }

       
      
    }


}


//RefundPayment.cs
------------------
using System;

namespace POS.Core.Models
{
    public class RefundPayment : BaseEntity
    {
        public int Id { get; set; }
        public int RefundId { get; set; }
        public Refund Refund { get; set; }
        public int PaymentMethodId { get; set; }
        public PaymentMethod PaymentMethod { get; set; }
        public decimal Amount { get; set; }
        public DateTime Date { get; set; }
        public bool IsDeleted { get; set; }
        public DateTime LastModified { get; set; }
    }
} 

//SafeDrop.cs
-------------
namespace POS.Core.Models
{
    public class SafeDrop : BaseEntity
    {
        public int Id { get; set; }

        public decimal Amount { get; set; }

        public DateTime Time { get; set; }

        public int StoreId { get; set; }
        public Store Store { get; set; }

        public int shiftId { get; set; }
        public Shift Shift { get; set; }

       
      

    }

   

}


//Sale.cs
---------
namespace POS.Core.Models
{
    public class Sale : BaseEntity
    {
        public int SaleId { get; set; }
        public DateTime Date { get; set; }
        public decimal TotalValue { get; set; }
        public decimal Cashback { get; set; }
        public bool IsOnHold { get; set; }
        public ICollection<ProductStoreSale> ProductStoreSales { get; set; }
        public ICollection<SpecialProductSale> SpecialProductSales { get; set; }

        // Navigation property for many-to-many
        public ICollection<SalePaymentMethod2> SalePaymentMethods { get; set; }

        public decimal? CashTendered { get; set; }
        public string? Barcode { get; set; }
        public Shift Shift { get; set; }
        public int ShiftId { get; set; }

       
      
    }

  




}


//SaleItem.cs
-------------
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace POS.Core.Models
{
    

}


//SalePaymentMethod2.cs
-----------------------
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace POS.Core.Models
{
    public class SalePaymentMethod2 : BaseEntity
    {
        public int Id { get; set; }

        public int SaleId { get; set; }
        public Sale Sale { get; set; }

        public int PaymentMethodId { get; set; }
        public PaymentMethod PaymentMethod { get; set; }

        public decimal Amount { get; set; } // Optional: If you want to track how much was paid using each method

       
      
    }
 

}


//Shift.cs
----------
namespace POS.Core.Models
{
    public class Shift : BaseEntity
    {
        public int Id { get; set; }
        public DateTime StartTime { get; set; }
        public DateTime EndTime { get; set; }
        public int? CashierId { get; set; }
        public Cashier? Cashier { get; set; }

        public StoreDay? StoreDay { get; set; }

        public int? StoreDayId { get; set; }


        public PosMachine? PosMachine { get; set; }

        public int? PosMachineId { get; set; }


        public bool InProgress { get; set; }

       
      
    }



}


//SpecialProductSale.cs
-----------------------
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace POS.Core.Models
{
    public class SpecialProductSale : BaseEntity
    {
        public int Id { get; set; }
        public decimal Amount { get; set; }
        public Product Product { get; set; }
        public int ProductId { get; set; }

        public Sale Sale { get; set; }

        public int SaleId { get; set; }

       
      
    }
 

}


//StockType.cs
--------------
namespace POS.Core.Models
{
    public enum StockType
    {
        Stock,
        Instant
    }

    
}


//Store.cs
----------
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace POS.Core.Models
{

    public class Store : BaseEntity
    {
        public int StoreId { get; set; }
        public string StoreName { get; set; }

        // Navigation Properties
        public ICollection<ProductStore> ProductStores { get; set; } = new List<ProductStore>();

        public ICollection<CashierStore> CashierStores { get; set; } = new List<CashierStore>();

        public int? CompanyId { get; set; }
        public Company? Company { get; set; }

        public Brand Brand { get; set; }
        public int? BrandId { get; set; }

        public string? VatNo { get; set; }

        public string? StoreIdForAPI { get; set; }

        public ICollection<PosMachine> PosMachines { get; set; } = new List<PosMachine>();

        public bool AllowNegativeInventory { get; set; }

        public int? MainPosMachineId { get; set; }

       
      
    }


}


//StoreDay.cs
-------------
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace POS.Core.Models
{
    public class StoreDay : BaseEntity
    {
        public int Id { get; set; }

        public Store Store { get; set; }

        public int StoreId { get; set; }

        public DateTime StartTime { get; set; }

        public DateTime EndTime { get; set; }

        public ICollection<Shift> Shifts { get; set; }

        public bool InProgress { get; set; }

       
      


    }

}


//SubCatagory.cs
----------------
namespace POS.Core.Models
{
    public class SubCatagory : BaseEntity
    {
        public int Id { get; set; }
        public string Name { get; set; }

        public Catagory Catagory { get; set; }

        public StockType StockType { get; set; }
        public string? CashierAlert { get; set; }

        public ICollection<Division> Divisions { get; set; }


       

      
    }

}


//SyncedTime.cs
---------------
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace POS.Core.Models
{
    public class SyncedTime
    {
        public int Id { get; set; }

        public DateTime Time { get; set; }
    }
}


//User.cs
---------
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace POS.Core.Models
{
    public class User : BaseEntity
    {
        public int Id { get; set; }

        public string? UserId { get; set; }

        public string Name { get; set; }

        public string Password { get; set; }

        public string? Role { get; set; } // "Admin", "Accountant", "BackOffice"
       
      
    }
    

}


//Vat.cs
--------
namespace POS.Core.Models
{
    public class Vat : BaseEntity
    {
        public int Id { get; set; }
        public string Code { get; set; }

        public string? Rate { get; set; }
        public decimal Value { get; set; }

        public ICollection<Product> Products { get; set; }

       
      
    }

   

}


