﻿<UserControl x:Class="POS.WPF.Views.NumericKeypad"
             xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006" 
             xmlns:d="http://schemas.microsoft.com/expression/blend/2008" 
             xmlns:local="clr-namespace:POS.WPF.Views"
             mc:Ignorable="d" 
             d:DesignHeight="450" d:DesignWidth="800">
    <UserControl.Resources>
        <Style TargetType="Button">
            <Setter Property="FontSize" Value="18"/>
            <Setter Property="Foreground" Value="White"/>
            <Setter Property="Background" Value="#004e7c"/>
            <Setter Property="Margin" Value="2"/>
        </Style>
    </UserControl.Resources>
    <UniformGrid x:Name="UniformGrid" Rows="5" Columns="3" Background="#083c55">
        <Button Content="7"/>
        <Button Content="8"/>
        <Button Content="9"/>
        <Button Content="4"/>
        <Button Content="5"/>
        <Button Content="6"/>
        <Button Content="1"/>
        <Button Content="2"/>
        <Button Content="3"/>
        <Button Content="0"/>
        <Button Content="."/>
        <Button Content="00"/>
        <Button Content="Backspace"/>
        <Button Content="%"/>
        <Button Content="Clear"/>
    </UniformGrid>
            
</UserControl>
