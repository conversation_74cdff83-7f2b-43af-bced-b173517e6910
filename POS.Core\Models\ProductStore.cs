using System.Reflection.Metadata.Ecma335;

namespace POS.Core.Models
{
    public class ProductStore : BaseEntity
    {
        public int ProductStoreId { get; set; }
        public int ProductId { get; set; }
        public Product Product { get; set; }
        public int StoreId { get; set; }
        public Store Store { get; set; }
        public int InventoryCount { get; set; }

        // Navigation Properties
        public ICollection<ProductStoreSale> ProductStoreSales { get; set; }

        /// <summary>
        /// Store specific price
        /// </summary>
        public decimal? StoreSpecificPrice { get; set; }


       

      

    }


}
