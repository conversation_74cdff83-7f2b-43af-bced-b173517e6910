﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Windows.Controls;
using System.Windows.Documents;
using System.Windows;
using System.Printing;
using System.IO;
using System.Drawing.Printing;
using System.Drawing;


namespace POS.WPF.Helpers
{
    public class Print
    {
        //89089098
        public static void Print1()
        {
            PrintDocument printDoc = new PrintDocument();
            printDoc.PrinterSettings.PrinterName = "Microsoft Print to PDF";

            printDoc.PrintPage += (sender, e) =>
            {
                // Draw text on the page
                e.Graphics.DrawString("Hello, this is a test for virtual printing.",
                                      new Font("Arial", 12),
                                      Brushes.Black,
                                      new PointF(100, 100));
            };

            try
            {
                printDoc.Print();
                Console.WriteLine("Print job sent successfully!");
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Error during printing: {ex.Message}");
            }
        }
    }
}
