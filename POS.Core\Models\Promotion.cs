using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace POS.Core.Models
{
    public enum PromotionType
    {
        XForY,
        BuyOneGetOneFree,
        SingleProductPromotion
    }

    public class Promotion : BaseEntity
    {
        public int Id { get; set; }

        /// <summary>
        /// got from the textfile
        /// </summary>
        public string Identifire { get; set; }

        public string Name { get; set; }
        public PromotionType Type { get; set; }
        public ICollection<Product> Products { get; set; } = new List<Product>();
        public DateTime StartTime { get; set; }
        public DateTime EndTime { get; set; }

        /// <summary>
        /// For promotions like "2 Coke for $3" , this will be $3
        /// </summary>
        public decimal? PromotionPrice { get; set; } 
        public int Quantity { get; set; } // For promotions like "Buy 2, Get 1 Free"


       

      
    }
 

}
