﻿@{
    ViewData["Title"] = "Home Page";
}

<div class="text-center">

    <a style="width: 100%;margin-bottom: 0.5rem" asp-controller="Brand" asp-action="Index" class="btn btn-primary btn-block">Brand</a>
    <a style="width: 100%;margin-bottom: 0.5rem" asp-controller="Promotion" asp-action="Index" class="btn btn-primary btn-block">Promotions</a>

    <a style="width: 100%;margin-bottom: 0.5rem" asp-controller="Company" asp-action="Index" class="btn btn-primary btn-block">Company</a>
    <a style="width: 100%;margin-bottom: 0.5rem" asp-controller="Stores" asp-action="Index" class="btn btn-primary btn-block">Stores</a>
    <a style="width: 100%;margin-bottom: 0.5rem" asp-controller="Vat" asp-action="Index" class="btn btn-primary btn-block">Vat</a>
    <a style="width: 100%;margin-bottom: 0.5rem" asp-controller="Products" asp-action="Index" class="btn btn-primary btn-block">Products</a>
    <a style="width: 100%;margin-bottom: 0.5rem" asp-controller="PosMachine" asp-action="Index" class="btn btn-primary btn-block">Pos Machines</a>
    <a style="width: 100%;margin-bottom: 0.5rem" asp-controller="VatReport" asp-action="VatReport" class="btn btn-primary btn-block">Vat Report</a>
    <a style="width: 100%;margin-bottom: 0.5rem" asp-controller="EmailSubscription" asp-action="Index" class="btn btn-primary btn-block">Email Subscription</a>
    <a style="width: 100%;margin-bottom: 0.5rem" asp-controller="BudgensPromotions" asp-action="Index" class="btn btn-primary btn-block">Budgens Promotions</a>
    <a style="width: 100%;margin-bottom: 0.5rem" asp-controller="MorrisonsPromotions" asp-action="Index" class="btn btn-primary btn-block">Budgens Promotions</a>



</div>
