﻿using Microsoft.EntityFrameworkCore.Metadata.Internal;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Windows;
using System.Windows.Controls;
using System.Windows.Data;
using System.Windows.Documents;
using System.Windows.Input;
using System.Windows.Media;
using System.Windows.Media.Imaging;
using System.Windows.Shapes;
using WindowsInput;
using WindowsInput.Native;

namespace POS.WPF.Views
{
    /// <summary>
    /// Interaction logic for PaidAmount.xaml
    /// </summary>
    public partial class PaidAmount : Window
    {
        public static decimal Amount { get; set; }
        public static bool Cancelled { get; set; }
        private decimal _totalAmount;
        private List<decimal> _denominations = new List<decimal> { 5, 10, 20, 50, 100, 200, 500 };
        private InputSimulator _inputSimulator;

        public PaidAmount(decimal totalAmount)
        {
            InitializeComponent();
            Amount = 0;
            Cancelled = false;
            _totalAmount = totalAmount;
            CreateAmountButtons();
            _inputSimulator = new InputSimulator();
            Helpers.WindowStyleHelper.SetWindowStyle(this);
        }

        private void CreateAmountButtons()
        {
            // Exact amount button
            Button exactButton = new Button
            {
                Content = $"Exact: {_totalAmount:C}",
                Margin = new Thickness(5),
                FontSize = 18,
                Height = 40,
                Padding = new Thickness(5)
            };
            exactButton.Click += (s, e) =>
            {
                Amount = _totalAmount;
                txtPaidAmount.Text = _totalAmount.ToString("N2");
            };

            buttonsPanel.Children.Add(exactButton);

            // Suggested amounts
            List<decimal> suggestedAmounts = CalculateSuggestedAmounts(_totalAmount);
            foreach (decimal amount in suggestedAmounts)
            {
                Button btn = new Button
                {
                    Content = $"{amount:C}",
                    Margin = new Thickness(5),
                    FontSize = 18,
                    Height = 40,
                    Padding = new Thickness(5)
                };
                btn.Click += (s, e) =>
                {
                    Amount = amount;
                    txtPaidAmount.Text = amount.ToString("N2");
                };

                buttonsPanel.Children.Add(btn);
            }
        }

        private List<decimal> CalculateSuggestedAmounts(decimal total)
        {
            var candidates = _denominations.Where(d => d > total).Take(4).ToList();
            if (candidates.Count < 4)
            {
                int needed = 4 - candidates.Count;
                decimal last = candidates.LastOrDefault();
                if (last == 0)
                {
                    last = _denominations.Last(); // Use the highest denomination if total is larger
                }
                for (int i = 0; i < needed; i++)
                {
                    last *= 2;
                    candidates.Add(last);
                }
            }
            return candidates;
        }

        private void Button_Click(object sender, RoutedEventArgs e)
        {
            if (decimal.TryParse(txtPaidAmount.Text, out decimal amount))
            {
                Amount = amount;
                this.Close();
            }
            else
            {
                MessageBox.Show("Please enter a valid amount.");
            }
        }

        private void NumericKeypad_KeyPressed(object sender, string key)
        {
            txtPaidAmount.Focus();

            switch (key)
            {
                case  "Backspace":
                    _inputSimulator.Keyboard.KeyPress(VirtualKeyCode.BACK);
                    break;
                case "%":
                    _inputSimulator.Keyboard.KeyPress(VirtualKeyCode.SHIFT, VirtualKeyCode.VK_5);
                    break;
                case "00":
                    _inputSimulator.Keyboard.KeyPress(VirtualKeyCode.VK_0);
                    _inputSimulator.Keyboard.KeyPress(VirtualKeyCode.VK_0);
                    break;
                case ".":
                    _inputSimulator.Keyboard.KeyPress(VirtualKeyCode.DECIMAL);
                    break;
                default:
                    if (int.TryParse(key, out var number))
                    {
                        var keyCode = (VirtualKeyCode)Enum.Parse(
                            typeof(VirtualKeyCode),
                            $"VK_{number}"
                        );
                        _inputSimulator.Keyboard.KeyPress(keyCode);
                    }
                    break;
            }
        }


        //cancel
        private void Button_Click_1(object sender, RoutedEventArgs e)
        {
            Cancelled = true;
            this.Close();
        }
    }
}
