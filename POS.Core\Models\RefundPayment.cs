using System;

namespace POS.Core.Models
{
    public class RefundPayment : BaseEntity
    {
        public int Id { get; set; }
        public int RefundId { get; set; }
        public Refund Refund { get; set; }
        public int PaymentMethodId { get; set; }
        public PaymentMethod PaymentMethod { get; set; }
        public decimal Amount { get; set; }
        public DateTime Date { get; set; }
        public bool IsDeleted { get; set; }
        public DateTime LastModified { get; set; }
    }
} 