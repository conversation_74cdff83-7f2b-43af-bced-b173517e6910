﻿@model BackOffice.ViewModels.ProductSelectionViewModel

@{
    ViewBag.Title = "Select Products";
}

<h2>Select Products</h2>

<form id="productForm" method="post" action="@Url.Action("ProcessSelectedProducts")">
    <div>
        <label for="brandSelect">Brand:</label>
        <select id="brandSelect">
            <option value="">-- Select Brand --</option>
            @foreach (var brand in Model.Brands)
            {
                <option value="@brand.Id">@brand.Name</option>
            }
        </select>
    </div>

    <div>
        <label for="productSelect">Products:</label>
        <select id="productSelect">
            <option value="">-- Select Product --</option>
        </select>
        <button type="button" id="addProductButton">Add Product</button>
    </div>

    <div>
        <label for="selectedProducts">Selected Products:</label>
        <!-- A multiple select box to display chosen products -->
        <select id="selectedProducts" name="SelectedProductIds" multiple="multiple" size="5" style="width:200px;"></select>
    </div>

    <div>
        <input type="submit" value="Submit Selected Products" />
    </div>
</form>

@section Scripts {
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    <script>
        $(document).ready(function () {
            // When a brand is selected, load the products via AJAX.
            $('#brandSelect').change(function () {
                var brandId = $(this).val();

                if (brandId !== '') {
                    $.ajax({
                        url: '@Url.Action("GetProductsByBrand", "Print")', // Update controller name
                        type: 'GET',
                        data: { brandId: brandId },
                        success: function (data) {
                            console.log("Products received:", data); // Debugging log

                            var productSelect = $('#productSelect');
                            productSelect.empty();
                            productSelect.append('<option value="">-- Select Product --</option>');

                            $.each(data, function (i, product) {
                                console.log("Adding product:", product); // Debugging log
                                productSelect.append('<option value="' + product.id + '">' + product.name + '</option>');
                            });

                            productSelect.trigger("change"); // Force UI update
                        },
                        error: function () {
                            alert("Error loading products.");
                        }
                    });
                }
            });


            $('#addProductButton').click(function () {
                var productSelect = $('#productSelect');
                var selectedValue = productSelect.val();
                var selectedText = productSelect.find('option:selected').text();

                if (selectedValue && selectedValue !== '') {
                    // Check if already added to avoid duplicates.
                    if ($('#selectedProducts option[value="' + selectedValue + '"]').length === 0) {
                        $('#selectedProducts').append($('<option>', {
                            value: selectedValue,
                            text: selectedText
                        }));
                    }
                }
            });

            // Intercept form submission to mark all options in #selectedProducts as selected.
            $('#productForm').on('submit', function () {
                $('#selectedProducts option').prop('selected', true);
            });

        });
    </script>
}
