﻿@model POS.Core.Models.SubCatagory

@{
    ViewData["Title"] = "Delete";
}

<h1>Delete</h1>
<h3>Are you sure you want to delete this?</h3>
<div>
    <h4>VAT</h4>
    <dl class="row">
        <dt class="col-sm-2">Code</dt>
        <dd class="col-sm-10">@Model.Name</dd>

    </dl>
</div>
<form asp-action="Delete">
    <input type="hidden" asp-for="Id" />
    <input type="submit" value="Delete" class="btn btn-danger" /> |
    <a asp-action="Index" class="btn btn-secondary">Cancel</a>
</form>
