﻿using POS.Core.Models;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Windows;

namespace POS.WPF.Views
{
    public partial class PickPaymentMethod : Window
    {
        public List<PaymentMethod> SelectedPaymentMethods { get; private set; } = new List<PaymentMethod>();

        public PickPaymentMethod(IEnumerable<PaymentMethod> paymentMethods)
        {
            InitializeComponent();
            PaymentMethodsListView.ItemsSource = paymentMethods;
            Helpers.WindowStyleHelper.SetWindowStyle(this);
        }

        private void SelectButton_Click(object sender, RoutedEventArgs e)
        {
            // Get selected items
            var selectedItems = PaymentMethodsListView.SelectedItems.Cast<PaymentMethod>().ToList();

            if (selectedItems.Count == 0)
            {
                MessageBox.Show("Please select at least one payment method.", "Selection Required", MessageBoxButton.OK, MessageBoxImage.Warning);
                return;
            }

            if (selectedItems.Count > 2)
            {
                MessageBox.Show("You can select a maximum of two payment methods.", "Selection Limit", MessageBoxButton.OK, MessageBoxImage.Warning);
                return;
            }

            // Check if cash is included if two are selected
            if (selectedItems.Count == 2 && !selectedItems.Any(pm => pm.Method.Equals("Cash", StringComparison.OrdinalIgnoreCase)))
            {
                MessageBox.Show("If selecting two methods, one must be 'Cash'.", "Selection Rule", MessageBoxButton.OK, MessageBoxImage.Warning);
                return;
            }

            // Assign valid selection and close dialog
            SelectedPaymentMethods = selectedItems;
            DialogResult = true;
        }

        private void CancelButton_Click(object sender, RoutedEventArgs e)
        {
            this.Close();
        }
    }
}
