﻿@model BackOffice.ViewModels.MorrisonsOrderViewModel

<h2>Morrisons Order Placement</h2>

@if (TempData["SuccessMessage"] != null)
{
    <div class="alert alert-success">@TempData["SuccessMessage"]</div>
}

<form asp-action="PlaceOrder" method="post" id="orderForm">
    <div class="mb-3">
        <label for="barcode" class="form-label">Barcode</label>
        <input type="text" class="form-control" id="barcode" name="Barcode" autofocus>
    </div>
    <div class="mb-3">
        <label for="productName" class="form-label">Product Name</label>
        <input type="text" class="form-control" id="productName" readonly>
    </div>
    <div class="mb-3">
        <label for="quantity" class="form-label">Quantity</label>
        <input type="number" class="form-control" id="quantity" name="Quantity" min="1">
    </div>
    <button type="submit" class="btn btn-primary" id="submitOrder" disabled>Place Order</button>
</form>

<script>
    document.getElementById("barcode").addEventListener("input", function() {
        let barcode = this.value;
        if (barcode.length > 0) {
            fetch(`/morrisons/order/GetProduct?barcode=${barcode}`)
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        document.getElementById("productName").value = data.productName;
                        document.getElementById("submitOrder").disabled = false;
                    } else {
                        document.getElementById("productName").value = "Not Found";
                        document.getElementById("submitOrder").disabled = true;
                    }
                })
                .catch(error => console.error("Error:", error));
        } else {
            document.getElementById("productName").value = "";
            document.getElementById("submitOrder").disabled = true;
        }
    });
</script>
