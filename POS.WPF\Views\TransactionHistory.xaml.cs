﻿using Microsoft.EntityFrameworkCore;
using POS.Core.Models;
using POS.Printer;
using POS.WPF.Helpers;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Windows;
using System.Windows.Controls;
using System.Windows.Data;
using System.Windows.Documents;
using System.Windows.Input;
using System.Windows.Media;
using System.Windows.Media.Imaging;
using System.Windows.Shapes;
using TimeManagement;

namespace POS.WPF.Views
{
    /// <summary>
    /// Interaction logic for TransactionHistory.xaml
    /// </summary>
    public partial class TransactionHistory : Window
    {
        public TransactionHistory()
        {
            InitializeComponent();
            Helpers.WindowStyleHelper.SetWindowStyle(this);

            using (var context = new LocalData.LocalContext())
            {
                try
                {
                    // Get PosMachine and associated Store
                    var posMachine = context.PosMachines
                        .Include(p => p.Store)
                        .FirstOrDefault(p => p.Id == UiShift.PosMachineId);

                    if (posMachine == null)
                    {
                        MessageBox.Show("Pos Machine not found");
                        return;
                    }

                    // Calculate date range (last 7 days)
                    var startDate = CustomTimeProvider.Now.AddDays(-7);

                    // Get Sales for the Store in the last week
                    var sales = context.Sales
                        .Include(s => s.ProductStoreSales)
                            .ThenInclude(pss => pss.ProductStore)
                                .ThenInclude(ps => ps.Product)
                        .Where(s => s.Date >= startDate && s.ProductStoreSales
                            .Any(pss => pss.ProductStore.StoreId == posMachine.Store.StoreId))
                        .ToList();

                    dgSales.ItemsSource = sales;
                }
                catch (Exception ex)
                {
                    MessageBox.Show($"Error loading data: {ex.Message}");
                }
            }

        }

        private void BtnLoadTransactions_Click(object sender, RoutedEventArgs e)
        {
           

        }

        private void DgSales_SelectionChanged(object sender, SelectionChangedEventArgs e)
        {
            if (dgSales.SelectedItem is Sale selectedSale)
            {
                // Display detailed items
                dgSaleDetails.ItemsSource = selectedSale.ProductStoreSales;
            }
        }

        public static void RePrint(int saleId)
        {
            using (LocalData.LocalContext contxt = new LocalData.LocalContext())
            {
                var sale = contxt.Sales
                    .Include(s => s.SalePaymentMethods)
                    .ThenInclude(s => s.PaymentMethod)
                    .Include(s => s.ProductStoreSales)
                    .First(s => s.SaleId == saleId);
                
                var productStoreSales = contxt.ProductStoreSales
                    .Where(pss => pss.SaleId == saleId)
                    .ToList();

                List<POS.Printer.Product> products = new List<POS.Printer.Product>();

                foreach (var productStoreSale in productStoreSales)
                {
                    ProductPriceReductionType reductionType;
                    decimal reducedAmount;

                    if (productStoreSale.PriceOverriden)
                    {
                        reductionType = ProductPriceReductionType.PriceOverride;
                        reducedAmount = (decimal)productStoreSale.OverriddenDiscountAmount;
                    }
                    else if (productStoreSale.StaffDiscount)
                    {
                        reductionType = ProductPriceReductionType.StaffDiscount;
                        reducedAmount = productStoreSale.StaffDiscountedAmount;
                    }
                    else
                    {
                        reductionType = ProductPriceReductionType.None;
                        reducedAmount = 0;
                    }

                    var productStore = contxt.ProductStores
                        .Include(p => p.Product)
                        .First(p => p.ProductStoreId == productStoreSale.ProductStoreId);

                    products.Add(new Printer.Product(
                        $"{productStore.Product.Name} {productStoreSale.Quantity}", 
                        productStoreSale.Total, 
                        reductionType, 
                        reducedAmount));
                }

                List<string> cashierAlerts = GetCashierAlerts(sale.SaleId);
                var specialProductSales = new List<SpecialProductSale>(); // If needed, fetch from context

                // Handle different payment scenarios
                var cashPayment = sale.SalePaymentMethods.FirstOrDefault(spm => spm.PaymentMethod.Method == "Cash");
                var otherPayment = sale.SalePaymentMethods.LastOrDefault(spm => spm.PaymentMethod.Method != "Cash");

                if (cashPayment != null)
                {
                    // Cash only or Cash + Other payment
                    var paidAmount = sale.CashTendered.ToString();
                    var changeDue = (sale.CashTendered - sale.TotalValue).ToString();
                    
                    PaymentType? paymentType2 = null;
                    if (otherPayment != null)
                    {
                        // Cash + Other payment
                        paymentType2 = otherPayment.PaymentMethodId switch
                        {
                            1 => Printer.PaymentType.Card,
                            3 => Printer.PaymentType.Voucher,
                            4 => Printer.PaymentType.Deliveroo,
                            5 => Printer.PaymentType.Uber,
                            6 => Printer.PaymentType.PPCard,
                            _ => null
                        };
                    }

                    PrintBill(sale, products, productStoreSales, specialProductSales, 
                             PaymentType.Cash, paymentType2, paidAmount, changeDue, cashierAlerts);
                }
                else if (otherPayment != null)
                {
                    // Other payment only
                    var paymentType = otherPayment.PaymentMethodId switch
                    {
                        1 => Printer.PaymentType.Card,
                        3 => Printer.PaymentType.Voucher,
                        4 => Printer.PaymentType.Deliveroo,
                        5 => Printer.PaymentType.Uber,
                        6 => Printer.PaymentType.PPCard,
                        _ => PaymentType.Cash
                    };

                    PrintBill(sale, products, productStoreSales, specialProductSales,
                             paymentType, null, "0", "0", cashierAlerts);
                }
            }
        }

       

        private static void PrintBill(Sale sale, List<Printer.Product> products, List<ProductStoreSale> productStoreSales
       , List<SpecialProductSale> specialProductSales, Printer.PaymentType paymentType1, Printer.PaymentType? paymentType2, string paidAmount, string changeDue, List<string> cashierAlerts = null)
        {
            using (LocalData.LocalContext context = new LocalData.LocalContext())
            {
                POS.Printer.PrintData printData = new Printer.PrintData(paymentType1, paymentType2)
                {
                    StoreId = UiShift.StoreId.ToString(),
                    Time = sale.Date.ToString(),
                    CashierName = UiShift.CashierName,
                    TransactionId = sale.SaleId.ToString(),
                    PaidAmount = paidAmount,
                    ChangeDue = (double.TryParse(changeDue, out double changeDueDouble) && changeDueDouble < 0) ? "0" : changeDue,
                    Products = products,
                    Total = sale.TotalValue.ToString(),
                    VatNo = context.Stores.Find(UiShift.StoreId).VatNo,
                    Barcode = sale.Barcode,
                    StoreName = context.Stores.Find(UiShift.StoreId).StoreName,
                    PosMachineId = UiShift.PosMachineId.ToString(),
                    CashierAlerts = cashierAlerts ?? new List<string>(),
                    VatEntries = GenerateVatEntries(productStoreSales, specialProductSales, sale),
                    Type2Amount = sale.TotalValue - decimal.Parse(paidAmount)
                };

                POS.Printer.Printer.Print(printData, ConfigurationHelper.Address, ConfigurationHelper.PrinterName);
            }
        }



        private static List<string> GetCashierAlerts(int saleId)
        {
            using (LocalData.LocalContext context = new LocalData.LocalContext())
            {
                return context.ProductStoreSales
                    .Where(pss => pss.SaleId == saleId)
                    .Select(pss => pss.ProductStore.Product.Division.SubCatagory.CashierAlert)
                    .Where(alert => !string.IsNullOrEmpty(alert))
                    .Distinct()
                    .ToList();
            }
        }

        public static List<VatEntry> GenerateVatEntries(List<ProductStoreSale> productStoreSales, List<SpecialProductSale> specialProductSales, Sale sale)
        {
            using (LocalData.LocalContext dbContext = new LocalData.LocalContext())
            {
                // Process ProductStoreSales
                var productEntries = productStoreSales.Select(pss =>
                {
                    var productStore = dbContext.ProductStores.First(ps => ps.ProductStoreId == pss.ProductStoreId);
                    var product = dbContext.Products.First(p => p.Id == productStore.ProductId);
                    var vatRate = dbContext.Vats.First(v => v.Id == product.VatId);

                    decimal totalPrice = pss.Total; // Use the individual sale item's total
                    decimal vatValue = vatRate.Value / 100m;
                    decimal exVat = totalPrice / (1 + vatValue);
                    decimal vatAmount = totalPrice - exVat;

                    return new
                    {
                        Code = vatRate.Code,
                        Rate = vatRate.Rate,
                        ExVat = Math.Round(exVat, 2),
                        VatAmount = Math.Round(vatAmount, 2),
                        TotalPrice = Math.Round(totalPrice, 2)
                    };
                });

                // Process SpecialProductSales
                var specialEntries = specialProductSales.Select(sps =>
                {
                    var product = dbContext.Products.First(p => p.Id == sps.ProductId);
                    var vatRate = dbContext.Vats.First(v => v.Id == product.VatId);

                    decimal totalPrice = sps.Amount; // Use the special product sale amount
                    decimal vatValue = vatRate.Value / 100m;
                    decimal exVat = totalPrice / (1 + vatValue);
                    decimal vatAmount = totalPrice - exVat;

                    return new
                    {
                        Code = vatRate.Code,
                        Rate = vatRate.Rate,
                        ExVat = Math.Round(exVat, 2),
                        VatAmount = Math.Round(vatAmount, 2),
                        TotalPrice = Math.Round(totalPrice, 2)
                    };
                });

                // Combine and group results
                return productEntries
                    .Concat(specialEntries)
                    .GroupBy(x => x.Code)
                    .Select(g => new VatEntry(
                        g.Key,
                        g.First().Rate,
                        g.Sum(x => x.ExVat),
                        g.Sum(x => x.VatAmount),
                        g.Sum(x => x.TotalPrice)
                    ))
                    .ToList();
            }
        }




        private void Button_Click(object sender, RoutedEventArgs e)
        {
            if (dgSales.SelectedItem is Sale selectedSale)
            {
                RePrint(selectedSale.SaleId);
            }
        }


        private void Button_Click_1(object sender, RoutedEventArgs e)
        {
            this.Close();
        }
    }
}
