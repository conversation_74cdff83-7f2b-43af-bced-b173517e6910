﻿@model POS.Core.Models.Store

@{
    ViewData["Title"] = "Edit Store";
}

<h1>Edit Store</h1>

<form asp-action="Edit">
    <input type="hidden" asp-for="StoreId" />

    <div class="form-group">
        <label asp-for="StoreName" class="control-label"></label>
        <input asp-for="StoreName" class="form-control" />
        <span asp-validation-for="StoreName" class="text-danger"></span>
    </div>

    <div class="form-group">
        <label asp-for="BrandId" class="control-label">Brand</label>
        <select asp-for="BrandId" class="form-control" asp-items="@(new SelectList(ViewBag.Brands, "Id", "Name"))">
            <option value="">-- Select Brand --</option>
        </select>
        <span asp-validation-for="BrandId" class="text-danger"></span>
    </div>

    <div class="form-group">
        <label asp-for="CompanyId" class="control-label">Company</label>
        <select asp-for="CompanyId" class="form-control" asp-items="@(new SelectList(ViewBag.Companies, "Id", "Name"))">
            <option value="">-- Select Company --</option>
        </select>
        <span asp-validation-for="CompanyId" class="text-danger"></span>
    </div>

    <div class="form-group">
    <label asp-for="StoreIdForAPI" class="control-label">API Store ID</label>
    <input asp-for="StoreIdForAPI" class="form-control" />
    <span asp-validation-for="StoreIdForAPI" class="text-danger"></span>
</div>


    <div class="form-group">
        <label asp-for="AllowNegativeInventory"></label>
        <input type="checkbox" asp-for="AllowNegativeInventory" />
        <span asp-validation-for="AllowNegativeInventory"></span>
    </div>

    <div class="form-group">
        <label asp-for="MainPosMachineId">Select Main POS Machine</label>
        <select asp-for="MainPosMachineId" class="form-control">
            <option value="">-- Select a POS Machine --</option>
            @foreach (var posMachine in Model.PosMachines)
            {
                <option value="@posMachine.Id">@posMachine.Id - @posMachine.Store?.StoreName</option>
            }
        </select>
        <span asp-validation-for="MainPosMachineId" class="text-danger"></span>
    </div>

    <!-- Division Selection -->
    <div class="form-group">
        <label for="divisionDropdown">Select Division</label>
        <select id="divisionDropdown" class="form-control">
            <option value="">-- Select Division --</option>
            @foreach (var division in ViewBag.Divisions)
            {
                <option value="@division.Id">@division.Name</option>
            }
        </select>
    </div>

    <h3>Product Stores</h3>
    <table class="table">
        <thead>
            <tr>
                <th>Product Name</th>
                <th>Default Price</th>
                <th>Store Specific Price</th>
                <th>Inventory Count</th>
            </tr>
        </thead>
        <tbody id="productTableBody">
            @foreach (var productStore in Model.ProductStores)
            {
                <tr>
                    <td>
                        <input type="hidden" name="ProductStores[@productStore.ProductStoreId].ProductStoreId" value="@productStore.ProductStoreId" />
                        <input type="hidden" name="ProductStores[@productStore.ProductStoreId].StoreId" value="@Model.StoreId" />
                        <input type="hidden" name="ProductStores[@productStore.ProductStoreId].ProductId" value="@productStore.ProductId" />
                        @productStore.Product.Name
                    </td>
                    <td>
                        @productStore.Product.SellingPrice.ToString("C")
                    </td>
                    <td>
                        <input type="number" step="0.01" name="ProductStores[@productStore.ProductStoreId].StoreSpecificPrice"
                               value="@(productStore.StoreSpecificPrice.HasValue ? productStore.StoreSpecificPrice.Value.ToString("0.00") : "")"
                               class="form-control" placeholder="N/A" />
                    </td>
                    <td>
                        <input type="number" name="ProductStores[@productStore.ProductStoreId].InventoryCount"
                               value="@productStore.InventoryCount"
                               class="form-control" />
                    </td>
                </tr>
            }
        </tbody>
    </table>

    <button type="submit" class="btn btn-primary">Save</button>
    <a asp-action="Index" class="btn btn-secondary">Cancel</a>
</form>

@section Scripts {
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    <script>
        $(document).ready(function () {
            $('#divisionDropdown').change(function () {
                var selectedDivisionId = $(this).val();
                var storeId = $('#StoreId').val();

                if (selectedDivisionId && storeId) {
                    $.ajax({
                        url: '@Url.Action("GetProductsByDivision", "Stores")',
                        type: 'GET',
                        data: { storeId: storeId, divisionId: selectedDivisionId },
                        success: function (result) {
                            // Update the product table body with the returned partial view
                            $('#productTableBody').html(result);
                        },
                        error: function () {
                            alert('An error occurred while fetching the products.');
                        }
                    });
                } else {
                    // Optionally handle the case when no division is selected
                    $('#productTableBody').html('');
                }
            });
        });
    </script>
}
