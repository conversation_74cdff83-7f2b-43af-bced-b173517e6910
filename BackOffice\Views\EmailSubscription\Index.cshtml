﻿@model List<POS.Core.Models.EmailSubscription>

@{
    ViewData["Title"] = "Email Subscriptions for Daily Reports";
    bool NightEnabled = ViewBag.NightEnabled;
}

<h2>Email Subscriptions for Daily Reports</h2>

<form asp-action="Save" method="post">
    <div id="email-container" class="email-list">
        @for (int i = 0; i < Model.Count; i++)
        {
            <div class="email-entry">
                <input type="email"
                       name="emails[@i]"
                       value="@Model[i].Email"
                       class="form-control email-input"
                       placeholder="Enter email address" />

                <button type="button" class="btn btn-danger remove-email">Remove</button>
            </div>
        }
        @if (Model.Count == 0)
        {
            <div class="email-entry">
                <input type="email"
                       name="emails[0]"
                       class="form-control email-input"
                       placeholder="Enter email address" />
                <button type="button" class="btn btn-danger remove-email">Remove</button>
            </div>
        }
    </div>

    <div class="form-group">
        <label>
            <input type="checkbox" name="NightEnabled" value="true" @(NightEnabled ? "checked" : "") />
            Night Enabled
        </label>
    </div>

    <div class="form-group">
        <button type="button" id="add-email" class="btn btn-primary">Add Email</button>
        <button type="submit" class="btn btn-success">Save Changes</button>
    </div>
</form>

@section Scripts {
    <script>
        $(document).ready(function () {
            $("#add-email").click(function () {
                var index = $(".email-entry").length;
                var newEntry = `
                            <div class="email-entry">
                                <input type="email"
                                       name="emails[${index}]"
                                       class="form-control email-input"
                                       placeholder="Enter email address" />
                                <button type="button" class="btn btn-danger remove-email">Remove</button>
                            </div>`;
                $("#email-container").append(newEntry);
            });

            $("#email-container").on("click", ".remove-email", function () {
                if ($(".email-entry").length > 1) {
                    $(this).parent().remove();
                }
            });
        });
    </script>
}

<style>
    .email-list {
        margin-bottom: 20px;
    }

    .email-entry {
        margin-bottom: 10px;
        display: flex;
        gap: 10px;
        align-items: center;
    }

    .email-input {
        flex-grow: 1;
    }
</style>
