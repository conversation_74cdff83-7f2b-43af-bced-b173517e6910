﻿<Window x:Class="POS.WPF.Views.PaidAmount"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        xmlns:local="clr-namespace:POS.WPF.Views"
        Title="Paid Amount">
    <Grid Background="#004e7c">
        <StackPanel>
            <!-- Styled TextBox -->
            <TextBox x:Name="txtPaidAmount" 
                     FontSize="24" 
                     Height="50" 
                     Background="#0a688d" 
                     Foreground="White" 
                     VerticalAlignment="Center" 
                     Margin="10"/>

            <!-- Buttons Panel -->
            <WrapPanel x:Name="buttonsPanel" Margin="10"/>

            <!-- Action Buttons -->
            <Button Content="Save" 
                    Margin="10" 
                    FontSize="24" 
                    Height="50" 
                    Background="#004e7c" 
                    Foreground="White" 
                    Click="Button_Click"/>

            <Button Content="Cancel" 
                    Margin="10" 
                    FontSize="24" 
                    Height="50" 
                    Background="#004e7c" 
                    Foreground="White" 
                    Click="Button_Click_1"/>

            <!-- Numeric Keypad -->
            <local:NumericKeypad KeyPressed="NumericKeypad_KeyPressed" 
                                Margin="5"/>
        </StackPanel>
    </Grid>
</Window>