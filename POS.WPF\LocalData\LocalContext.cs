﻿using Microsoft.EntityFrameworkCore;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using POS.Core.Models;
using System.IO;

namespace POS.WPF.LocalData
{
    public class LocalContext : DbContext
    {
        public DbSet<Store> Stores { get; set; }
        public DbSet<POS.Core.Models.Product> Products { get; set; }

        public DbSet<Brand> Brands { get; set; }

        public DbSet<ProductStore> ProductStores { get; set; }
        public DbSet<Sale> Sales { get; set; }

        public DbSet<StoreDay> StoreDays { get; set; }

        public DbSet<ProductStoreSale> ProductStoreSales { get; set; }

        public DbSet<SpecialProductSale> SpecialProductSales { get; set; }
        public DbSet<Cashier> Cashiers { get; set; }

        public DbSet<User> Users { get; set; }

        public DbSet<Admin> Admins { get; set; }
        public DbSet<BackOffice> BackOffices { get; set; }
        public DbSet<Accountant> Accountants { get; set; }


        public DbSet<IpAddress> IpAddresses { get; set; }


        public DbSet<SafeDrop> SafeDrops { get; set; }

        public DbSet<PaymentMethod> PaymentMethods { get; set; }

        public DbSet<Shift> Shifts { get; set; }

        public DbSet<PaidOut> PaidOuts { get; set; }

        public DbSet<PaidOutOption> PaidOutOptions { get; set; }

        public DbSet<Company> Companies { get; set; }

        public DbSet<Vat> Vats { get; set; }

        public DbSet<SubCatagory> SubCatagories { get; set; }

        public DbSet<PosMachine> PosMachines { get; set; }

        public DbSet<Division> Divisions { get; set; }

        public DbSet<Refund> Refunds { get; set; }

        public DbSet<EmailSubscription> EmailSubscriptions { get; set; }

        public DbSet<NightEmail> NightEmails { get; set; }


        public DbSet<Promotion> Promotions { get; set; }

        public DbSet<SalePaymentMethod2> SalePaymentMethods { get; set; }

        public DbSet<NextOrderProduct> NextOrderProducts { get; set; }

        public DbSet<MorrisonOrder> MorrisonOrders { get; set; }

        public DbSet<MorrisonsOrderItem> MorrisonsOrderItems { get; set; }

        public DbSet<VoidSaleItem> VoidSaleItems { get; set; }

        public DbSet<Core.Models.Void> Voids { get; set; }

        public DbSet<DeletedSaleItem> DeletedSaleItems { get; set; }

        public DbSet<PendingSyncItem> PendingSyncItems { get; set; }

        public DbSet<IdMapping> IdMappings { get; set; }

        public DbSet<SyncedTime> SyncedTimes { get; set; }

        public DbSet<CashierStore> CashierStores { get; set; }

        public DbSet<CustomerCopy> CustomerCopys { get; set; }

        public DbSet<NonBrandPromotion>  NonBrandPromotions { get; set; }

        public DbSet<StoreSubCategoryLimit> StoreSubCategoryLimits { get; set; }

        public DbSet<QuickAccessButton> QuickAccessButtons { get; set; }

        protected override void OnModelCreating(ModelBuilder modelBuilder)
        {
            // Configure auto-increment primary keys for all entities
            modelBuilder.Entity<Store>().Property(s => s.StoreId).ValueGeneratedOnAdd();
            modelBuilder.Entity<POS.Core.Models.Product>().Property(p => p.Id).ValueGeneratedOnAdd();
            modelBuilder.Entity<Brand>().Property(b => b.Id).ValueGeneratedOnAdd();
            modelBuilder.Entity<ProductStore>().Property(ps => ps.ProductStoreId).ValueGeneratedOnAdd();
            modelBuilder.Entity<Sale>().Property(s => s.SaleId).ValueGeneratedOnAdd();
            modelBuilder.Entity<StoreDay>().Property(sd => sd.Id).ValueGeneratedOnAdd();
            modelBuilder.Entity<ProductStoreSale>().Property(ps => ps.ProductStoreSaleId).ValueGeneratedOnAdd();
            modelBuilder.Entity<SpecialProductSale>().Property(sps => sps.Id).ValueGeneratedOnAdd();
            modelBuilder.Entity<Cashier>().Property(c => c.Id).ValueGeneratedOnAdd();
            modelBuilder.Entity<User>().Property(u => u.Id).ValueGeneratedOnAdd();
            modelBuilder.Entity<Admin>().Property(a => a.Id).ValueGeneratedOnAdd();
            modelBuilder.Entity<BackOffice>().Property(bo => bo.Id).ValueGeneratedOnAdd();
            modelBuilder.Entity<Accountant>().Property(a => a.Id).ValueGeneratedOnAdd();
            modelBuilder.Entity<IpAddress>().Property(ip => ip.Id).ValueGeneratedOnAdd();
            modelBuilder.Entity<SafeDrop>().Property(sd => sd.Id).ValueGeneratedOnAdd();
            modelBuilder.Entity<PaymentMethod>().Property(pm => pm.Id).ValueGeneratedOnAdd();
            modelBuilder.Entity<Shift>().Property(s => s.Id).ValueGeneratedOnAdd();
            modelBuilder.Entity<PaidOut>().Property(po => po.Id).ValueGeneratedOnAdd();
            modelBuilder.Entity<PaidOutOption>().Property(po => po.Id).ValueGeneratedOnAdd();
            modelBuilder.Entity<Company>().Property(c => c.Id).ValueGeneratedOnAdd();
            modelBuilder.Entity<Vat>().Property(v => v.Id).ValueGeneratedOnAdd();
            modelBuilder.Entity<SubCatagory>().Property(sc => sc.Id).ValueGeneratedOnAdd();
            modelBuilder.Entity<PosMachine>().Property(pm => pm.Id).ValueGeneratedOnAdd();
            modelBuilder.Entity<Division>().Property(d => d.Id).ValueGeneratedOnAdd();
            modelBuilder.Entity<Refund>().Property(r => r.Id).ValueGeneratedOnAdd();
            modelBuilder.Entity<EmailSubscription>().Property(es => es.Id).ValueGeneratedOnAdd();
            modelBuilder.Entity<NightEmail>().Property(ne => ne.Id).ValueGeneratedOnAdd();
            modelBuilder.Entity<Promotion>().Property(p => p.Id).ValueGeneratedOnAdd();
            modelBuilder.Entity<NextOrderProduct>().Property(nop => nop.Id).ValueGeneratedOnAdd();
            modelBuilder.Entity<MorrisonOrder>().Property(mo => mo.Id).ValueGeneratedOnAdd();
            modelBuilder.Entity<MorrisonsOrderItem>().Property(moi => moi.Id).ValueGeneratedOnAdd();
            modelBuilder.Entity<VoidSaleItem>().Property(vsi => vsi.Id).ValueGeneratedOnAdd();
            modelBuilder.Entity<POS.Core.Models.Void>().Property(v => v.Id).ValueGeneratedOnAdd();
            modelBuilder.Entity<DeletedSaleItem>().Property(dsi => dsi.Id).ValueGeneratedOnAdd();
            modelBuilder.Entity<PendingSyncItem>().Property(psi => psi.Id).ValueGeneratedOnAdd();
            modelBuilder.Entity<SyncedTime>().Property(psi => psi.Id).ValueGeneratedOnAdd();
            modelBuilder.Entity<CustomerCopy>().Property(cc => cc.Id).ValueGeneratedOnAdd();
            modelBuilder.Entity<NonBrandPromotion>().Property(nbp => nbp.Id).ValueGeneratedOnAdd();
            modelBuilder.Entity<StoreSubCategoryLimit>().Property(scl => scl.Id).ValueGeneratedOnAdd();
            modelBuilder.Entity<QuickAccessButton>().Property(scl => scl.Id).ValueGeneratedOnAdd();

            // IdMapping doesn't need auto-increment as it uses composite key

            // Existing unique constraints
            modelBuilder.Entity<ProductStore>()
                .HasIndex(ps => new { ps.StoreId, ps.ProductId })
                .IsUnique();

            modelBuilder.Entity<IdMapping>()
                .HasKey(im => new { im.EntityType, im.LocalId });  // Define composite primary key

            modelBuilder.Entity<IdMapping>()
                .HasIndex(im => new { im.EntityType, im.LocalId })
                .IsUnique();

            modelBuilder.Entity<SalePaymentMethod2>(entity =>
            {
                entity.HasKey(e => e.Id);  // Set primary key

                // Add unique constraint
                entity.HasIndex(e => new { e.SaleId, e.PaymentMethodId })
                      .IsUnique();

                // Configure relationships
                entity.HasOne(e => e.Sale)
                      .WithMany(s => s.SalePaymentMethods)
                      .HasForeignKey(e => e.SaleId);

                entity.HasOne(e => e.PaymentMethod)
                      .WithMany(p => p.SalePaymentMethods)
                      .HasForeignKey(e => e.PaymentMethodId);
            });

            modelBuilder.Entity<CashierStore>()
                .HasKey(cs => new { cs.StoreId, cs.CashierId });

            modelBuilder.Entity<CashierStore>()
                .HasOne(sc => sc.Cashier)
                .WithMany(s => s.CashierStores)
                .HasForeignKey(sc => sc.CashierId);

            modelBuilder.Entity<CashierStore>()
                .HasOne(sc => sc.Store)
                .WithMany(c => c.CashierStores)
                .HasForeignKey(sc => sc.StoreId);

            base.OnModelCreating(modelBuilder);
        }


        //public Data(DbContextOptions<Data> options) : base(options)
        //{

        //}

        protected override void OnConfiguring(DbContextOptionsBuilder optionsBuilder)
        {
            string dbPath = "localpos.db";
            // Use executable directory as base path
            string fullPath = Path.Combine(AppDomain.CurrentDomain.BaseDirectory, dbPath);
            optionsBuilder.UseSqlite($"Data Source={fullPath}");
        }
    }
}
