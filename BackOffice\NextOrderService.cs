﻿using Microsoft.EntityFrameworkCore;
using POS.Core.Models;

namespace BackOffice
{
    public class NextOrderService
    {
        private readonly Data _context;

        public NextOrderService(Data context)
        {
            _context = context;
        }

        public async Task ProcessCsvUploadAsync(IFormFile file)
        {
            using var reader = new StreamReader(file.OpenReadStream());
            var csv = await reader.ReadToEndAsync();
            var lines = csv.Split('\n').Skip(1); // Assuming first row is headers

            foreach (var line in lines)
            {
                var columns = line.Split(',');
                if (columns.Length < 2) continue;

                string barcode = columns[0].Trim();
                if (!int.TryParse(columns[1].Trim(), out int quantity)) continue;

                var existingProduct = await _context.NextOrderProducts
                    .FirstOrDefaultAsync(p => p.Barcode == barcode);

                if (existingProduct != null)
                {
                    existingProduct.Quantity += quantity;
                }
                else
                {
                    _context.NextOrderProducts.Add(new NextOrderProduct { Barcode = barcode, Quantity = quantity });
                }
            }

            await _context.SaveChangesAsync();
        }

        public async Task<List<NextOrderProduct>> GetNextOrderProductsAsync()
        {
            return await _context.NextOrderProducts.ToListAsync();
        }

        public async Task UpdateQuantityAsync(int id, int quantity)
        {
            var product = await _context.NextOrderProducts.FindAsync(id);
            if (product != null)
            {
                product.Quantity = quantity;
                await _context.SaveChangesAsync();
            }
        }

        public async Task RemoveProductAsync(int id)
        {
            var product = await _context.NextOrderProducts.FindAsync(id);
            if (product != null)
            {
                _context.NextOrderProducts.Remove(product);
                await _context.SaveChangesAsync();
            }
        }
    }
}
