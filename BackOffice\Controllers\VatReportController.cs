﻿using BackOffice.ViewModels;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using POS.Core.Models;
using System.Security.Claims;

namespace BackOffice.Controllers
{
    [Authorize(Roles = "BackOffice,Admin,Accountant")]
    public class VatReportController : Controller
    {
        private readonly Data _context;

        public VatReportController(Data context)
        {
            _context = context;
        }

        private int? GetCurrentUserStoreId()
        {
            if (User.IsInRole("BackOffice"))
            {
                var userId = int.Parse(User.Claims.FirstOrDefault(c => c.Type == ClaimTypes.NameIdentifier)?.Value);
                return _context.BackOffices.Find(userId)?.StoreId;
            }
            return null;
        }

        private IActionResult RedirectToUserHome()
        {
            if (User.IsInRole("BackOffice"))
            {
                return RedirectToAction("Index", "BackOffice");
            }
            return RedirectToAction("Index", "Home");
        }

        public IActionResult Index()
        {
            return View();
        }


        public IActionResult VatReport(DateTime startDate, DateTime endDate)
        {
            // Validate dates
            if (startDate > endDate)
            {
                ModelState.AddModelError("", "Start date must be before end date.");
                return View(new List<VatReportViewModel>());
            }

            var storeId = GetCurrentUserStoreId();
            
            // Get store information for the report header
            string companyName = "";  // Replace with actual company name from settings
            string vatNumber = "";
            string storeName = "";
            
            if (storeId.HasValue)
            {
                var store = _context.Stores.Find(storeId.Value);
                if (store != null)
                {
                    vatNumber = store.VatNo;
                    storeName = store.StoreName;
                }
            }

            companyName = _context.Stores.FirstOrDefault(s => s.StoreId == storeId)?.Company?.Name ?? "";
            
            var query = _context.Sales
                .Where(s => s.Date >= startDate && s.Date <= endDate);

            // Apply store filter for BackOffice users
            if (storeId.HasValue)
            {
                query = query.Where(s => s.ProductStoreSales
                    .Any(pss => pss.ProductStore.StoreId == storeId.Value));
            }

            // Get regular product sales
            var regularSales = query
                .SelectMany(s => s.ProductStoreSales)
                .Select(pss => new
                {
                    pss.Total,
                    SubCategory = pss.ProductStore.Product.Division.SubCatagory,
                    Vat = pss.ProductStore.Product.Vat,
                    IsSpecial = false
                })
                .Where(x => x.Vat != null);

            // Get special product sales
            var specialSales = query
                .SelectMany(s => s.SpecialProductSales)
                .Select(sps => new
                {
                    Total = sps.Amount,
                    SubCategory = sps.Product.Division.SubCatagory,
                    Vat = sps.Product.Vat,
                    IsSpecial = true
                })
                .Where(x => x.Vat != null);

            // Combine both regular and special sales
            var combinedSales = regularSales.Concat(specialSales);

            var result = combinedSales
                .GroupBy(x => new { x.Vat.Code, x.SubCategory.Name, x.IsSpecial })
                .Select(g => new
                {
                    VatCode = g.Key.Code,
                    SubCategoryName = g.Key.Name,
                    IsSpecial = g.Key.IsSpecial,
                    SalesIncVat = g.Sum(x => x.Total),
                    SalesExcVat = g.Sum(x => x.Total / (1 + x.Vat.Value/100)),
                    VatAmount = g.Sum(x => x.Total - (x.Total / (1 + x.Vat.Value/100)))
                })
                .ToList();

            var reportData = result
                .GroupBy(d => d.VatCode)
                .Select(g => new VatReportViewModel
                {
                    VatCode = g.Key,
                    SubCategories = g.Select(sc => new VatReportSubCategory
                    {
                        SubCategoryName = sc.SubCategoryName,
                        IsSpecial = sc.IsSpecial,
                        SalesIncVat = sc.SalesIncVat,
                        SalesExcVat = sc.SalesExcVat,
                        VatAmount = sc.VatAmount
                    }).ToList()
                })
                .ToList();

            // Add the header information to ViewBag
            ViewBag.CompanyName = companyName;
            ViewBag.VatNumber = vatNumber;
            ViewBag.StoreName = storeName;
            ViewBag.StartDate = startDate;
            ViewBag.EndDate = endDate;
            
            // Calculate totals for each VAT code
            var vatCodeTotals = new List<VatCodeTotal>();
            decimal grandTotalIncVat = 0;
            decimal grandTotalExcVat = 0;
            decimal grandTotalVatAmount = 0;
            
            foreach (var vatGroup in reportData)
            {
                decimal totalIncVat = vatGroup.SubCategories.Sum(sc => sc.SalesIncVat);
                decimal totalExcVat = vatGroup.SubCategories.Sum(sc => sc.SalesExcVat);
                decimal totalVatAmount = vatGroup.SubCategories.Sum(sc => sc.VatAmount);
                
                vatGroup.TotalIncVat = totalIncVat;
                vatGroup.TotalExcVat = totalExcVat;
                vatGroup.TotalVatAmount = totalVatAmount;
                
                vatCodeTotals.Add(new VatCodeTotal
                {
                    VatCode = vatGroup.VatCode,
                    TotalIncVat = totalIncVat,
                    TotalExcVat = totalExcVat,
                    TotalVatAmount = totalVatAmount
                });
                
                grandTotalIncVat += totalIncVat;
                grandTotalExcVat += totalExcVat;
                grandTotalVatAmount += totalVatAmount;
            }
            
            ViewBag.VatCodeTotals = vatCodeTotals;
            ViewBag.GrandTotalIncVat = grandTotalIncVat;
            ViewBag.GrandTotalExcVat = grandTotalExcVat;
            ViewBag.GrandTotalVatAmount = grandTotalVatAmount;
            
            return View(reportData);
        }


    }

}

