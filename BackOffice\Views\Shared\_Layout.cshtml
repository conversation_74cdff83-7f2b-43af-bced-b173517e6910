﻿<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="utf-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>@ViewData["Title"] - BackOffice</title>
    <link rel="stylesheet" href="~/lib/bootstrap/dist/css/bootstrap.min.css" />
    <link rel="stylesheet" href="~/css/site.css" asp-append-version="true" />
    <link rel="stylesheet" href="~/BackOffice.styles.css" asp-append-version="true" />
    <link href="https://cdn.jsdelivr.net/npm/select2@4.1.0-rc.0/dist/css/select2.min.css" rel="stylesheet" />
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/select2@4.1.0-rc.0/dist/js/select2.min.js"></script>
</head>

<body>
    <header>
        <nav class="navbar navbar-expand-sm navbar-toggleable-sm navbar-light bg-white border-bottom box-shadow mb-3">
            <div class="container-fluid">
                <a class="navbar-brand" asp-area="" 
                   asp-controller="@(User.IsInRole("Admin") ? "Home" : User.IsInRole("BackOffice") ? "BackOffice" : "Home")" 
                   asp-action="Index">Pos Web</a>
                <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target=".navbar-collapse"
                        aria-controls="navbarSupportedContent" aria-expanded="false" aria-label="Toggle navigation">
                    <span class="navbar-toggler-icon"></span>
                </button>
                <div class="navbar-collapse collapse d-sm-inline-flex justify-content-between">
                    <ul class="navbar-nav flex-grow-1">
                        <li class="nav-item">
                            @if (User.IsInRole("BackOffice"))
                            {
                                <a class="nav-link text-dark" asp-area="" asp-controller="BackOffice" asp-action="Index">Home</a>
                            }
                            else
                            {
                                <a class="nav-link text-dark" asp-area="" asp-controller="Home" asp-action="Index">Home</a>
                            }
                        </li>
                        <li class="nav-item">
                            <a class="nav-link text-dark" asp-area="" asp-controller="Home"
                               asp-action="Privacy">Privacy</a>
                        </li>
                        
                        <li class="nav-item dropdown">
                            <a class="nav-link dropdown-toggle text-dark" href="#" id="reportsDropdown" role="button" data-bs-toggle="dropdown" aria-expanded="false">
                                Reports
                            </a>
                            <ul class="dropdown-menu" aria-labelledby="reportsDropdown">
                                <li><a class="dropdown-item" asp-area="" asp-controller="Reports" asp-action="DailySalesReport">Daily Sales Report</a></li>
                                <li><a class="dropdown-item" asp-area="" asp-controller="Reports" asp-action="MonthlySaleReport">Monthly Sales Report</a></li>
                                <li><a class="dropdown-item" asp-area="" asp-controller="Reports" asp-action="MonthlySalesReportExtended">Monthly Sales Report Extended</a></li>
                                <li><a class="dropdown-item" asp-area="" asp-controller="Reports" asp-action="DailySalesReportExtended">Daily Sales Report Extended</a></li>
                                <li><a class="dropdown-item" asp-area="" asp-controller="Reports" asp-action="MonthlyProductQuantityReport">Monthly Product Quantity Report</a></li>

                            </ul>
                        </li>

                         <li class="nav-item dropdown">
                            <a class="nav-link dropdown-toggle text-dark" href="#" id="reportsDropdown" role="button" data-bs-toggle="dropdown" aria-expanded="false">
                                Categories
                            </a>
                            <ul class="dropdown-menu" aria-labelledby="reportsDropdown">
                                <li><a class="dropdown-item" asp-area="" asp-controller="SubCatagory" asp-action="Index">SubCatagory</a></li>
                                <li><a class="dropdown-item" asp-area="" asp-controller="Divisions" asp-action="Index">Divisions</a></li>
                            </ul>
                        </li>

                        <li class="nav-item dropdown">
                            <a class="nav-link dropdown-toggle text-dark" href="#" id="morrisonDropdown" role="button" data-bs-toggle="dropdown" aria-expanded="false">
                                Morrison
                            </a>
                            <ul class="dropdown-menu" aria-labelledby="morrisonDropdown">
                                <li><a class="dropdown-item" asp-area="" asp-controller="MorrisonsProduct" asp-action="Index">Product Sync</a></li>
                            </ul>
                        </li>

                        <li class="nav-item dropdown">
                            <a class="nav-link dropdown-toggle text-dark" href="#" id="reportsDropdown" role="button" data-bs-toggle="dropdown" aria-expanded="false">
                                Users
                            </a>
                            <ul class="dropdown-menu" aria-labelledby="reportsDropdown">
                                <li><a class="dropdown-item" asp-area="" asp-controller="AdminUser" asp-action="Index">Main</a></li>
                                <li><a class="dropdown-item" asp-area="" asp-controller="Cashier" asp-action="Index">Cashiers</a></li>
                            </ul>
                        </li>

                    </ul>
                </div>
            </div>
            @if (User.Identity.IsAuthenticated)
            {
                <form method="post" asp-controller="Auth" asp-action="Logout">
                    <button type="submit">Logout</button>
                </form>
            }

        </nav>
    </header>
    <div class="container">
        <main role="main" class="pb-3">
            @RenderBody()
        </main>
    </div>

    <footer class="border-top footer text-muted">
        <div class="container">
            &copy; 2025 - BackOffice - <a asp-area="" asp-controller="Home" asp-action="Privacy">Privacy</a>
        </div>
    </footer>
    <script src="~/lib/jquery/dist/jquery.min.js"></script>
    <script src="~/lib/bootstrap/dist/js/bootstrap.bundle.min.js"></script>
    <script src="~/js/site.js" asp-append-version="true"></script>
    <script src="https://cdn.jsdelivr.net/npm/jsbarcode@3.11.5/dist/JsBarcode.all.min.js"></script>
    @RenderSection("Scripts", required: false)
</body>

</html>
