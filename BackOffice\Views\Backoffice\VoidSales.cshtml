@model IEnumerable<POS.Core.Models.Sale>

@{
    ViewData["Title"] = "Void Sales";
}

<h2>Void Sales</h2>

<div class="mb-3">
    <a asp-action="Index" class="btn btn-secondary">Back to Dashboard</a>
</div>

@if (Model.Any())
{
    <table class="table table-striped">
        <thead>
            <tr>
                <th>Sale ID</th>
                <th>Date</th>
                <th>Total Value</th>
                <th>Terminal</th>
                <th>Details</th>
            </tr>
        </thead>
        <tbody>
            @foreach (var sale in Model)
            {
                <tr>
                    <td>@sale.SaleId</td>
                    <td>@sale.Date.ToString("yyyy-MM-dd HH:mm:ss")</td>
                    <td>@sale.TotalValue.ToString("C")</td>
                    <td>@sale.Shift.PosMachine.Id</td>
                    <td>
                        <button class="btn btn-sm btn-primary" data-bs-toggle="modal" data-bs-target="#<EMAIL>">
                            View Details
                        </button>
                    </td>
                </tr>
            }
        </tbody>
    </table>

    @foreach (var sale in Model)
    {
        <div class="modal fade" id="<EMAIL>" tabindex="-1" aria-hidden="true">
            <div class="modal-dialog modal-lg">
                <div class="modal-content">
                    <div class="modal-header">
                        <h5 class="modal-title">Sale Details #@sale.SaleId</h5>
                        <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                    </div>
                    <div class="modal-body">
                        <p><strong>Date:</strong> @sale.Date.ToString("yyyy-MM-dd HH:mm:ss")</p>
                        <p><strong>Total Value:</strong> @sale.TotalValue.ToString("C")</p>
                        <p><strong>Terminal:</strong> @sale.Shift.PosMachine.Id</p>
                        <p><strong>Status:</strong> Void</p>
                        
                        @if (sale.ProductStoreSales != null && sale.ProductStoreSales.Any())
                        {
                            <h6 class="mt-3">Products</h6>
                            <table class="table table-sm">
                                <thead>
                                    <tr>
                                        <th>Product</th>
                                        <th>Quantity</th>
                                        <th>Unit Price</th>
                                        <th>Total</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    @foreach (var item in sale.ProductStoreSales)
                                    {
                                        var productName = item.ProductStore?.Product?.Name ?? "Unknown";
                                        var unitPrice = item.ProductStore?.StoreSpecificPrice ?? 
                                                       (item.ProductStore?.Product?.SellingPrice ?? 0);
                                        <tr>
                                            <td>@productName</td>
                                            <td>@item.Quantity</td>
                                            <td>@unitPrice.ToString("C")</td>
                                            <td>@item.Total.ToString("C")</td>
                                        </tr>
                                    }
                                </tbody>
                            </table>
                        }
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Close</button>
                    </div>
                </div>
            </div>
        </div>
    }
}
else
{
    <div class="alert alert-info">
        No void sales found.
    </div>
}


