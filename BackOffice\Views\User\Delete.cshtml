﻿@model POS.Core.Models.User

<h2>Delete User</h2>

<p>Are you sure you want to delete this user?</p>

<div>
    <h4>User Details</h4>
    <p>User Type: @Model.GetType().Name</p>
    <p>User ID: @Model.UserId</p>
    <p>Name: @Model.Name</p>
    <p>Password: @Model.Password</p>
</div>

<form asp-action="Delete" method="post">
    <input type="hidden" asp-for="Id" />
    <button type="submit">Delete</button>
    <a asp-action="Index">Cancel</a>
</form>