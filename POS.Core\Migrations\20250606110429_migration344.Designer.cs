﻿// <auto-generated />
using System;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Infrastructure;
using Microsoft.EntityFrameworkCore.Metadata;
using Microsoft.EntityFrameworkCore.Migrations;
using Microsoft.EntityFrameworkCore.Storage.ValueConversion;
using POS.Core.Models;

#nullable disable

namespace POS.Core.Migrations
{
    [DbContext(typeof(Data))]
    [Migration("20250606110429_migration344")]
    partial class migration344
    {
        /// <inheritdoc />
        protected override void BuildTargetModel(ModelBuilder modelBuilder)
        {
#pragma warning disable 612, 618
            modelBuilder
                .HasAnnotation("ProductVersion", "8.0.0")
                .HasAnnotation("Relational:MaxIdentifierLength", 128);

            SqlServerModelBuilderExtensions.UseIdentityColumns(modelBuilder);

            modelBuilder.Entity("POS.Core.Models.Brand", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("Id"));

                    b.Property<string>("BrandLogo")
                        .HasColumnType("nvarchar(max)");

                    b.Property<bool>("IsDeleted")
                        .HasColumnType("bit");

                    b.Property<DateTime?>("LastModified")
                        .HasColumnType("datetime2");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)");

                    b.HasKey("Id");

                    b.ToTable("Brands");
                });

            modelBuilder.Entity("POS.Core.Models.CashierStore", b =>
                {
                    b.Property<int>("StoreId")
                        .HasColumnType("int");

                    b.Property<int>("CashierId")
                        .HasColumnType("int");

                    b.Property<bool>("IsDeleted")
                        .HasColumnType("bit");

                    b.Property<DateTime?>("LastModified")
                        .HasColumnType("datetime2");

                    b.HasKey("StoreId", "CashierId");

                    b.HasIndex("CashierId");

                    b.ToTable("CashierStores");
                });

            modelBuilder.Entity("POS.Core.Models.Company", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("Id"));

                    b.Property<string>("Address")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)");

                    b.Property<bool>("IsDeleted")
                        .HasColumnType("bit");

                    b.Property<DateTime?>("LastModified")
                        .HasColumnType("datetime2");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)");

                    b.HasKey("Id");

                    b.ToTable("Companies");
                });

            modelBuilder.Entity("POS.Core.Models.DeletedSaleItem", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("Id"));

                    b.Property<DateTime>("DeletedTime")
                        .HasColumnType("datetime2");

                    b.Property<bool>("IsDeleted")
                        .HasColumnType("bit");

                    b.Property<DateTime?>("LastModified")
                        .HasColumnType("datetime2");

                    b.Property<string>("PLU")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)");

                    b.Property<int>("PosId")
                        .HasColumnType("int");

                    b.Property<int>("Qty")
                        .HasColumnType("int");

                    b.Property<int>("StoreId")
                        .HasColumnType("int");

                    b.Property<decimal>("Total")
                        .HasColumnType("decimal(18,2)");

                    b.HasKey("Id");

                    b.ToTable("DeletedSaleItems");
                });

            modelBuilder.Entity("POS.Core.Models.Division", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("Id"));

                    b.Property<bool>("IsDeleted")
                        .HasColumnType("bit");

                    b.Property<DateTime?>("LastModified")
                        .HasColumnType("datetime2");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)");

                    b.Property<int>("SubCatagoryId")
                        .HasColumnType("int");

                    b.HasKey("Id");

                    b.HasIndex("SubCatagoryId");

                    b.ToTable("Divisions");
                });

            modelBuilder.Entity("POS.Core.Models.EmailSubscription", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("Id"));

                    b.Property<string>("Email")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)");

                    b.Property<bool>("IsDeleted")
                        .HasColumnType("bit");

                    b.Property<DateTime?>("LastModified")
                        .HasColumnType("datetime2");

                    b.HasKey("Id");

                    b.ToTable("EmailSubscriptions");
                });

            modelBuilder.Entity("POS.Core.Models.IpAddress", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("Id"));

                    b.Property<int?>("AccountantId")
                        .HasColumnType("int");

                    b.Property<string>("Address")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)");

                    b.Property<int?>("BackOfficeId")
                        .HasColumnType("int");

                    b.Property<bool>("IsDeleted")
                        .HasColumnType("bit");

                    b.Property<DateTime?>("LastModified")
                        .HasColumnType("datetime2");

                    b.Property<int>("UserId")
                        .HasColumnType("int");

                    b.HasKey("Id");

                    b.HasIndex("AccountantId");

                    b.HasIndex("BackOfficeId");

                    b.HasIndex("UserId");

                    b.ToTable("IpAddresses");
                });

            modelBuilder.Entity("POS.Core.Models.MorrisonOrder", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("Id"));

                    b.Property<DateTime>("CreatedDate")
                        .HasColumnType("datetime2");

                    b.Property<DateTime>("ExpectedDeliveryDate")
                        .HasColumnType("datetime2");

                    b.Property<string>("GeneratedId")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)");

                    b.Property<bool>("IsDeleted")
                        .HasColumnType("bit");

                    b.Property<DateTime?>("LastModified")
                        .HasColumnType("datetime2");

                    b.Property<int>("MorrisonOrderStatus")
                        .HasColumnType("int");

                    b.Property<int>("StoreId")
                        .HasColumnType("int");

                    b.HasKey("Id");

                    b.HasIndex("StoreId");

                    b.ToTable("MorrisonOrders");
                });

            modelBuilder.Entity("POS.Core.Models.MorrisonsOrderItem", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("Id"));

                    b.Property<string>("Barcode")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)");

                    b.Property<bool>("IsDeleted")
                        .HasColumnType("bit");

                    b.Property<DateTime?>("LastModified")
                        .HasColumnType("datetime2");

                    b.Property<int>("MorrisonOrderId")
                        .HasColumnType("int");

                    b.Property<int>("Quantity")
                        .HasColumnType("int");

                    b.HasKey("Id");

                    b.HasIndex("MorrisonOrderId");

                    b.ToTable("MorrisonsOrderItems");
                });

            modelBuilder.Entity("POS.Core.Models.NextOrderProduct", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("Id"));

                    b.Property<string>("Barcode")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)");

                    b.Property<bool>("IsDeleted")
                        .HasColumnType("bit");

                    b.Property<DateTime?>("LastModified")
                        .HasColumnType("datetime2");

                    b.Property<int>("Quantity")
                        .HasColumnType("int");

                    b.Property<int>("StoreId")
                        .HasColumnType("int");

                    b.HasKey("Id");

                    b.HasIndex("StoreId");

                    b.ToTable("NextOrderProducts");
                });

            modelBuilder.Entity("POS.Core.Models.NightEmail", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("Id"));

                    b.Property<bool>("Enabled")
                        .HasColumnType("bit");

                    b.Property<bool>("IsDeleted")
                        .HasColumnType("bit");

                    b.Property<DateTime?>("LastModified")
                        .HasColumnType("datetime2");

                    b.HasKey("Id");

                    b.ToTable("NightEmails");
                });

            modelBuilder.Entity("POS.Core.Models.NonBrandPromotion", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("Id"));

                    b.Property<int>("CreatedById")
                        .HasColumnType("int");

                    b.Property<DateTime>("EndTime")
                        .HasColumnType("datetime2");

                    b.Property<bool>("IsDeleted")
                        .HasColumnType("bit");

                    b.Property<DateTime?>("LastModified")
                        .HasColumnType("datetime2");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)");

                    b.Property<decimal?>("PromotionPrice")
                        .HasColumnType("decimal(18,2)");

                    b.Property<int>("Quantity")
                        .HasColumnType("int");

                    b.Property<DateTime>("StartTime")
                        .HasColumnType("datetime2");

                    b.Property<int>("StoreId")
                        .HasColumnType("int");

                    b.Property<int>("Type")
                        .HasColumnType("int");

                    b.HasKey("Id");

                    b.HasIndex("CreatedById");

                    b.HasIndex("StoreId");

                    b.ToTable("NonBrandPromotions");
                });

            modelBuilder.Entity("POS.Core.Models.PaidOut", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("Id"));

                    b.Property<decimal>("Amount")
                        .HasColumnType("decimal(18,2)");

                    b.Property<bool>("IsDeleted")
                        .HasColumnType("bit");

                    b.Property<DateTime?>("LastModified")
                        .HasColumnType("datetime2");

                    b.Property<int?>("PaidOutOptionId")
                        .HasColumnType("int");

                    b.Property<int>("ShiftId")
                        .HasColumnType("int");

                    b.Property<int?>("StoreId")
                        .HasColumnType("int");

                    b.Property<DateTime>("Time")
                        .HasColumnType("datetime2");

                    b.HasKey("Id");

                    b.HasIndex("PaidOutOptionId");

                    b.HasIndex("ShiftId");

                    b.HasIndex("StoreId");

                    b.ToTable("PaidOuts");
                });

            modelBuilder.Entity("POS.Core.Models.PaidOutOption", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("Id"));

                    b.Property<bool>("IsDeleted")
                        .HasColumnType("bit");

                    b.Property<DateTime?>("LastModified")
                        .HasColumnType("datetime2");

                    b.Property<string>("Option")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)");

                    b.HasKey("Id");

                    b.ToTable("PaidOutOptions");
                });

            modelBuilder.Entity("POS.Core.Models.PaymentMethod", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("Id"));

                    b.Property<bool>("IsDeleted")
                        .HasColumnType("bit");

                    b.Property<DateTime?>("LastModified")
                        .HasColumnType("datetime2");

                    b.Property<string>("Method")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)");

                    b.HasKey("Id");

                    b.ToTable("PaymentMethods");
                });

            modelBuilder.Entity("POS.Core.Models.PosMachine", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("Id"));

                    b.Property<bool>("IsDeleted")
                        .HasColumnType("bit");

                    b.Property<DateTime?>("LastModified")
                        .HasColumnType("datetime2");

                    b.Property<int>("StoreId")
                        .HasColumnType("int");

                    b.HasKey("Id");

                    b.HasIndex("StoreId");

                    b.ToTable("PosMachines");
                });

            modelBuilder.Entity("POS.Core.Models.Product", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("Id"));

                    b.Property<string>("Barcode")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)");

                    b.Property<int?>("BrandId")
                        .HasColumnType("int");

                    b.Property<string>("Description")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)");

                    b.Property<int>("DivisionId")
                        .HasColumnType("int");

                    b.Property<bool>("IsDeleted")
                        .HasColumnType("bit");

                    b.Property<DateTime?>("LastModified")
                        .HasColumnType("datetime2");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)");

                    b.Property<int?>("NonBrandPromotionId")
                        .HasColumnType("int");

                    b.Property<string>("PLU")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)");

                    b.Property<int?>("PromotionId")
                        .HasColumnType("int");

                    b.Property<decimal>("PurchasePackSize")
                        .HasColumnType("decimal(18,2)");

                    b.Property<decimal>("PurchasePrice")
                        .HasColumnType("decimal(18,2)");

                    b.Property<decimal>("SellingPrice")
                        .HasColumnType("decimal(18,2)");

                    b.Property<bool>("SpecialProduct")
                        .HasColumnType("bit");

                    b.Property<int>("VatId")
                        .HasColumnType("int");

                    b.HasKey("Id");

                    b.HasIndex("BrandId");

                    b.HasIndex("DivisionId");

                    b.HasIndex("NonBrandPromotionId");

                    b.HasIndex("PromotionId");

                    b.HasIndex("VatId");

                    b.ToTable("Products");
                });

            modelBuilder.Entity("POS.Core.Models.ProductStore", b =>
                {
                    b.Property<int>("ProductStoreId")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("ProductStoreId"));

                    b.Property<int>("InventoryCount")
                        .HasColumnType("int");

                    b.Property<bool>("IsDeleted")
                        .HasColumnType("bit");

                    b.Property<DateTime?>("LastModified")
                        .HasColumnType("datetime2");

                    b.Property<int>("ProductId")
                        .HasColumnType("int");

                    b.Property<int>("StoreId")
                        .HasColumnType("int");

                    b.Property<decimal?>("StoreSpecificPrice")
                        .HasColumnType("decimal(18,2)");

                    b.HasKey("ProductStoreId");

                    b.HasIndex("ProductId");

                    b.HasIndex("StoreId", "ProductId")
                        .IsUnique();

                    b.ToTable("ProductStores");
                });

            modelBuilder.Entity("POS.Core.Models.ProductStoreSale", b =>
                {
                    b.Property<int>("ProductStoreSaleId")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("ProductStoreSaleId"));

                    b.Property<bool>("IsDeleted")
                        .HasColumnType("bit");

                    b.Property<bool>("IsNonBrandPromotion")
                        .HasColumnType("bit");

                    b.Property<bool>("IsPromotion")
                        .HasColumnType("bit");

                    b.Property<DateTime?>("LastModified")
                        .HasColumnType("datetime2");

                    b.Property<int?>("NonBrandPromotionId")
                        .HasColumnType("int");

                    b.Property<decimal>("OverriddenDiscountAmount")
                        .HasColumnType("decimal(18,2)");

                    b.Property<bool>("PriceOverriden")
                        .HasColumnType("bit");

                    b.Property<int>("ProductStoreId")
                        .HasColumnType("int");

                    b.Property<int?>("PromotionId")
                        .HasColumnType("int");

                    b.Property<int>("Quantity")
                        .HasColumnType("int");

                    b.Property<int>("SaleId")
                        .HasColumnType("int");

                    b.Property<bool>("StaffDiscount")
                        .HasColumnType("bit");

                    b.Property<decimal>("StaffDiscountedAmount")
                        .HasColumnType("decimal(18,2)");

                    b.Property<decimal>("Total")
                        .HasColumnType("decimal(18,2)");

                    b.HasKey("ProductStoreSaleId");

                    b.HasIndex("NonBrandPromotionId");

                    b.HasIndex("ProductStoreId");

                    b.HasIndex("PromotionId");

                    b.HasIndex("SaleId");

                    b.ToTable("ProductStoreSales");
                });

            modelBuilder.Entity("POS.Core.Models.Promotion", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("Id"));

                    b.Property<DateTime>("EndTime")
                        .HasColumnType("datetime2");

                    b.Property<string>("Identifire")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)");

                    b.Property<bool>("IsDeleted")
                        .HasColumnType("bit");

                    b.Property<DateTime?>("LastModified")
                        .HasColumnType("datetime2");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)");

                    b.Property<decimal?>("PromotionPrice")
                        .HasColumnType("decimal(18,2)");

                    b.Property<int>("Quantity")
                        .HasColumnType("int");

                    b.Property<DateTime>("StartTime")
                        .HasColumnType("datetime2");

                    b.Property<int>("Type")
                        .HasColumnType("int");

                    b.HasKey("Id");

                    b.ToTable("Promotions");
                });

            modelBuilder.Entity("POS.Core.Models.QuickAccessButton", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("Id"));

                    b.Property<string>("ButtonText")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)");

                    b.Property<int>("ProductId")
                        .HasColumnType("int");

                    b.Property<int>("StoreId")
                        .HasColumnType("int");

                    b.HasKey("Id");

                    b.HasIndex("ProductId");

                    b.HasIndex("StoreId");

                    b.ToTable("QuickAccessButtons");
                });

            modelBuilder.Entity("POS.Core.Models.Refund", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("Id"));

                    b.Property<decimal>("Amount")
                        .HasColumnType("decimal(18,2)");

                    b.Property<DateTime>("Date")
                        .HasColumnType("datetime2");

                    b.Property<bool>("IsDeleted")
                        .HasColumnType("bit");

                    b.Property<DateTime?>("LastModified")
                        .HasColumnType("datetime2");

                    b.Property<int>("ProductStoreSaleId")
                        .HasColumnType("int");

                    b.Property<int>("Quantity")
                        .HasColumnType("int");

                    b.HasKey("Id");

                    b.HasIndex("ProductStoreSaleId");

                    b.ToTable("Refunds");
                });

            modelBuilder.Entity("POS.Core.Models.SafeDrop", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("Id"));

                    b.Property<decimal>("Amount")
                        .HasColumnType("decimal(18,2)");

                    b.Property<bool>("IsDeleted")
                        .HasColumnType("bit");

                    b.Property<DateTime?>("LastModified")
                        .HasColumnType("datetime2");

                    b.Property<int>("StoreId")
                        .HasColumnType("int");

                    b.Property<DateTime>("Time")
                        .HasColumnType("datetime2");

                    b.Property<int>("shiftId")
                        .HasColumnType("int");

                    b.HasKey("Id");

                    b.HasIndex("StoreId");

                    b.HasIndex("shiftId");

                    b.ToTable("SafeDrops");
                });

            modelBuilder.Entity("POS.Core.Models.Sale", b =>
                {
                    b.Property<int>("SaleId")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("SaleId"));

                    b.Property<string>("Barcode")
                        .HasColumnType("nvarchar(max)");

                    b.Property<decimal?>("CashTendered")
                        .HasColumnType("decimal(18,2)");

                    b.Property<decimal>("Cashback")
                        .HasColumnType("decimal(18,2)");

                    b.Property<DateTime>("Date")
                        .HasColumnType("datetime2");

                    b.Property<bool>("IsDeleted")
                        .HasColumnType("bit");

                    b.Property<bool>("IsOnHold")
                        .HasColumnType("bit");

                    b.Property<DateTime?>("LastModified")
                        .HasColumnType("datetime2");

                    b.Property<int>("ShiftId")
                        .HasColumnType("int");

                    b.Property<decimal>("TotalValue")
                        .HasColumnType("decimal(18,2)");

                    b.HasKey("SaleId");

                    b.HasIndex("ShiftId");

                    b.ToTable("Sales");
                });

            modelBuilder.Entity("POS.Core.Models.SalePaymentMethod2", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("Id"));

                    b.Property<decimal>("Amount")
                        .HasColumnType("decimal(18,2)");

                    b.Property<bool>("IsDeleted")
                        .HasColumnType("bit");

                    b.Property<DateTime?>("LastModified")
                        .HasColumnType("datetime2");

                    b.Property<int>("PaymentMethodId")
                        .HasColumnType("int");

                    b.Property<int>("SaleId")
                        .HasColumnType("int");

                    b.HasKey("Id");

                    b.HasIndex("PaymentMethodId");

                    b.HasIndex("SaleId", "PaymentMethodId")
                        .IsUnique();

                    b.ToTable("SalePaymentMethods");
                });

            modelBuilder.Entity("POS.Core.Models.Shift", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("Id"));

                    b.Property<int?>("CashierId")
                        .HasColumnType("int");

                    b.Property<DateTime>("EndTime")
                        .HasColumnType("datetime2");

                    b.Property<bool>("InProgress")
                        .HasColumnType("bit");

                    b.Property<bool>("IsDeleted")
                        .HasColumnType("bit");

                    b.Property<DateTime?>("LastModified")
                        .HasColumnType("datetime2");

                    b.Property<int?>("PosMachineId")
                        .HasColumnType("int");

                    b.Property<DateTime>("StartTime")
                        .HasColumnType("datetime2");

                    b.Property<int?>("StoreDayId")
                        .HasColumnType("int");

                    b.HasKey("Id");

                    b.HasIndex("CashierId");

                    b.HasIndex("PosMachineId");

                    b.HasIndex("StoreDayId");

                    b.ToTable("Shifts");
                });

            modelBuilder.Entity("POS.Core.Models.SpecialProductSale", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("Id"));

                    b.Property<decimal>("Amount")
                        .HasColumnType("decimal(18,2)");

                    b.Property<bool>("IsDeleted")
                        .HasColumnType("bit");

                    b.Property<DateTime?>("LastModified")
                        .HasColumnType("datetime2");

                    b.Property<int>("ProductId")
                        .HasColumnType("int");

                    b.Property<int>("SaleId")
                        .HasColumnType("int");

                    b.HasKey("Id");

                    b.HasIndex("ProductId");

                    b.HasIndex("SaleId");

                    b.ToTable("SpecialProductSales");
                });

            modelBuilder.Entity("POS.Core.Models.Store", b =>
                {
                    b.Property<int>("StoreId")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("StoreId"));

                    b.Property<bool>("AllowNegativeInventory")
                        .HasColumnType("bit");

                    b.Property<int?>("BrandId")
                        .HasColumnType("int");

                    b.Property<int?>("CompanyId")
                        .HasColumnType("int");

                    b.Property<bool>("IsDeleted")
                        .HasColumnType("bit");

                    b.Property<DateTime?>("LastModified")
                        .HasColumnType("datetime2");

                    b.Property<int?>("MainPosMachineId")
                        .HasColumnType("int");

                    b.Property<string>("StoreIdForAPI")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("StoreName")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("VatNo")
                        .HasColumnType("nvarchar(max)");

                    b.HasKey("StoreId");

                    b.HasIndex("BrandId");

                    b.HasIndex("CompanyId");

                    b.ToTable("Stores");
                });

            modelBuilder.Entity("POS.Core.Models.StoreDay", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("Id"));

                    b.Property<DateTime>("EndTime")
                        .HasColumnType("datetime2");

                    b.Property<bool>("InProgress")
                        .HasColumnType("bit");

                    b.Property<bool>("IsDeleted")
                        .HasColumnType("bit");

                    b.Property<DateTime?>("LastModified")
                        .HasColumnType("datetime2");

                    b.Property<DateTime>("StartTime")
                        .HasColumnType("datetime2");

                    b.Property<int>("StoreId")
                        .HasColumnType("int");

                    b.HasKey("Id");

                    b.HasIndex("StoreId");

                    b.ToTable("StoreDays");
                });

            modelBuilder.Entity("POS.Core.Models.StoreSubCategoryLimit", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("Id"));

                    b.Property<bool>("IsDeleted")
                        .HasColumnType("bit");

                    b.Property<DateTime?>("LastModified")
                        .HasColumnType("datetime2");

                    b.Property<int>("QuantityLimit")
                        .HasColumnType("int");

                    b.Property<int>("StoreId")
                        .HasColumnType("int");

                    b.Property<int>("SubCategoryId")
                        .HasColumnType("int");

                    b.HasKey("Id");

                    b.HasIndex("StoreId");

                    b.HasIndex("SubCategoryId");

                    b.ToTable("StoreSubCategoryLimits");
                });

            modelBuilder.Entity("POS.Core.Models.SubCatagory", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("Id"));

                    b.Property<string>("CashierAlert")
                        .HasColumnType("nvarchar(max)");

                    b.Property<int>("Catagory")
                        .HasColumnType("int");

                    b.Property<bool>("IsDeleted")
                        .HasColumnType("bit");

                    b.Property<DateTime?>("LastModified")
                        .HasColumnType("datetime2");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)");

                    b.Property<int>("StockType")
                        .HasColumnType("int");

                    b.HasKey("Id");

                    b.ToTable("SubCatagories");
                });

            modelBuilder.Entity("POS.Core.Models.User", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("Id"));

                    b.Property<string>("Discriminator")
                        .IsRequired()
                        .HasMaxLength(13)
                        .HasColumnType("nvarchar(13)");

                    b.Property<bool>("IsDeleted")
                        .HasColumnType("bit");

                    b.Property<DateTime?>("LastModified")
                        .HasColumnType("datetime2");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("Password")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("Role")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("UserId")
                        .HasColumnType("nvarchar(max)");

                    b.HasKey("Id");

                    b.ToTable("Users");

                    b.HasDiscriminator<string>("Discriminator").HasValue("User");

                    b.UseTphMappingStrategy();
                });

            modelBuilder.Entity("POS.Core.Models.Vat", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("Id"));

                    b.Property<string>("Code")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)");

                    b.Property<bool>("IsDeleted")
                        .HasColumnType("bit");

                    b.Property<DateTime?>("LastModified")
                        .HasColumnType("datetime2");

                    b.Property<string>("Rate")
                        .HasColumnType("nvarchar(max)");

                    b.Property<decimal>("Value")
                        .HasColumnType("decimal(18,2)");

                    b.HasKey("Id");

                    b.ToTable("Vats");
                });

            modelBuilder.Entity("POS.Core.Models.Void", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("Id"));

                    b.Property<bool>("IsDeleted")
                        .HasColumnType("bit");

                    b.Property<DateTime?>("LastModified")
                        .HasColumnType("datetime2");

                    b.Property<int>("PosId")
                        .HasColumnType("int");

                    b.Property<int>("StoreId")
                        .HasColumnType("int");

                    b.Property<DateTime>("VoidTime")
                        .HasColumnType("datetime2");

                    b.HasKey("Id");

                    b.ToTable("Voids");
                });

            modelBuilder.Entity("POS.Core.Models.VoidSaleItem", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("Id"));

                    b.Property<bool>("IsDeleted")
                        .HasColumnType("bit");

                    b.Property<DateTime?>("LastModified")
                        .HasColumnType("datetime2");

                    b.Property<string>("PLU")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)");

                    b.Property<int>("Qty")
                        .HasColumnType("int");

                    b.Property<decimal>("Total")
                        .HasColumnType("decimal(18,2)");

                    b.Property<int?>("VoidId")
                        .HasColumnType("int");

                    b.HasKey("Id");

                    b.HasIndex("VoidId");

                    b.ToTable("VoidSaleItems");
                });

            modelBuilder.Entity("POS.Core.Models.Accountant", b =>
                {
                    b.HasBaseType("POS.Core.Models.User");

                    b.HasDiscriminator().HasValue("Accountant");
                });

            modelBuilder.Entity("POS.Core.Models.Admin", b =>
                {
                    b.HasBaseType("POS.Core.Models.User");

                    b.HasDiscriminator().HasValue("Admin");
                });

            modelBuilder.Entity("POS.Core.Models.BackOffice", b =>
                {
                    b.HasBaseType("POS.Core.Models.User");

                    b.Property<int?>("StoreId")
                        .HasColumnType("int");

                    b.HasIndex("StoreId");

                    b.HasDiscriminator().HasValue("BackOffice");
                });

            modelBuilder.Entity("POS.Core.Models.Cashier", b =>
                {
                    b.HasBaseType("POS.Core.Models.User");

                    b.HasDiscriminator().HasValue("Cashier");
                });

            modelBuilder.Entity("POS.Core.Models.CashierStore", b =>
                {
                    b.HasOne("POS.Core.Models.Cashier", "Cashier")
                        .WithMany("CashierStores")
                        .HasForeignKey("CashierId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("POS.Core.Models.Store", "Store")
                        .WithMany("CashierStores")
                        .HasForeignKey("StoreId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("Cashier");

                    b.Navigation("Store");
                });

            modelBuilder.Entity("POS.Core.Models.Division", b =>
                {
                    b.HasOne("POS.Core.Models.SubCatagory", "SubCatagory")
                        .WithMany("Divisions")
                        .HasForeignKey("SubCatagoryId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("SubCatagory");
                });

            modelBuilder.Entity("POS.Core.Models.IpAddress", b =>
                {
                    b.HasOne("POS.Core.Models.Accountant", null)
                        .WithMany("IpAddresses")
                        .HasForeignKey("AccountantId");

                    b.HasOne("POS.Core.Models.BackOffice", null)
                        .WithMany("IpAddresses")
                        .HasForeignKey("BackOfficeId");

                    b.HasOne("POS.Core.Models.User", "User")
                        .WithMany()
                        .HasForeignKey("UserId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("User");
                });

            modelBuilder.Entity("POS.Core.Models.MorrisonOrder", b =>
                {
                    b.HasOne("POS.Core.Models.Store", "Store")
                        .WithMany()
                        .HasForeignKey("StoreId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("Store");
                });

            modelBuilder.Entity("POS.Core.Models.MorrisonsOrderItem", b =>
                {
                    b.HasOne("POS.Core.Models.MorrisonOrder", "MorrisonOrder")
                        .WithMany("OrderItems")
                        .HasForeignKey("MorrisonOrderId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("MorrisonOrder");
                });

            modelBuilder.Entity("POS.Core.Models.NextOrderProduct", b =>
                {
                    b.HasOne("POS.Core.Models.Store", "Store")
                        .WithMany()
                        .HasForeignKey("StoreId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("Store");
                });

            modelBuilder.Entity("POS.Core.Models.NonBrandPromotion", b =>
                {
                    b.HasOne("POS.Core.Models.BackOffice", "CreatedBy")
                        .WithMany()
                        .HasForeignKey("CreatedById")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("POS.Core.Models.Store", "Store")
                        .WithMany()
                        .HasForeignKey("StoreId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("CreatedBy");

                    b.Navigation("Store");
                });

            modelBuilder.Entity("POS.Core.Models.PaidOut", b =>
                {
                    b.HasOne("POS.Core.Models.PaidOutOption", "PaidOutOption")
                        .WithMany()
                        .HasForeignKey("PaidOutOptionId");

                    b.HasOne("POS.Core.Models.Shift", "Shift")
                        .WithMany()
                        .HasForeignKey("ShiftId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("POS.Core.Models.Store", "Store")
                        .WithMany()
                        .HasForeignKey("StoreId");

                    b.Navigation("PaidOutOption");

                    b.Navigation("Shift");

                    b.Navigation("Store");
                });

            modelBuilder.Entity("POS.Core.Models.PosMachine", b =>
                {
                    b.HasOne("POS.Core.Models.Store", "Store")
                        .WithMany("PosMachines")
                        .HasForeignKey("StoreId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("Store");
                });

            modelBuilder.Entity("POS.Core.Models.Product", b =>
                {
                    b.HasOne("POS.Core.Models.Brand", "Brand")
                        .WithMany("Products")
                        .HasForeignKey("BrandId");

                    b.HasOne("POS.Core.Models.Division", "Division")
                        .WithMany("Products")
                        .HasForeignKey("DivisionId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("POS.Core.Models.NonBrandPromotion", "NonBrandPromotion")
                        .WithMany("Products")
                        .HasForeignKey("NonBrandPromotionId");

                    b.HasOne("POS.Core.Models.Promotion", "Promotion")
                        .WithMany("Products")
                        .HasForeignKey("PromotionId");

                    b.HasOne("POS.Core.Models.Vat", "Vat")
                        .WithMany("Products")
                        .HasForeignKey("VatId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("Brand");

                    b.Navigation("Division");

                    b.Navigation("NonBrandPromotion");

                    b.Navigation("Promotion");

                    b.Navigation("Vat");
                });

            modelBuilder.Entity("POS.Core.Models.ProductStore", b =>
                {
                    b.HasOne("POS.Core.Models.Product", "Product")
                        .WithMany("ProductStores")
                        .HasForeignKey("ProductId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("POS.Core.Models.Store", "Store")
                        .WithMany("ProductStores")
                        .HasForeignKey("StoreId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("Product");

                    b.Navigation("Store");
                });

            modelBuilder.Entity("POS.Core.Models.ProductStoreSale", b =>
                {
                    b.HasOne("POS.Core.Models.NonBrandPromotion", "NonBrandPromotion")
                        .WithMany()
                        .HasForeignKey("NonBrandPromotionId");

                    b.HasOne("POS.Core.Models.ProductStore", "ProductStore")
                        .WithMany("ProductStoreSales")
                        .HasForeignKey("ProductStoreId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("POS.Core.Models.Promotion", "Promotion")
                        .WithMany()
                        .HasForeignKey("PromotionId");

                    b.HasOne("POS.Core.Models.Sale", "Sale")
                        .WithMany("ProductStoreSales")
                        .HasForeignKey("SaleId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("NonBrandPromotion");

                    b.Navigation("ProductStore");

                    b.Navigation("Promotion");

                    b.Navigation("Sale");
                });

            modelBuilder.Entity("POS.Core.Models.QuickAccessButton", b =>
                {
                    b.HasOne("POS.Core.Models.Product", "Product")
                        .WithMany()
                        .HasForeignKey("ProductId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("POS.Core.Models.Store", "Store")
                        .WithMany()
                        .HasForeignKey("StoreId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("Product");

                    b.Navigation("Store");
                });

            modelBuilder.Entity("POS.Core.Models.Refund", b =>
                {
                    b.HasOne("POS.Core.Models.ProductStoreSale", "ProductStoreSale")
                        .WithMany()
                        .HasForeignKey("ProductStoreSaleId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("ProductStoreSale");
                });

            modelBuilder.Entity("POS.Core.Models.SafeDrop", b =>
                {
                    b.HasOne("POS.Core.Models.Store", "Store")
                        .WithMany()
                        .HasForeignKey("StoreId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("POS.Core.Models.Shift", "Shift")
                        .WithMany()
                        .HasForeignKey("shiftId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("Shift");

                    b.Navigation("Store");
                });

            modelBuilder.Entity("POS.Core.Models.Sale", b =>
                {
                    b.HasOne("POS.Core.Models.Shift", "Shift")
                        .WithMany()
                        .HasForeignKey("ShiftId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("Shift");
                });

            modelBuilder.Entity("POS.Core.Models.SalePaymentMethod2", b =>
                {
                    b.HasOne("POS.Core.Models.PaymentMethod", "PaymentMethod")
                        .WithMany("SalePaymentMethods")
                        .HasForeignKey("PaymentMethodId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("POS.Core.Models.Sale", "Sale")
                        .WithMany("SalePaymentMethods")
                        .HasForeignKey("SaleId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("PaymentMethod");

                    b.Navigation("Sale");
                });

            modelBuilder.Entity("POS.Core.Models.Shift", b =>
                {
                    b.HasOne("POS.Core.Models.Cashier", "Cashier")
                        .WithMany()
                        .HasForeignKey("CashierId");

                    b.HasOne("POS.Core.Models.PosMachine", "PosMachine")
                        .WithMany()
                        .HasForeignKey("PosMachineId");

                    b.HasOne("POS.Core.Models.StoreDay", "StoreDay")
                        .WithMany("Shifts")
                        .HasForeignKey("StoreDayId");

                    b.Navigation("Cashier");

                    b.Navigation("PosMachine");

                    b.Navigation("StoreDay");
                });

            modelBuilder.Entity("POS.Core.Models.SpecialProductSale", b =>
                {
                    b.HasOne("POS.Core.Models.Product", "Product")
                        .WithMany()
                        .HasForeignKey("ProductId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("POS.Core.Models.Sale", "Sale")
                        .WithMany("SpecialProductSales")
                        .HasForeignKey("SaleId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("Product");

                    b.Navigation("Sale");
                });

            modelBuilder.Entity("POS.Core.Models.Store", b =>
                {
                    b.HasOne("POS.Core.Models.Brand", "Brand")
                        .WithMany("Stores")
                        .HasForeignKey("BrandId");

                    b.HasOne("POS.Core.Models.Company", "Company")
                        .WithMany("Stores")
                        .HasForeignKey("CompanyId");

                    b.Navigation("Brand");

                    b.Navigation("Company");
                });

            modelBuilder.Entity("POS.Core.Models.StoreDay", b =>
                {
                    b.HasOne("POS.Core.Models.Store", "Store")
                        .WithMany()
                        .HasForeignKey("StoreId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("Store");
                });

            modelBuilder.Entity("POS.Core.Models.StoreSubCategoryLimit", b =>
                {
                    b.HasOne("POS.Core.Models.Store", "Store")
                        .WithMany("SubCategoryLimits")
                        .HasForeignKey("StoreId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("POS.Core.Models.SubCatagory", "SubCategory")
                        .WithMany("StoreLimits")
                        .HasForeignKey("SubCategoryId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("Store");

                    b.Navigation("SubCategory");
                });

            modelBuilder.Entity("POS.Core.Models.VoidSaleItem", b =>
                {
                    b.HasOne("POS.Core.Models.Void", null)
                        .WithMany("VoidSaleItems")
                        .HasForeignKey("VoidId");
                });

            modelBuilder.Entity("POS.Core.Models.BackOffice", b =>
                {
                    b.HasOne("POS.Core.Models.Store", "Store")
                        .WithMany()
                        .HasForeignKey("StoreId");

                    b.Navigation("Store");
                });

            modelBuilder.Entity("POS.Core.Models.Brand", b =>
                {
                    b.Navigation("Products");

                    b.Navigation("Stores");
                });

            modelBuilder.Entity("POS.Core.Models.Company", b =>
                {
                    b.Navigation("Stores");
                });

            modelBuilder.Entity("POS.Core.Models.Division", b =>
                {
                    b.Navigation("Products");
                });

            modelBuilder.Entity("POS.Core.Models.MorrisonOrder", b =>
                {
                    b.Navigation("OrderItems");
                });

            modelBuilder.Entity("POS.Core.Models.NonBrandPromotion", b =>
                {
                    b.Navigation("Products");
                });

            modelBuilder.Entity("POS.Core.Models.PaymentMethod", b =>
                {
                    b.Navigation("SalePaymentMethods");
                });

            modelBuilder.Entity("POS.Core.Models.Product", b =>
                {
                    b.Navigation("ProductStores");
                });

            modelBuilder.Entity("POS.Core.Models.ProductStore", b =>
                {
                    b.Navigation("ProductStoreSales");
                });

            modelBuilder.Entity("POS.Core.Models.Promotion", b =>
                {
                    b.Navigation("Products");
                });

            modelBuilder.Entity("POS.Core.Models.Sale", b =>
                {
                    b.Navigation("ProductStoreSales");

                    b.Navigation("SalePaymentMethods");

                    b.Navigation("SpecialProductSales");
                });

            modelBuilder.Entity("POS.Core.Models.Store", b =>
                {
                    b.Navigation("CashierStores");

                    b.Navigation("PosMachines");

                    b.Navigation("ProductStores");

                    b.Navigation("SubCategoryLimits");
                });

            modelBuilder.Entity("POS.Core.Models.StoreDay", b =>
                {
                    b.Navigation("Shifts");
                });

            modelBuilder.Entity("POS.Core.Models.SubCatagory", b =>
                {
                    b.Navigation("Divisions");

                    b.Navigation("StoreLimits");
                });

            modelBuilder.Entity("POS.Core.Models.Vat", b =>
                {
                    b.Navigation("Products");
                });

            modelBuilder.Entity("POS.Core.Models.Void", b =>
                {
                    b.Navigation("VoidSaleItems");
                });

            modelBuilder.Entity("POS.Core.Models.Accountant", b =>
                {
                    b.Navigation("IpAddresses");
                });

            modelBuilder.Entity("POS.Core.Models.BackOffice", b =>
                {
                    b.Navigation("IpAddresses");
                });

            modelBuilder.Entity("POS.Core.Models.Cashier", b =>
                {
                    b.Navigation("CashierStores");
                });
#pragma warning restore 612, 618
        }
    }
}
