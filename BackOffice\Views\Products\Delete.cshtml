﻿@model POS.Core.Models.Product

@{
    ViewData["Title"] = "Delete Product";
}

<h1>Delete Product</h1>

<div>
    <h4>Product</h4>
    <hr />
    <dl class="row">
        <dt class="col-sm-2">Name</dt>
        <dd class="col-sm-10">@Model.Name</dd>

       

        <dt class="col-sm-2">PLU</dt>
        <dd class="col-sm-10">@Model.PLU</dd>

        <dt class="col-sm-2">Barcode</dt>
        <dd class="col-sm-10">@Model.Barcode</dd>

        <dt class="col-sm-2">Brand</dt>
        <dd class="col-sm-10">@Model.Brand.Name</dd>

        
    </dl>
</div>

<form asp-action="DeleteConfirmed" method="post">
    <input type="hidden" asp-for="Id" />
    <button type="submit" class="btn btn-danger">Delete</button>
    <a asp-action="Index" class="btn btn-secondary">Back to List</a>
</form>
