﻿@model BackOffice.ViewModels.UserViewModel

<h2>Create User</h2>

<form asp-action="Create" method="post">
    <div>
        <label asp-for="UserType">User Type:</label>
        <select asp-for="UserType">
            <option value="Accountant">Accountant</option>
            <option value="BackOffice">BackOffice</option>
            <option value="Admin">Admin</option>
            <option value="Cashier">Cashier</option>
        </select>
    </div>

    <div>
        <label asp-for="UserId">User ID:</label>
        <input asp-for="UserId" />
    </div>

    <div>
        <label asp-for="Name">Name:</label>
        <input asp-for="Name" />
    </div>

    <div>
        <label asp-for="Password">Password:</label>
        <input asp-for="Password" type="password" />
    </div>

    <div>
        <label>IP Addresses (comma-separated):</label>
        <input asp-for="IpAddresses" />
    </div>

    <button type="submit">Create</button>
</form>