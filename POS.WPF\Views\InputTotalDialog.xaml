﻿<Window x:Class="POS.WPF.Views.InputTotalDialog"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        Title="InputTotalDialog" 
        Height="450" 
        Width="800"
        Background="#004e7c">
    <!-- Match main window background -->
    <Grid Margin="20">
        <!-- Centered Content -->
        <StackPanel VerticalAlignment="Center" HorizontalAlignment="Center">
            <!-- Styled TextBox -->
            <TextBox 
                x:Name="txtTotal"
                Width="300"
                Height="50"
                FontSize="24"
                Background="#0a688d"
                Foreground="White"
                Margin="0 0 0 20"
                HorizontalContentAlignment="Center"
                VerticalContentAlignment="Center"/>
            <Button 
                Content="CONFIRM"
                Width="150"
                Height="50"
                FontSize="18"
                Background="#004e7c"
                Foreground="White"
                Click="btnConfirm_Click"/>

            <Button 
     Content="CANCEL"
     Width="150"
     Height="50"
     FontSize="18"
     Background="#004e7c"
     Foreground="White"
     Click="Button_Click"/>
            <!-- Confirm Button -->
        </StackPanel>
    </Grid>
</Window>