﻿using Integral.Library.GuardianClient;
using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Windows.Forms;
using static Integral.Library.GuardianClient.TransactionHook;

namespace GuardianConnectionDemoWinformss
{
    public partial class Form1 : Form
    {
        public Form1()
        {
            InitializeComponent();
        }

        private void btnSend_Click(object sender, EventArgs e)
        {

            // Validate the amount input
            if (!decimal.TryParse(txtAmount.Text, out decimal amount))
            {
                return;
            }

            try
            {
                int amountInMinorUnits = (int)(amount * 100);
                var tillInfo = new TillInformation();
                var transactionHook = new TransactionHook();
                var transactionInfo = new TransactionInfo();

                bool isTransactionSuccessful = transactionHook.Process(
                    TRANSACTIONHOOK_TRANSACTIONTYPE.INT_TT_SALE,
                    amountInMinorUnits,
                    ref tillInfo,
                    ref transactionInfo
                );

                if (isTransactionSuccessful)
                {

                }
                else
                {
                }
            }
            catch (Exception ex)
            {
            }
        }
    }
}
