﻿<Window x:Class="POS.WPF.MainWindow"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
        xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
        xmlns:local="clr-namespace:POS.WPF.Views"
        mc:Ignorable="d"
        Title="MainWindow" Height="503" Width="823" WindowState="Maximized">



    <Grid Background="#004e7c" >


        <!-- Layout Root -->
        <Grid.ColumnDefinitions>
            <ColumnDefinition Width="3*" />
            <ColumnDefinition Width="1*" />

        </Grid.ColumnDefinitions>

        <Grid Grid.Column="0">

            <Grid>
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="2*" />
                    <ColumnDefinition Width="1*" />

                </Grid.ColumnDefinitions>

                <Grid.RowDefinitions>
                    <RowDefinition Height="0.5*"></RowDefinition>
                    <RowDefinition Height="2*"></RowDefinition>
                    <RowDefinition Height="1*"></RowDefinition>
                    <RowDefinition Height="auto"></RowDefinition>


                </Grid.RowDefinitions>

                <!-- search and button -->
                <Grid Grid.Row="0" Grid.Column="0" Grid.ColumnSpan="2">

                    <Grid.ColumnDefinitions>
                        <ColumnDefinition Width="6*" />
                        <ColumnDefinition Width="*" />
                    </Grid.ColumnDefinitions>

                    <TextBox x:Name="txtInput" Grid.Column="0" Margin="10" FontSize="24" Height="50"
Background="#0a688d" Foreground="White" VerticalAlignment="Center"
Text="Enter PLU" />

                    <!-- Button -->
                    <Button Grid.Column="1" Margin="10" FontSize="24" Height="50" Click="Button_Click">
                        <Image Source="/Images/enter-key.png" Height="30" Width="30"/>
                    </Button>
                </Grid>

                <!-- Left Section (Product Table) -->
                <DataGrid x:Name="MyDataGrid" 
  CanUserAddRows="False"
  Grid.Row="1" 
  AutoGenerateColumns="False" 
  Margin="10" 
  Grid.Column="0"
  
  Foreground="White" 
  BorderBrush="Transparent" 
  HeadersVisibility="Column" 
  FontSize="16" 
  ItemsSource="{Binding Products}"
  SelectionMode="Single" 
  SelectionUnit="FullRow">

                    <DataGrid.Background>
                        <ImageBrush  
            Opacity="0.3"  
            Stretch="Uniform"
            AlignmentX="Center"
            AlignmentY="Center"/>
                    </DataGrid.Background>

                    <DataGrid.RowBackground>
                        <SolidColorBrush Color="Transparent"/>
                    </DataGrid.RowBackground>

                    <DataGrid.ColumnHeaderStyle>
                        <Style TargetType="DataGridColumnHeader">
                            <Setter Property="Background" Value="Green" />
                            <Setter Property="Foreground" Value="White" />
                            <Setter Property="HorizontalContentAlignment" Value="Center" />
                            <Setter Property="FontSize" Value="16" />
                        </Style>
                    </DataGrid.ColumnHeaderStyle>
                    <DataGrid.CellStyle>
                        <Style TargetType="DataGridCell">
                            <Setter Property="Background" Value="Green" />
                            <Setter Property="Foreground" Value="White" />
                            <Setter Property="HorizontalContentAlignment" Value="Center" />
                            <Setter Property="FontSize" Value="16" />
                        </Style>
                    </DataGrid.CellStyle>

                    <DataGrid.Columns>
                        <!-- Customized Product Name Column -->
                        <DataGridTemplateColumn Header="Product Name" Width="*">
                            <DataGridTemplateColumn.CellTemplate>
                                <DataTemplate>
                                    <StackPanel Orientation="Vertical" MouseDown="OnProductCellClick">
                                        <TextBlock Text="{Binding ProductName}" FontWeight="Bold" FontSize="14" />
                                        <TextBlock Text="{Binding PLU}" FontSize="12" Foreground="Gray" />
                                    </StackPanel>
                                </DataTemplate>
                            </DataGridTemplateColumn.CellTemplate>
                        </DataGridTemplateColumn>

                        <!-- Unit Price Column with Auto Width -->
                        <DataGridTextColumn Header="Unit Price" Binding="{Binding UnitPrice}" Width="Auto" />

                        <DataGridTemplateColumn Header="Qty" Width="120">
                            <DataGridTemplateColumn.CellTemplate>
                                <DataTemplate>
                                    <StackPanel Orientation="Horizontal" HorizontalAlignment="Center">
                                        <!-- Decrease Button -->
                                        <Button Content="-" Width="20" Height="20" Margin="5,0,5,0"
                Command="{Binding DataContext.DecreaseQuantityCommand, RelativeSource={RelativeSource AncestorType=DataGrid}}"
                CommandParameter="{Binding}" />
                                        <!-- Quantity Display -->
                                        <TextBlock Text="{Binding Quantity}" VerticalAlignment="Center" FontWeight="Bold" />
                                        <!-- Increase Button -->
                                        <Button Content="+" Width="20" Height="20" Margin="5,0,5,0"
                Command="{Binding DataContext.IncreaseQuantityCommand, RelativeSource={RelativeSource AncestorType=DataGrid}}"
                CommandParameter="{Binding}" />
                                    </StackPanel>
                                </DataTemplate>
                            </DataGridTemplateColumn.CellTemplate>
                        </DataGridTemplateColumn>

                        <!-- Total Column with Auto Width -->
                        <DataGridTemplateColumn Header="Total £" Width="Auto">
                            <DataGridTemplateColumn.CellTemplate>
                                <DataTemplate>
                                    <StackPanel Orientation="Vertical" MouseDown="OnProductCellClick">
                                        <TextBlock Text="{Binding Total}" FontWeight="Bold" FontSize="14" />
                                        <TextBlock Text="{Binding Note}" FontSize="12" Foreground="Blue" />
                                    </StackPanel>
                                </DataTemplate>
                            </DataGridTemplateColumn.CellTemplate>
                        </DataGridTemplateColumn>

                        <DataGridTemplateColumn>
                            <DataGridTemplateColumn.CellTemplate>
                                <DataTemplate>
                                    <StackPanel Orientation="Horizontal" HorizontalAlignment="Center">
                                        <Button Width="20" Height="20" Margin="5,0,5,0"
                            Command="{Binding DataContext.DeleteProductCommand, RelativeSource={RelativeSource AncestorType=DataGrid}}"
                            CommandParameter="{Binding}">
                                            <Image Source="/Images/delivery.png" Stretch="Uniform" />
                                        </Button>
                                    </StackPanel>
                                </DataTemplate>
                            </DataGridTemplateColumn.CellTemplate>
                        </DataGridTemplateColumn>
                    </DataGrid.Columns>
                </DataGrid>



                <!-- Bottom Section (Totals) -->
                <StackPanel Grid.Row="2" Grid.Column="0" Orientation="Vertical" VerticalAlignment="Bottom" Margin="10">
                    <TextBlock Text="{Binding SubTotal, StringFormat='Sub Total : £{0:F2}'}" FontSize="18" Foreground="White" />
                    <TextBlock Text="{Binding Discount, StringFormat='Discount : £{0:F2}'}" FontSize="18" Foreground="White" />
                    <TextBlock Text="{Binding TotalAmount, StringFormat='Total Amount : £{0:F2}'}" FontSize="24" Foreground="Lime"  />
                    <TextBlock FontSize="18" x:Name="txtTotal" Foreground="White" Visibility="Hidden"  />

                </StackPanel>


                <Grid Grid.Row="1" Grid.Column="1">
                    <Grid.ColumnDefinitions>
                        <ColumnDefinition Width="*" />
                    </Grid.ColumnDefinitions>

                    <Grid.RowDefinitions>
                        <RowDefinition Height="10*"></RowDefinition>
                        <RowDefinition Height="Auto"></RowDefinition>
                        <RowDefinition Height="2.5*"></RowDefinition>
                    </Grid.RowDefinitions>

                    <!-- Logo at the top -->
                    <Image Source="/Images/EPOS LOGO.png" Grid.Row="0" Grid.Column="0" />

                    <!-- Buttons using Grid layout for better alignment -->
                    <Grid x:Name="newbuttonspanel" Grid.Row="1" Grid.Column="0" Margin="0,20,0,0">
                        <Grid.RowDefinitions>
                            <RowDefinition Height="Auto"/>
                            <RowDefinition Height="Auto"/>
                        </Grid.RowDefinitions>
                        <Grid.ColumnDefinitions>
                            <ColumnDefinition Width="*"/>
                            <ColumnDefinition Width="*"/>
                        </Grid.ColumnDefinitions>

                        <!-- Carry Bag Small Button (Green) at position (0,0) -->
                        <Button x:Name="BagSmall" Background="Green" Margin="5" Click="Button_Click_19" 
        Grid.Row="0" Grid.Column="0" Width="100" Height="80" HorizontalAlignment="Right">
                            <StackPanel Orientation="Vertical" HorizontalAlignment="Center">
                                <Image Source="/Images/icons8-paid-30.png" Width="30" Height="30"/>
                                <TextBlock Text="CARRY BAG SMALL" Foreground="White" FontWeight="Bold" 
                   TextAlignment="Center" TextWrapping="Wrap" FontSize="12"/>
                            </StackPanel>
                        </Button>


                        <!-- Carry Bag Big Button (Green) at position (0,1) -->
                        <Button x:Name="BagBig" Background="Green" Margin="5" Click="Button_Click_20" 
                                Grid.Row="0" Grid.Column="1" Width="100" Height="80" HorizontalAlignment="Left">
                            <StackPanel Orientation="Vertical" HorizontalAlignment="Center">
                                <Image Source="/Images/icons8-paid-30.png" Width="30" Height="30"/>
                                <TextBlock Text="CARRY BAG BIG" Foreground="White" FontWeight="Bold" 
                                           TextAlignment="Center" TextWrapping="Wrap" FontSize="12"/>
                            </StackPanel>
                        </Button>

                        <!-- Quick Access Products Button at position (1,0) -->
                        <Button x:Name="OpenQuickAccessButton" Background="Green" Margin="5,10,5,5" Click="OpenQuickAccessProducts_Click" 
                                Grid.Row="1" Grid.Column="0" Width="100" Height="80" HorizontalAlignment="Right">
                            <StackPanel Orientation="Vertical" HorizontalAlignment="Center">
                                <Image Source="/Images/change.png" Width="30" Height="30"/>
                                <TextBlock Text="QUICK PRODUCTS" Foreground="White" FontWeight="Bold" 
                                           TextAlignment="Center" TextWrapping="Wrap" FontSize="12"/>
                            </StackPanel>
                        </Button>
                    </Grid>
                </Grid>

                <!-- Numeric Keypad in its own row -->
                <local:NumericKeypad KeyPressed="NumericKeypad_KeyPressed" Grid.Row="2" Grid.Column="1" Margin="20,0,20,20"/>


                <StackPanel x:Name="dataPanel" Grid.Row="3" Grid.Column="0" Grid.ColumnSpan="2" Orientation="Horizontal"   >
                    <Label  x:Name="labelCashierName"></Label>
                    <Label  x:Name="labelShiftId"></Label>
                    <Label  x:Name="labelShiftStartTime"></Label>
                    <Label  x:Name="labelDayStartTime"></Label>
                    <Label  x:Name="labelCurrentTime"></Label>


                </StackPanel>

            </Grid>

            <!--<TextBlock  x:Name="txtStatus" Grid.Row="2" Grid.Column="0" Text="placeholder" FontSize="20" Foreground="Blue" />-->


        </Grid>


        <Grid Grid.Column="1">

            <UniformGrid x:Name="controlButtons" Rows="9" Columns="2" Grid.Row="0" Margin="5" Background="#0a688d">



                <Button Background="#004e7c" Margin="5" Click="Button_Click_1">
                    <StackPanel Orientation="Vertical" HorizontalAlignment="Center">
                        <Image Source="/Images/void.png" Width="30" Height="30"/>
                        <TextBlock Text="VOID" Foreground="White" FontWeight="Bold" TextAlignment="Center"/>
                    </StackPanel>
                </Button>

                <Button Background="#004e7c" Margin="5" Click="Button_Click_1">
                    <StackPanel Orientation="Vertical" HorizontalAlignment="Center">
                        <Image Source="/Images/question.png" Width="30" Height="30"/>
                        <TextBlock Text="PRICE INQUIRY" Foreground="White" FontWeight="Bold" TextAlignment="Center"/>
                    </StackPanel>
                </Button>

                <Button Background="#004e7c" Margin="5" Click="Button_Click_3">
                    <StackPanel Orientation="Vertical" HorizontalAlignment="Center">
                        <Image Source="/Images/credit-card.png" Width="30" Height="30"/>
                        <TextBlock Text="SELECT PAYMENT" Foreground="White" FontWeight="Bold" TextAlignment="Center"/>
                    </StackPanel>
                </Button>

                <Button Background="#004e7c" Margin="5" Click="Button_Click_2">
                    <StackPanel Orientation="Vertical" HorizontalAlignment="Center">
                        <Image Source="/Images/pause.png" Width="30" Height="30"/>
                        <TextBlock Text="HOLD TRANSACTION" Foreground="White" FontWeight="Bold" TextAlignment="Center"/>
                    </StackPanel>
                </Button>

                <Button Background="#004e7c" Margin="5" Click="Button_Click_4">
                    <StackPanel Orientation="Vertical" HorizontalAlignment="Center">
                        <Image Source="/Images/shield.png" Width="30" Height="30"/>
                        <TextBlock Text="SAFE DROP" Foreground="White" FontWeight="Bold" TextAlignment="Center"/>
                    </StackPanel>
                </Button>

                <Button Background="#004e7c" Margin="5" Click="Button_Click_5">
                    <StackPanel Orientation="Vertical" HorizontalAlignment="Center">
                        <Image Source="/Images/change.png" Width="30" Height="30"/>
                        <TextBlock Text="PRICE OVERRIDE" Foreground="White" FontWeight="Bold" TextAlignment="Center"/>
                    </StackPanel>
                </Button>

                <Button Background="#004e7c" Margin="5" Click="Button_Click_13">
                    <StackPanel Orientation="Vertical" HorizontalAlignment="Center">
                        <Image Source="/Images/money-back.png" Width="30" Height="30"/>
                        <TextBlock Text="REFUND" Foreground="White" FontWeight="Bold" TextAlignment="Center"/>
                    </StackPanel>
                </Button>

                <Button Background="#004e7c" Margin="5" Click="Button_Click_10">
                    <StackPanel Orientation="Vertical" HorizontalAlignment="Center">
                        <Image Source="/Images/reprint.png" Width="30" Height="30"/>
                        <TextBlock Text="REPRINT RECEIPT" Foreground="White" FontWeight="Bold" TextAlignment="Center"/>
                    </StackPanel>
                </Button>

                <Button Background="#004e7c" Margin="5" Click="Button_Click_6">
                    <StackPanel Orientation="Vertical" HorizontalAlignment="Center">
                        <Image Source="/Images/to-do-list.png" Width="30" Height="30"/>
                        <TextBlock Text="ON HOLD LIST" Foreground="White" FontWeight="Bold" TextAlignment="Center"/>
                    </StackPanel>
                </Button>

                <Button Background="#004e7c" Margin="5" Click="Button_Click_8">
                    <StackPanel Orientation="Vertical" HorizontalAlignment="Center">
                        <Image Source="/Images/discount.png" Width="30" Height="30"/>
                        <TextBlock Text="STAFF DISCOUNT" Foreground="White" FontWeight="Bold" TextAlignment="Center"/>
                    </StackPanel>
                </Button>

                <Button Background="#004e7c" Margin="5" Click="Button_Click_9">
                    <StackPanel Orientation="Vertical" HorizontalAlignment="Center">
                        <Image Source="/Images/icons8-paid-30.png" Width="30" Height="30"/>
                        <TextBlock Text="PAID OUT" Foreground="White" FontWeight="Bold" TextAlignment="Center"/>
                    </StackPanel>
                </Button>

                <Button Background="#004e7c" Margin="5" Click="Button_Click_12">
                    <StackPanel Orientation="Vertical" HorizontalAlignment="Center">
                        <Image Source="/Images/icons8-log-out-24.png" Width="30" Height="30"/>
                        <TextBlock Text="LOGOUT" Foreground="White" FontWeight="Bold" TextAlignment="Center"/>
                    </StackPanel>
                </Button>

                <Button Background="#004e7c" Margin="5" Click="Button_Click_14">
                    <StackPanel Orientation="Vertical" HorizontalAlignment="Center">
                        <Image Source="/Images/icons8-newspaper-50.png" Width="30" Height="30"/>
                        <TextBlock Text="NEWS PAPER" Foreground="White" FontWeight="Bold" TextAlignment="Center"/>
                    </StackPanel>
                </Button>

                <Button Background="#004e7c" Margin="5" Click="Button_Click_15">
                    <StackPanel Orientation="Vertical" HorizontalAlignment="Center">
                        <Image Source="/Images/icons8-paypoint-24.png" Width="30" Height="30"/>
                        <TextBlock Text="PAYPOINT" Foreground="White" FontWeight="Bold" TextAlignment="Center"/>
                    </StackPanel>
                </Button>

                <Button Background="#004e7c" Margin="5,5,0,5" Click="Button_Click_16" HorizontalAlignment="Left" Width="88">
                    <StackPanel Orientation="Vertical" HorizontalAlignment="Center">
                        <Image Source="/Images/icons8-hot-food-64.png" Width="30" Height="30"/>
                        <TextBlock Text="HOT FOOD" Foreground="White" FontWeight="Bold" TextAlignment="Center"/>
                    </StackPanel>
                </Button>

                <Button Background="#004e7c" Margin="5" Click="Button_Click_17">
                    <StackPanel Orientation="Vertical" HorizontalAlignment="Center">
                        <Image Source="/Images/third-party.png" Width="30" Height="30"/>
                        <TextBlock Text="3RD PARTY" Foreground="White" FontWeight="Bold" TextAlignment="Center"/>
                    </StackPanel>
                </Button>

                <Button Background="#004e7c" Margin="5" Click="Button_Click_7">
                    <StackPanel Orientation="Vertical" HorizontalAlignment="Center">
                        <Image Source="/Images/sign-out-option.png" Width="30" Height="30"/>
                        <TextBlock Text="SHIFT END" Foreground="White" FontWeight="Bold" TextAlignment="Center"/>
                    </StackPanel>
                </Button>

                <Button Background="#004e7c" Margin="5" Click="Button_Click_11">
                    <StackPanel Orientation="Vertical" HorizontalAlignment="Center">
                        <Image Source="/Images/power.png" Width="30" Height="30"/>
                        <TextBlock Text="DAY END" Foreground="White" FontWeight="Bold" TextAlignment="Center"/>
                    </StackPanel>
                </Button>

                <!--<Button Background="#004e7c" Margin="5" Click="Button_Click_18">
                    Temp-report-hook
                </Button>-->

                <!--<Button x:Name="OpenQuickAccessButton" Background="#004e7c" Margin="5" Click="OpenQuickAccessProducts_Click">
                    <StackPanel Orientation="Vertical" HorizontalAlignment="Center">
                        <Image Source="/Images/shopping.png" Width="30" Height="30"/>
                        <TextBlock Text="QUICK PRODUCTS" Foreground="White" FontWeight="Bold" TextAlignment="Center"/>
                    </StackPanel>
                </Button>-->




            </UniformGrid>



            <!-- Action Buttons -->



        </Grid>

        <!-- Right Section (Buttons and Keypad) -->
        <TextBox Grid.Row="4" Height="0" Width="0" x:Name="barcodeTextBox" KeyDown="BarcodeTextBox_KeyDown"/>


    </Grid>

</Window>
