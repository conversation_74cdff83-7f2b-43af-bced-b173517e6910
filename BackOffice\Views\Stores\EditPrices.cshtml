@model POS.Core.Models.Store

@{
    ViewData["Title"] = "Edit Prices";
}

<h1>Edit Store Prices</h1>

@if (TempData["SuccessMessage"] != null)
{
    <div class="alert alert-success">
        @TempData["SuccessMessage"]
    </div>
}

@if (TempData["ErrorMessage"] != null)
{
    <div class="alert alert-danger">
        @TempData["ErrorMessage"]
    </div>
}

<div class="row mb-3">
    <div class="col-md-6">
        <div class="card">
            <div class="card-header">
                <h5>Store Information</h5>
            </div>
            <div class="card-body">
                <p><strong>Store Name:</strong> @Model.StoreName</p>
                <p><strong>Brand:</strong> @Model.Brand?.Name</p>
                <p><strong>Company:</strong> @Model.Company?.Name</p>
            </div>
        </div>
    </div>
</div>

<!-- Division Selection -->
<div class="form-group mb-3">
    <label for="divisionDropdown">Select Division</label>
    <select id="divisionDropdown" class="form-control">
        <option value="">-- Select Division --</option>
        @foreach (var division in ViewBag.Divisions)
        {
            <option value="@division.Id">@division.Name</option>
        }
    </select>
</div>

<form asp-action="SavePrices" method="post">
    <input type="hidden" name="StoreId" value="@Model.StoreId" />
    
    <h3>Product Prices</h3>
    <table class="table table-striped">
        <thead>
            <tr>
                <th>Product Name</th>
                <th>Default Price</th>
                <th>Store Specific Price</th>
            </tr>
        </thead>
        <tbody id="productTableBody">
            @foreach (var productStore in Model.ProductStores)
            {
                <tr>
                    <td>
                        <input type="hidden" name="ProductStoreId" value="@productStore.ProductStoreId" />
                        @productStore.Product.Name
                    </td>
                    <td>
                        @productStore.Product.SellingPrice.ToString("C")
                    </td>
                    <td>
                        <input type="number" step="0.01" name="StoreSpecificPrice"
                               value="@(productStore.StoreSpecificPrice.HasValue ? productStore.StoreSpecificPrice.Value.ToString("0.00") : "")"
                               class="form-control" placeholder="N/A" />
                    </td>
                </tr>
            }
        </tbody>
    </table>

    <button type="submit" class="btn btn-primary">Save Prices</button>
    <a asp-action="Index" asp-controller="BackOffice" class="btn btn-secondary">Back</a>
</form>

@section Scripts {
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    <script>
        $(document).ready(function () {
            $('#divisionDropdown').change(function () {
                var selectedDivisionId = $(this).val();
                var storeId = '@Model.StoreId';

                if (selectedDivisionId && storeId) {
                    $.ajax({
                        url: '@Url.Action("GetProductsByDivisionForPrices", "Stores")',
                        type: 'GET',
                        data: { storeId: storeId, divisionId: selectedDivisionId },
                        success: function (result) {
                            // Update the product table body with the returned partial view
                            $('#productTableBody').html(result);
                        },
                        error: function () {
                            alert('An error occurred while fetching the products.');
                        }
                    });
                } else {
                    // Optionally handle the case when no division is selected
                    $('#productTableBody').html('');
                }
            });
        });
    </script>
}
