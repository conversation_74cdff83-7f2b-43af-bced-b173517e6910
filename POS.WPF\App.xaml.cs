﻿using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Hosting;
using NLog;
using POS.Core.Models;
using POS.WPF.LocalData;
using POS.WPF.Services;
using POS.WPF.Views;
using System.Configuration;
using System.Data;
using System.Windows;
using TimeManagement;

namespace POS.WPF;

/// <summary>
/// Interaction logic for App.xaml
/// </summary>
public partial class App : Application
{
    private static readonly Logger Logger = LogManager.GetCurrentClassLogger();
    private readonly IHost _host;

    public App()
    {
        _host = Host.CreateDefaultBuilder()
            .ConfigureServices((context, services) =>
            {
                // Register DbContexts
                services.AddSingleton<LocalContext>();
                services.AddSingleton<Data>();

                // Register Sync Services
                services.AddSingleton<ISyncService, SyncService>();
                services.AddHostedService<SyncBackgroundService>();

                // Register Windows
                services.AddTransient<LoginWindow>();
                services.AddTransient<MainWindow>();
            })
            .Build();
    }

    protected override async void OnStartup(StartupEventArgs e)
    {
        await _host.StartAsync();
        
        // Set display time provider to use local timezone
        DisplayTimeProvider.SetTimeZone(TimeZoneInfo.Local.Id);
        
    

        base.OnStartup(e);
        Logger.Info("Application started");
    }

    protected override async void OnExit(ExitEventArgs e)
    {
        using (_host)
        {
            await _host.StopAsync(TimeSpan.FromSeconds(5));
        }

        Logger.Info("Application exiting");
        base.OnExit(e);
    }
}

