﻿@model POS.Core.Models.Division

@{
    ViewData["Title"] = "Create Division";
}

<h2>Create Division</h2>

<form asp-action="Create">
    <div class="form-group">
        <label asp-for="Name" class="control-label"></label>
        <input asp-for="Name" class="form-control" required />
    </div>
    <div class="form-group">
        <label asp-for="SubCatagoryId" class="control-label">SubCategory</label>
        <select asp-for="SubCatagoryId" class="form-control" required>
            <option value="">-- Select --</option>
            @foreach (var sub in ViewBag.SubCatagories)
            {
                <option value="@sub.Id">@sub.Name</option>
            }
        </select>
    </div>
    <button type="submit" class="btn btn-success">Save</button>
    <a asp-action="Index" class="btn btn-secondary">Cancel</a>
</form>
