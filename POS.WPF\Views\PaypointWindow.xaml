﻿<Window x:Class="POS.WPF.Views.PaypointWindow"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        xmlns:local="clr-namespace:POS.WPF.Views"
        Title="Paypoint" Height="600" Width="1000">
    <Grid Background="#004e7c">
        <Grid.ColumnDefinitions>
            <ColumnDefinition Width="3*"/>
            <ColumnDefinition Width="1*"/>
        </Grid.ColumnDefinitions>

        <!-- Transaction Display -->
        <DataGrid Grid.Column="0" Margin="10" AutoGenerateColumns="False"
                  Background="{StaticResource DataGridBackground}"
                  HeadersVisibility="Column" FontSize="16">
            <DataGrid.ColumnHeaderStyle>
                <Style TargetType="DataGridColumnHeader">
                    <Setter Property="Background" Value="Green"/>
                    <Setter Property="Foreground" Value="White"/>
                    <Setter Property="FontSize" Value="16"/>
                </Style>
            </DataGrid.ColumnHeaderStyle>
            <DataGrid.Columns>
                <DataGridTextColumn Header="Item" Binding="{Binding ItemName}" Width="*"/>
                <DataGridTextColumn Header="Amount" Binding="{Binding Amount}" Width="150"/>
            </DataGrid.Columns>
        </DataGrid>

        <!-- Keypad Section -->
        <local:NumericKeypad Grid.Column="1" Margin="10" 
                            KeyPressed="NumericKeypad_KeyPressed"
                            Background="#0a688d"/>
    </Grid>
</Window>