﻿using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using POS.Core.Models;
using System.Text.Json;
using System.Text;
using Microsoft.AspNetCore.Components.Web;
using System.Net.Http.Headers;
using BackOffice.Models;
using System.Text.Json.Serialization;
using System.Security.Claims;
using TimeManagement;
using Microsoft.Extensions.Options;
using BackOffice.Configuration;

namespace BackOffice.Controllers
{
    /// <summary>
    /// Controller responsible for managing the next order functionality in the BackOffice system.
    /// This controller handles operations related to creating and managing orders for <PERSON>'s wholesale system.
    /// </summary>
    public class NextOrderController : Controller
    {
        private readonly Data _context;
        private readonly MorrisonsApiConfig _apiConfig;

        public NextOrderController(Data context, IOptions<MorrisonsApiConfig> apiConfig)
        {
            _context = context;
            _apiConfig = apiConfig.Value;
        }

        /// <summary>
        /// Displays the index page showing the list of products in the next order.
        /// GET: /NextOrder/
        /// </summary>
        /// <returns>View containing list of NextOrderProducts for the current store</returns>
        public async Task<IActionResult> Index()
        {
            //get the store associalted with the logged in backoffice
            var userId = int.Parse(User.Claims.FirstOrDefault(c => c.Type == ClaimTypes.NameIdentifier)?.Value);
            var storeId= _context.BackOffices.Find(userId).StoreId;

            var products = await _context.NextOrderProducts.Where(p => p.StoreId == storeId).ToListAsync();

            ViewBag.StoreId = storeId;  
            return View(products);
        }

        /// <summary>
        /// Processes an uploaded CSV file containing product orders.
        /// POST: /NextOrder/UploadCsv
        /// Expected CSV format: barcode,quantity (with header row)
        /// </summary>
        /// <param name="file">CSV file containing product orders</param>
        /// <returns>Redirects to Index with success/error message</returns>
        [HttpPost]
        public async Task<IActionResult> UploadCsv(IFormFile file)
        {
            if (file == null || file.Length == 0)
            {
                TempData["Error"] = "Please select a CSV file.";
                return RedirectToAction("Index");
            }

            var userId = int.Parse(User.Claims.FirstOrDefault(c => c.Type == ClaimTypes.NameIdentifier)?.Value);
            var storeId = _context.BackOffices.Find(userId).StoreId;

            using var reader = new StreamReader(file.OpenReadStream());
            var csv = await reader.ReadToEndAsync();
            var lines = csv.Split('\n').Skip(1); // Skip header row

            foreach (var line in lines)
            {
                var columns = line.Split(',');
                if (columns.Length < 2) continue;

                string barcode = columns[0].Trim();
                if (!int.TryParse(columns[1].Trim(), out int quantity)) continue;

                var existingProduct = await _context.NextOrderProducts
                    .FirstOrDefaultAsync(p => p.Barcode == barcode);

                if (existingProduct != null)
                {
                    existingProduct.Quantity += quantity;
                }
                else
                {
                    _context.NextOrderProducts.Add(new NextOrderProduct { Barcode = barcode, Quantity = quantity , StoreId = (int)storeId });
                }
            }

            await _context.SaveChangesAsync();
            TempData["Success"] = "CSV file processed successfully.";
            return RedirectToAction("Index");
        }

        /// <summary>
        /// Updates the quantity of a specific product in the next order.
        /// POST: /NextOrder/UpdateQuantity
        /// </summary>
        /// <param name="id">Product ID</param>
        /// <param name="quantity">New quantity value</param>
        /// <returns>Redirects to Index</returns>
        [HttpPost]
        public async Task<IActionResult> UpdateQuantity(int id, int quantity)
        {
            var product = await _context.NextOrderProducts.FindAsync(id);
            if (product != null)
            {
                product.Quantity = quantity;
                await _context.SaveChangesAsync();
            }
            return RedirectToAction("Index");
        }

        /// <summary>
        /// Removes a product from the next order list.
        /// POST: /NextOrder/Remove
        /// </summary>
        /// <param name="id">Product ID to remove</param>
        /// <returns>Redirects to Index</returns>
        [HttpPost]
        public async Task<IActionResult> Remove(int id)
        {
            try
            {
                var product = await _context.NextOrderProducts.FindAsync(id);
                if (product != null)
                {
                    _context.NextOrderProducts.Remove(product);
                    await _context.SaveChangesAsync();
                }
                
                return RedirectToAction("Index");
            }
            catch (Exception ex)
            {
                // Log the exception
                Console.WriteLine($"Error removing product: {ex.Message}");
                TempData["Error"] = "Failed to remove product. Please try again.";
                return RedirectToAction("Index");
            }
        }


        /// <summary>
        /// Places an order with Morrison's wholesale system.
        /// POST: /NextOrder/PlaceOrder
        /// </summary>
        /// <param name="storeId">ID of the store placing the order</param>
        /// <param name="deliveryDate">Requested delivery date for the order</param>
        /// <returns>Redirects to Index with order status</returns>
        public async Task<IActionResult> PlaceOrder(int storeId, DateTime deliveryDate)
        {
            try
            {
                // 1. Get products from your database
                var products = _context.Products
                    .Select(p => new { p.Barcode, p.PLU, p.PurchasePackSize })
                    .Where(p => _context.NextOrderProducts.Any(np => np.Barcode == p.Barcode && np.StoreId == storeId && np.IsDeleted == false))
                    .ToList();

                if (!products.Any())
                {
                    Console.WriteLine("No products found to order");
                    return RedirectToAction("Index");
                }

                // 2. Create order items from your products
                var orderItems = products.Select((product, index) => new
                {
                    quantityType = "CS", // Assuming you're ordering by cases
                    orderReferenceCode = "AM",
                    itemId = product.PLU.ToString(), // Using PLU as itemId
                    itemAlternateId = new
                    {
                        clientId = "", // Can be empty
                        barcodeEan = product.Barcode,
                        skuPin = product.PLU.ToString(), // Using PLU as skuPin
                        skuMin = product.PLU.ToString() // Using same as skuPin if you don't have MIN
                    },
                    priceOrderedCurrency = "GBP",
                    itemBaseType = "skuPin",
                    itemLineId = (index + 1).ToString(), // Sequential line number
                    quantityOrdered = GetOrderQuantity(product.Barcode), // You'll need to implement this
                }).ToArray();

                string shippingId =_context.Stores.Find(storeId).StoreIdForAPI;

                string orderId = GenerateOrderId(shippingId);

                

                // 3. Create the order payload
                var orderRequest = new
                {
                    orders = new
                    {
                        orderId = orderId,
                        shipToLocationId = shippingId,
                        orderReferenceCode = "CH", // You may need to determine this per product
                        messageType = "DAILY",
                        shipToDeliverAt = deliveryDate.ToString("yyyy-MM-dd"),
                        messageCreatedAt = CustomTimeProvider.Now.ToString("yyyy-MM-ddTHH:mm:ssZ"),
                        shipToDeliverLatestAt = deliveryDate.ToString("yyyy-MM-dd")
                    },
                    items = orderItems
                };

                // 4. Serialize and send the order
                var jsonContent = JsonSerializer.Serialize(orderRequest);
                var content = new StringContent(jsonContent, Encoding.UTF8, "application/json");

                // Using environment-specific configuration
                string url = $"{_apiConfig.ApiUrl}/{_apiConfig.CustomerId}/orders?apikey={_apiConfig.ApiKey}";
                string authToken = _apiConfig.AuthToken;

                HttpClientHandler handler = new HttpClientHandler
                {
                    ServerCertificateCustomValidationCallback = (message, cert, chain, errors) => true
                };

                using (HttpClient httpClient = new HttpClient(handler))
                {
                    httpClient.DefaultRequestHeaders.Authorization = new AuthenticationHeaderValue("Basic", authToken);
                    httpClient.DefaultRequestHeaders.Accept.Add(new MediaTypeWithQualityHeaderValue("application/json"));

                    HttpResponseMessage response = await httpClient.PostAsync(url, content);

                    if (response.IsSuccessStatusCode)
                    {
                        string responseContent = await response.Content.ReadAsStringAsync();
                        Console.WriteLine("Response: " + responseContent);

                        List<MorrisonsOrderItem> morrisonsOrderItems = new List<MorrisonsOrderItem>();

                        foreach (var item in _context.NextOrderProducts.Where(n => n.StoreId == storeId && n.IsDeleted == false))
                        {
                            morrisonsOrderItems.Add(new MorrisonsOrderItem
                            {
                                Barcode = item.Barcode,
                                Quantity = item.Quantity,
                                 

                            });
                        }


                        _context.MorrisonOrders.Add(new MorrisonOrder
                        {
                            StoreId = storeId,
                            GeneratedId = orderId
                            ,
                            CreatedDate = CustomTimeProvider.Now,
                            ExpectedDeliveryDate = deliveryDate,
                            OrderItems = morrisonsOrderItems, 
                            MorrisonOrderStatus = MorrisonOrderStatus.Created
                        });


                        _context.NextOrderProducts.RemoveRange(_context.NextOrderProducts.Where(n => n.StoreId == storeId));

                        await _context.SaveChangesAsync();

                        return RedirectToAction("Index");
                    }
                    else
                    {
                        Console.WriteLine($"Error: {response.StatusCode} - {await response.Content.ReadAsStringAsync()}");
                        return RedirectToAction("Index");
                    }
                }

            }
            catch (Exception ex)
            {
                Console.WriteLine($"Exception occurred: {ex.Message}");
                return RedirectToAction("Index");
            }
        }

   

        /// <summary>
        /// Retrieves the order quantity for a specific product barcode.
        /// </summary>
        /// <param name="barcode">Product barcode</param>
        /// <returns>Ordered quantity</returns>
        private int GetOrderQuantity(string barcode)
        {
            return _context.NextOrderProducts.FirstOrDefault(p => p.Barcode == barcode)?.Quantity ?? 0;
        }


        /// <summary>
        /// Generates a unique order ID based on store ID and timestamp.
        /// Format: {storeId}{yyMMddHHmmss}
        /// </summary>
        /// <param name="storeId">Store identifier</param>
        /// <returns>Generated order ID</returns>
        private string GenerateOrderId(string storeId)
        {
            var uniqueOrderId = CustomTimeProvider.Now.ToString("yyMMddHHmmss");
            return $"{storeId}{uniqueOrderId}";
        }


        /// <summary>
        /// Confirms an existing order with Morrison's system.
        /// GET: /NextOrder/ConfirmOrder
        /// </summary>
        /// <returns>Confirmation status of the order</returns>

        /// <summary>
        /// Handles Electronic Delivery Note (EDN) functionality.
        /// GET: /NextOrder/EDN
        /// </summary>

        private static readonly HttpClient client = new HttpClient();


    }


       /// <summary>
    /// Represents the response structure from Morrison's API
    /// </summary>
    public class OrderResponse
    {
        [JsonPropertyName("orders")]
        public List<Order> Orders { get; set; }
    }

    /// <summary>
    /// Represents an individual order in the Morrison's system
    /// </summary>
    public class Order
    {
        [JsonPropertyName("orderReferenceCode")]
        public string OrderReferenceCode { get; set; }

        [JsonPropertyName("toEmailAddress")]
        public string ToEmailAddress { get; set; }

        [JsonPropertyName("orderId")]
        public string OrderId { get; set; }

        [JsonPropertyName("orderRaisedDate")]
        public string OrderRaisedDate { get; set; }

        [JsonPropertyName("shipToDeliverAt")]
        public string ShipToDeliverAt { get; set; }

        [JsonPropertyName("customerOrderId")]
        public string CustomerOrderId { get; set; }

        [JsonPropertyName("messageType")]
        public string MessageType { get; set; }

        [JsonPropertyName("shipFromLocationId")]
        public string ShipFromLocationId { get; set; }

        [JsonPropertyName("items")]
        public List<Item> Items { get; set; }

        [JsonPropertyName("shipToLocationId")]
        public string ShipToLocationId { get; set; }

        [JsonPropertyName("customerId")]
        public string CustomerId { get; set; }

        [JsonPropertyName("isIndependents")]
        public bool IsIndependents { get; set; }

        [JsonPropertyName("isConfirmationNotificationRequired")]
        public bool IsConfirmationNotificationRequired { get; set; }

        [JsonPropertyName("storeName")]
        public string StoreName { get; set; }

        [JsonPropertyName("deliveryNetwork")]
        public string DeliveryNetwork { get; set; }

        [JsonPropertyName("storeIdPrefix")]
        public string StoreIdPrefix { get; set; }

        [JsonPropertyName("shipToAddressLine1")]
        public string ShipToAddressLine1 { get; set; }

        [JsonPropertyName("shipToAddressCity")]
        public string ShipToAddressCity { get; set; }

        [JsonPropertyName("shipToAddressLine2")]
        public string ShipToAddressLine2 { get; set; }

        [JsonPropertyName("shipToAddressPostCode")]
        public string ShipToAddressPostCode { get; set; }

        [JsonPropertyName("shipToAddressCountryCode")]
        public string ShipToAddressCountryCode { get; set; }

        [JsonPropertyName("parentCustomerName")]
        public string ParentCustomerName { get; set; }
    }

    /// <summary>
    /// Represents an individual item in a Morrison's order
    /// </summary>
    public class Item
    {
        [JsonPropertyName("statusValidation")]
        public string StatusValidation { get; set; }

        [JsonPropertyName("itemLineId")]
        public int ItemLineId { get; set; }

        [JsonPropertyName("itemId")]
        public string ItemId { get; set; }

        [JsonPropertyName("orderId")]
        public string OrderId { get; set; }

        [JsonPropertyName("shipToLocationId")]
        public string ShipToLocationId { get; set; }

        [JsonPropertyName("quantityOrdered")]
        public int QuantityOrdered { get; set; }

        [JsonPropertyName("orderReferenceCode")]
        public string OrderReferenceCode { get; set; }

        [JsonPropertyName("customerId")]
        public string CustomerId { get; set; }

        [JsonPropertyName("shipToDeliverAt")]
        public string ShipToDeliverAt { get; set; }

        [JsonPropertyName("itemAlternateId")]
        public ItemAlternateId ItemAlternateId { get; set; }

        [JsonPropertyName("shipFromLocationId")]
        public string ShipFromLocationId { get; set; }

        [JsonPropertyName("quantityConfirmed")]
        public int QuantityConfirmed { get; set; }

        [JsonPropertyName("itemCaseSize")]
        public string ItemCaseSize { get; set; }

        [JsonPropertyName("priceOrderedAmount")]
        public double PriceOrderedAmount { get; set; }

        [JsonPropertyName("quantityType")]
        public string QuantityType { get; set; }

        [JsonPropertyName("isSubstitutePinRequired")]
        public bool IsSubstitutePinRequired { get; set; }

        [JsonPropertyName("isSubstitutePin")]
        public bool IsSubstitutePin { get; set; }

        [JsonPropertyName("priceConfirmedEachesAmount")]
        public double PriceConfirmedEachesAmount { get; set; }

        [JsonPropertyName("priceConfirmedTaxRate")]
        public double PriceConfirmedTaxRate { get; set; }

        [JsonPropertyName("priceConfirmedAmount")]
        public double PriceConfirmedAmount { get; set; }
    }

    /// <summary>
    /// Represents alternative identifiers for an item
    /// </summary>
    public class ItemAlternateId
    {
        [JsonPropertyName("skuMin")]
        public string SkuMin { get; set; }

        [JsonPropertyName("clientId")]
        public string ClientId { get; set; }

        [JsonPropertyName("barcodeEan")]
        public string BarcodeEan { get; set; }

        [JsonPropertyName("skuPin")]
        public string SkuPin { get; set; }
    }

}
