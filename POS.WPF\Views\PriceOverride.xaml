﻿<Window x:Class="POS.WPF.Views.PriceOverride"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
        xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
        xmlns:local="clr-namespace:POS.WPF.Views"
        mc:Ignorable="d"
        Title="Price Override" Height="450" Width="800"
        WindowStartupLocation="CenterScreen">

    <Grid Background="#004e7c">
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
            
        </Grid.RowDefinitions>

        <!-- Input Section -->
        <Border Grid.Row="0" Background="#0a688d" Margin="10" CornerRadius="5">
            <Grid>
                <Grid.RowDefinitions>
                    <RowDefinition Height="Auto"/>
                    <RowDefinition Height="Auto"/>
                    <RowDefinition Height="Auto"/>

                </Grid.RowDefinitions>

                <TextBox x:Name="txtOverridePrice"
                         Margin="10"
                         FontSize="24"
                         VerticalContentAlignment="Center"
                         HorizontalContentAlignment="Center"
                         Foreground="White"
                         BorderThickness="0"
                         Text=""
                         Background="LightBlue"/>

                <Button Grid.Row="1"
                        Content="SAVE"
                        Margin="10"
                        FontSize="24"
                        Background="{x:Null}"
                        Foreground="White"
                        BorderBrush="#33FFFFFF"
                        BorderThickness="1"
                        Click="Button_Click"/>

                <Button Grid.Row="2"
        Content="CANCEL"
        Margin="10"
        FontSize="24"
        Background="{x:Null}"
        Foreground="White"
        BorderBrush="#33FFFFFF"
        BorderThickness="1"
        Click="Button_Click_1"/>
            </Grid>
        </Border>

        <!-- Numeric Keypad -->
        <local:NumericKeypad Grid.Row="1"
                             Margin="10"
                             Background="#0a688d"
                             KeyPressed="NumericKeypad_KeyPressed"/>
    </Grid>
</Window>