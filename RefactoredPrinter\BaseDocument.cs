﻿using RefactoredPrinter.Interfaces;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace RefactoredPrinter
{
    public abstract class BaseDocument : IPrintDocument
    {
        protected abstract void BuildHeader();
        protected abstract void BuildBody();
        protected abstract void BuildFooter();

        public List<byte> GetPrintCommands(IPrinterEmitter emitter)
        {
            var commands = new List<byte>();
            BuildHeader();
            BuildBody();
            BuildFooter();
            return commands;
        }
    }
}
