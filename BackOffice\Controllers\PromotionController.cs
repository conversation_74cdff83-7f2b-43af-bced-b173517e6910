﻿using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Mvc.Rendering;
using Microsoft.EntityFrameworkCore;
using POS.Core.Models;

namespace BackOffice.Controllers
{
    public class PromotionController : Controller
    {
        private readonly Data _context;

        public PromotionController(Data context)
        {
            _context = context;
        }

        private IActionResult RedirectToUserHome()
        {
            if (User.IsInRole("BackOffice"))
            {
                return RedirectToAction("Index", "BackOffice");
            }
            return RedirectToAction("Index", "Home");
        }

        public async Task<IActionResult> Index(int page = 1, int pageSize = 10)
        {
            var totalCount = await _context.Promotions.CountAsync();

            var promotions = await _context.Promotions
                .Include(p => p.Products)
                .OrderByDescending(p => p.StartTime)
                .Skip((page - 1) * pageSize)
                .Take(pageSize)
                .ToListAsync();

            ViewBag.CurrentPage = page;
            ViewBag.TotalPages = (int)Math.Ceiling((double)totalCount / pageSize);

            return View(promotions);
        }

        public async Task<IActionResult> Details(int id)
        {
            var promotion = await _context.Promotions
                .Include(p => p.Products)
                .FirstOrDefaultAsync(p => p.Id == id);

            if (promotion == null)
                return NotFound();

            return View(promotion);
        }

        public IActionResult Create()
        {
            ViewBag.Products = new MultiSelectList(_context.Products.OrderBy(p => p.Name), "Id", "Name");

            return View();
        }

        [HttpPost]
        public async Task<IActionResult> Create(Promotion promotion, int[] selectedProducts)
        {
                promotion.Products = await _context.Products
                    .Where(p => selectedProducts.Contains(p.Id))
                    .ToListAsync();

                promotion.Identifire = "";

                _context.Promotions.Add(promotion);
                await _context.SaveChangesAsync();
                return RedirectToAction(nameof(Index));

        }

        public async Task<IActionResult> Edit(int id)
        {
            var promotion = await _context.Promotions
                .Include(p => p.Products)
                .FirstOrDefaultAsync(p => p.Id == id);

            if (promotion == null)
                return NotFound();

            var selectedProducts = promotion.Products
    .Select(p => new { id = p.Id, text = p.Name })
    .ToList();

            ViewBag.SelectedProductList = selectedProducts;

            ViewBag.Products = new MultiSelectList(_context.Products, "Id", "Name", promotion.Products.Select(p => p.Id));
            return View(promotion);
        }

        [HttpPost]
        public async Task<IActionResult> Edit(int id, Promotion promotion, string selectedProducts)
        {
            if (id != promotion.Id)
                return NotFound();

            // Debug: Log the received selectedProducts value
            System.Diagnostics.Debug.WriteLine($"Received selectedProducts: {selectedProducts}");

            if (ModelState.IsValid)
            {
                try
                {
                    var existing = await _context.Promotions
                        .Include(p => p.Products)
                        .FirstOrDefaultAsync(p => p.Id == id);

                    if (existing == null)
                        return NotFound();

                    existing.Name = promotion.Name;
                    existing.Type = promotion.Type;
                    existing.Identifire = promotion.Identifire;
                    existing.StartTime = promotion.StartTime;
                    existing.EndTime = promotion.EndTime;
                    existing.PromotionPrice = promotion.PromotionPrice;
                    existing.Quantity = promotion.Quantity;

                    // Clear existing products
                    existing.Products.Clear();
                    await _context.SaveChangesAsync(); // Save changes to ensure products are removed
                    
                    // Parse the comma-separated product IDs
                    if (!string.IsNullOrEmpty(selectedProducts))
                    {
                        var productIds = selectedProducts.Split(',').Select(int.Parse).ToArray();
                        System.Diagnostics.Debug.WriteLine($"Product IDs to add: {string.Join(", ", productIds)}");
                        
                        var productsToAdd = await _context.Products
                            .Where(p => productIds.Contains(p.Id))
                            .ToListAsync();
                        
                        foreach (var product in productsToAdd)
                        {
                            existing.Products.Add(product);
                        }
                    }

                    await _context.SaveChangesAsync();
                    return RedirectToUserHome();
                }
                catch (Exception ex)
                {
                    System.Diagnostics.Debug.WriteLine($"Error in Edit: {ex.Message}");
                    ModelState.AddModelError("", "An error occurred while saving the promotion.");
                }
            }

            // If we get here, repopulate the view data
            var selectedProductsList = await _context.Promotions
                .Include(p => p.Products)
                .Where(p => p.Id == id)
                .SelectMany(p => p.Products)
                .Select(p => new { id = p.Id, text = p.Name })
                .ToListAsync();

            ViewBag.SelectedProductList = selectedProductsList;
            ViewBag.Products = new MultiSelectList(_context.Products, "Id", "Name", selectedProducts?.Split(',').Select(int.Parse));
            return View(promotion);
        }

        public async Task<IActionResult> Delete(int id)
        {
            var promotion = await _context.Promotions
                .Include(p => p.Products)
                .FirstOrDefaultAsync(p => p.Id == id);

            if (promotion == null)
                return NotFound();

            return View(promotion);
        }

        [HttpPost, ActionName("Delete")]
        public async Task<IActionResult> DeleteConfirmed(int id)
        {
            var promotion = await _context.Promotions.FindAsync(id);
            _context.Promotions.Remove(promotion);
            await _context.SaveChangesAsync();
            return RedirectToAction(nameof(Index));
        }

        [HttpGet]
        public async Task<IActionResult> Search(string term)
        {
            var products = await _context.Products
                .Where(p => p.Name.Contains(term))
                .OrderBy(p => p.Name)
                .Take(20)
                .Select(p => new {
                    id = p.Id,
                    text = p.Name
                })
                .ToListAsync();

            return Json(products);
        }

        [HttpGet]
        public JsonResult SearchProducts(string term)
        {
            // Query your database for products matching the search term
            var products = _context.Products
                .Where(p => p.Name.Contains(term) || p.PLU.Contains(term))
                .Select(p => new { id = p.Id, text = p.PLU + " - " + p.Name })
                .Take(20) // Limit results for performance
                .ToList();
            
            return Json(products);
        }

    }
}
