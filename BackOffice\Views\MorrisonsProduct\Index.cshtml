﻿

@model dynamic

<div class="container">
    <h2>Morrisons Product Sync</h2>
    
    <div id="syncProgress" style="display: none;">
        <div class="progress mb-3">
            <div class="progress-bar" role="progressbar" style="width: 0%"></div>
        </div>
        <p id="statusMessage" class="text-info"></p>
    </div>

    <div id="syncResults" style="display: none;">
        <div class="alert alert-success">
            <h4>Sync Complete</h4>
            <p>New Products Added: <span id="newProductCount">0</span></p>
            <p>Errors Encountered: <span id="errorCount">0</span></p>
            <p>Total Processed: <span id="totalProcessed">0</span></p>
        </div>
    </div>

    <div id="syncError" class="alert alert-danger" style="display: none;">
    </div>

    <button id="startSync" class="btn btn-primary" onclick="startSync()">
        Start Sync
    </button>
</div>

@section Scripts {
    <script src="~/lib/microsoft/signalr/signalr.min.js"></script>
    <script>
        const connection = new signalR.HubConnectionBuilder()
            .withUrl("/syncProgressHub")
            .build();

        connection.on("ReceiveProgress", (message, processed, total) => {
            const percentage = Math.round((processed / total) * 100);
            $("#syncProgress").show();
            $("#syncProgress .progress-bar").css("width", percentage + "%");
            $("#statusMessage").text(message);
        });

        connection.on("ReceiveError", (errorMessage) => {
            console.log("Processing error:", errorMessage);
        });

        connection.on("SyncComplete", (result) => {
            $("#syncProgress").hide();
            $("#syncResults").show();
            $("#newProductCount").text(result.newProducts);
            $("#errorCount").text(result.errorCount);
            $("#totalProcessed").text(result.totalProcessed);
        });

        connection.on("SyncError", (errorMessage) => {
            $("#syncProgress").hide();
            $("#syncError").show().text(errorMessage);
        });

        connection.start().catch(console.error);

        function startSync() {
            $("#startSync").prop("disabled", true);
            $("#syncResults").hide();
            $("#syncError").hide();
            
            fetch('@Url.Action("FetchFullCatalog")', { method: 'POST' })
                .catch(error => {
                    $("#syncError").show().text("Failed to start sync: " + error);
                    $("#startSync").prop("disabled", false);
                });
        }
    </script>
}
