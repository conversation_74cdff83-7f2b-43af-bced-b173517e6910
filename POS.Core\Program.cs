﻿using POS.Core.Models;
using System.ComponentModel;
using TimeManagement;


//using (var context = new Data())
//{
//    SeedData(context);
//}

//int x = 0;



static void SeedData(Data context)
{
   // context.Database.EnsureDeleted();
    context.Database.EnsureCreated();

    SeedBaselineData(context);
    
    // Verify baseline data exists before proceeding
    if (!context.Brands.Any())
    {
        throw new InvalidOperationException("Baseline data must be seeded before test data");
    }
    
    SeedTestData(context);
}

static void SeedBaselineData(Data context)
{
    #region BaseLine Seed Data
    Brand brand1 = new Brand { Name = "Morrison" };
    Brand brand2 = new Brand { Name = "Budgens" };

    Vat vatStandard = new Vat { Code = "CODE0", Value = 20, Rate = "20%" };
    Vat vatReduced = new Vat { Code = "CODE1", Value = 5, Rate = "5%" };
    Vat vatZero = new Vat { Code = "CODE2", Value = 0, Rate = "0%" };
    Vat vatExempt = new Vat { Code = "EXEMPT", Value = 0, Rate = "0%" };

    SubCatagory SubCatagoryHotFoods = new SubCatagory { Name = "HotFoods", Catagory = Catagory.ShopSales, StockType = StockType.Stock };
    SubCatagory SubCatagoryNewsPapers = new SubCatagory { Name = "NewsPapers", Catagory = Catagory.ShopSales, StockType = StockType.Stock };
    SubCatagory SubCatagoryPayPoint = new SubCatagory { Name = "PayPoint", Catagory = Catagory.Excempt, StockType = StockType.Stock };
    SubCatagory ThirdParty = new SubCatagory { Name = "3rdParty", Catagory = Catagory.Excempt, StockType = StockType.Stock };

    Division DivisionHotFoods = new Division { Name = "HotFoods", SubCatagory = SubCatagoryHotFoods };
    Division DivisionNewsPapers = new Division { Name = "NewsPapers", SubCatagory = SubCatagoryNewsPapers };
    Division DivisionPayPoint = new Division { Name = "PayPoint", SubCatagory = SubCatagoryPayPoint };
    Division DivisionThirdParty = new Division { Name = "3rdParty", SubCatagory = ThirdParty };

    Product ProductPayPoint = new Product()
    {
        Barcode = "2342342342342353532432424235345",
        Brand = null,
        Description = "",
        Division = DivisionPayPoint,
        Name = "PayPoint",
        PLU = "",
        PurchasePackSize = 0,
        PurchasePrice = 0,
        SellingPrice = 0,
        SpecialProduct = true,
        Vat = vatExempt
    };

    Product ProductNewsPapers = new Product()
    {
        Barcode = "31324546574645345346564643545",
        Brand = null,
        Description = "",
        Division = DivisionNewsPapers,
        Name = "NewsPapers",
        PLU = "",
        PurchasePackSize = 0,
        PurchasePrice = 0,
        SellingPrice = 0,
        SpecialProduct = true,
        Vat = vatZero
    };

    Product ProductHotFoods = new Product()
    {
        Barcode = "898757463453453456576574563445",
        Brand = null,
        Description = "",
        Division = DivisionHotFoods,
        Name = "HotFoods",
        PLU = "",
        PurchasePackSize = 0,
        PurchasePrice = 0,
        SellingPrice = 0,
        SpecialProduct = true,
        Vat = vatStandard
    };

    Product ThirdPartyProduct = new Product()
    {
        Barcode = "3423454563536787667567678",
        Brand = null,
        Description = "",
        Division = DivisionThirdParty,
        Name = "ThirdParty",
        PLU = "",
        PurchasePackSize = 0,
        PurchasePrice = 0,
        SellingPrice = 0,
        SpecialProduct = true,
        Vat = vatStandard
    };

    var paymentMethods = new List<PaymentMethod>
    {
        new PaymentMethod { Method = "Card" },
        new PaymentMethod { Method = "Cash"  },
        new PaymentMethod { Method = "Voucher"  },
        new PaymentMethod { Method = "Deliveroo"  },
        new PaymentMethod { Method = "Uber"  },
        new PaymentMethod { Method = "PP Card"  }
    };

    var paidOutOptions = new List<PaidOutOption>
    {
        new PaidOutOption { Option = "SCRATCH CARD"  },
        new PaidOutOption { Option = "NATIONAL LOTTERY"  },
        new PaidOutOption { Option = "PP CREDIT"  },
        new PaidOutOption { Option = "OTHERS"  }
    };

    context.PaymentMethods.AddRange(paymentMethods);
    context.PaidOutOptions.AddRange(paidOutOptions);
    context.Brands.AddRange(brand1, brand2);
    context.Products.AddRange(ProductHotFoods, ProductNewsPapers, ProductPayPoint, ThirdPartyProduct);
    context.Vats.AddRange(vatStandard, vatReduced, vatZero);
    context.SaveChanges();
    Console.WriteLine("BaseLine Seed Data Seeded Successfully!");
    #endregion
}

static void SeedTestData(Data context)
{
    #region Test Data
    var brand1 = context.Brands.First(b => b.Name == "Morrison");
    var brand2 = context.Brands.First(b => b.Name == "Budgens");

    var store1 = new Store { StoreName = "Eastwood", Brand = brand1, VatNo = "123 456 678" };
    var store2 = new Store { StoreName = "ST Marrys", Brand = brand1, VatNo = "987 654 321" };
    var store3 = new Store { StoreName = "Test Store", Brand = brand1, VatNo = "187 654 321" };

    var cashier1 = new Cashier { Name = "John", Password = "John", Role = "Cashier" };
    var cashier2 = new Cashier { Name = "Jane", Password = "Jane", Role = "Cashier" };
    var accountant = new Accountant { Name = "Nick", Password = "Nick", Role = "Accountant" };
    var admin = new Admin { Name = "Bob", Password = "Bob", Role = "Admin" };

    PosMachine posMachine = new PosMachine { Store = store1 };
    PosMachine posMachine1 = new PosMachine { Store = store2 };
    PosMachine posMachine3 = new PosMachine { Store = store3 };
    PosMachine posMachine4 = new PosMachine { Store = store3 };

    cashier1.CashierStores.Add(new CashierStore { Store = store1, IsDeleted = false });
    cashier1.CashierStores.Add(new CashierStore { Store = store2, IsDeleted = false });
    cashier1.CashierStores.Add(new CashierStore { Store = store3, IsDeleted = false });

    cashier2.CashierStores.Add(new CashierStore { Store = store1, IsDeleted = false });
    cashier2.CashierStores.Add(new CashierStore { Store = store2, IsDeleted = false }); 
    cashier2.CashierStores.Add(new CashierStore { Store = store3, IsDeleted = false });

    context.Stores.AddRange(store1, store2);
    context.PosMachines.AddRange(posMachine, posMachine1);
    context.Cashiers.Add(cashier1);
    context.Cashiers.Add(cashier2);
    context.Accountants.Add(accountant);
    context.Admins.Add(admin);
    context.SaveChanges();

    cashier1.UserId = cashier1.Id.ToString("D4");
    cashier1.Password = cashier1.UserId;
    cashier2.UserId = cashier2.Id.ToString("D4");
    cashier2.Password = cashier2.UserId;
    context.SaveChanges();

    Console.WriteLine("Test Data Seeded Successfully!");
    #endregion
}

