﻿using RefactoredPrinter.Interfaces;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace RefactoredPrinter
{
    public class DocumentFactory
    {
        public static IPrintDocument CreateReceipt(PrintData data) => new ReceiptDocument(data);
       // public static IPrintDocument CreateSafeDrop(...) => new SafeDropDocument(...);
    }
}
