﻿@model POS.Core.Models.PosMachine

@{
    ViewData["Title"] = "Edit POS Machine";
}

<h2>Edit POS Machine</h2>

<form asp-action="Edit">
    <input type="hidden" asp-for="Id" />

 

    <div class="form-group">
        <label>Store</label>
        <select asp-for="Store.StoreId" class="form-control">
            @foreach (var store in ViewBag.Stores)
            {
                <option value="@store.StoreId">@store.StoreName</option>
            }
        </select>
    </div>

    <button type="submit" class="btn btn-success">Save</button>
</form>
