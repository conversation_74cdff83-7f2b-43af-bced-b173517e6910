﻿@model POS.Core.Models.Promotion
@{
    ViewData["Title"] = "Delete Promotion";
}

<h2>Delete Promotion</h2>

<div class="alert alert-danger">
    Are you sure you want to delete the promotion <strong>@Model.Name</strong>?
</div>

<dl class="row">
    <dt class="col-sm-2">Identifier</dt>
    <dd class="col-sm-10">@Model.Identifire</dd>

    <dt class="col-sm-2">Type</dt>
    <dd class="col-sm-10">@Model.Type</dd>

    <dt class="col-sm-2">Price</dt>
    <dd class="col-sm-10">@Model.PromotionPrice?.ToString("C")</dd>

    <dt class="col-sm-2">Quantity</dt>
    <dd class="col-sm-10">@Model.Quantity</dd>

    <dt class="col-sm-2">Start Time</dt>
    <dd class="col-sm-10">@Model.StartTime</dd>

    <dt class="col-sm-2">End Time</dt>
    <dd class="col-sm-10">@Model.EndTime</dd>

    <dt class="col-sm-2">Products</dt>
    <dd class="col-sm-10">@string.Join(", ", Model.Products.Select(p => p.Name))</dd>
</dl>

<form asp-action="DeleteConfirmed" method="post">
    <input type="hidden" asp-for="Id" />
    <button type="submit" class="btn btn-danger">Delete</button>
    <a asp-action="Index" class="btn btn-secondary">Cancel</a>
</form>
