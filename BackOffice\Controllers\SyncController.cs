using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using POS.Core.Models;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Linq.Expressions;
using System.Reflection;
using System.Threading.Tasks;

namespace BackOffice.Controllers
{
    [Route("api/[controller]")]
    [ApiController]
    public class SyncController : ControllerBase
    {
        private readonly Data _centralDb;

        public SyncController(Data centralDb)
        {
            _centralDb = centralDb;
        }

        // Helper method to get the correct DbSet property name
        private string GetDbSetPropertyName(string entityType)
        {
            // Special cases
            if (entityType == "SalePaymentMethod2")
                return "SalePaymentMethods";
            if (entityType == "Void" || entityType == "POS.Core.Models.Void")
                return "Voids";
            
            // Default case: append "s" to the entity type name
            return entityType + "s";
        }

        [HttpGet("entities/{entityName}")]
        public async Task<IActionResult> GetUpdatedEntities(
            string entityName, 
            [FromQuery] DateTime lastSync,
            [FromQuery] int pageSize = 100,
            [FromQuery] int page = 1)
        {
            try
            {
                // Handle special case for Void entity
                string entityTypeName = entityName;
                if (entityName == "Void" || entityName == "Voids")
                {
                    entityTypeName = "Voids";
                }

                // Get the DbSet dynamically based on entityName
                var dbSetPropertyName = GetDbSetPropertyName(entityTypeName);
                var dbSetProperty = _centralDb.GetType().GetProperty(dbSetPropertyName);
                
                if (dbSetProperty == null)
                    return NotFound($"Entity type '{entityName}' not found");

                var dbSet = dbSetProperty.GetValue(_centralDb);
                var entityType = dbSet.GetType().GetGenericArguments()[0];

                // Check if the entity has LastModified property
                var lastModifiedProp = entityType.GetProperty("LastModified");
                var hasLastModified = lastModifiedProp != null;
                var isNullableDateTime = hasLastModified && 
                    Nullable.GetUnderlyingType(lastModifiedProp.PropertyType) == typeof(DateTime);

                // Get updated entities
                var query = dbSet as IQueryable;
                
                if (hasLastModified)
                {
                    // Use dynamic LINQ for filtering by LastModified
                    var parameter = Expression.Parameter(entityType, "e");
                    var property = Expression.Property(parameter, "LastModified");
                    var constant = Expression.Constant(lastSync, typeof(DateTime));
                    
                    Expression comparison;
                    if (isNullableDateTime)
                    {
                        // For nullable DateTime, we need to handle it differently
                        // First check if property has value, then compare
                        var hasValueProperty = Expression.Property(property, "HasValue");
                        var valueProperty = Expression.Property(property, "Value");
                        var valueComparison = Expression.GreaterThan(valueProperty, constant);
                        comparison = Expression.AndAlso(hasValueProperty, valueComparison);
                    }
                    else
                    {
                        // For non-nullable DateTime
                        comparison = Expression.GreaterThan(property, constant);
                    }
                    
                    var lambda = Expression.Lambda(comparison, parameter);

                    var whereMethod = typeof(Queryable).GetMethods()
                        .First(m => m.Name == "Where" && m.GetParameters().Length == 2)
                        .MakeGenericMethod(entityType);

                    query = whereMethod.Invoke(null, new object[] { query, lambda }) as IQueryable;
                }

                // Get total count for pagination metadata
                var countMethod = typeof(Queryable).GetMethods()
                    .First(m => m.Name == "Count" && m.GetParameters().Length == 1)
                    .MakeGenericMethod(entityType);
                var totalCount = (int)countMethod.Invoke(null, new object[] { query });

                // Apply pagination
                var skipMethod = typeof(Queryable).GetMethods()
                    .First(m => m.Name == "Skip")
                    .MakeGenericMethod(entityType);
                var takeMethod = typeof(Queryable).GetMethods()
                    .First(m => m.Name == "Take")
                    .MakeGenericMethod(entityType);

                var skip = (page - 1) * pageSize;
                query = skipMethod.Invoke(null, new object[] { query, skip }) as IQueryable;
                query = takeMethod.Invoke(null, new object[] { query, pageSize }) as IQueryable;

                // Use reflection to call ToList() method
                var toListMethod = typeof(Enumerable).GetMethod("ToList")
                    .MakeGenericMethod(entityType);
                
                var result = toListMethod.Invoke(null, new object[] { query });
                
                return Ok(new {
                    Data = result,
                    TotalCount = totalCount,
                    Page = page,
                    PageSize = pageSize,
                    TotalPages = (int)Math.Ceiling(totalCount / (double)pageSize)
                });
            }
            catch (Exception ex)
            {
                return StatusCode(500, $"Error retrieving entities: {ex.Message}");
            }
        }

        [HttpPost("entities/{entityType}")]
        public async Task<IActionResult> CreateEntity(string entityType, [FromBody] object entityData)
        {
            try
            {
                // Log the received data
                Console.WriteLine($"Received create request for entity type: {entityType}");
                var jsonString = System.Text.Json.JsonSerializer.Serialize(entityData);
                Console.WriteLine($"Received JSON: {jsonString}");
                
                // Handle special case for Void entity
                string actualEntityType = entityType;
                if (entityType == "Void" || entityType == "Voids")
                {
                    actualEntityType = "Void";
                }

                // Get the entity type from the assembly
                var type = AppDomain.CurrentDomain.GetAssemblies()
                    .SelectMany(a => a.GetTypes())
                    .FirstOrDefault(t => t.Name == actualEntityType && t.Namespace == "POS.Core.Models");

                if (type == null)
                {
                    Console.WriteLine($"Entity type '{entityType}' not found");
                    return NotFound($"Entity type '{entityType}' not found");
                }

                // Convert the dynamic object to the correct type with proper options
                var options = new System.Text.Json.JsonSerializerOptions
                {
                    PropertyNameCaseInsensitive = true,
                    AllowTrailingCommas = true,
                    ReadCommentHandling = System.Text.Json.JsonCommentHandling.Skip,
                    NumberHandling = System.Text.Json.Serialization.JsonNumberHandling.AllowReadingFromString
                };
                
                var entity = System.Text.Json.JsonSerializer.Deserialize(jsonString, type, options);

                // Debug: Print property values
                foreach (var prop in type.GetProperties())
                {
                    var value = prop.GetValue(entity);
                    Console.WriteLine($"Property {prop.Name} = {value}");
                }

                // Add to the central database
                var dbSetPropertyName = GetDbSetPropertyName(entityType);
                var dbSetProperty = _centralDb.GetType().GetProperty(dbSetPropertyName);
                
                if (dbSetProperty == null)
                {
                    Console.WriteLine($"DbSet for entity type '{entityType}' not found");
                    return NotFound($"DbSet for entity type '{entityType}' not found");
                }

                var dbSet = dbSetProperty.GetValue(_centralDb);
                var addMethod = dbSet.GetType().GetMethod("Add");
                addMethod.Invoke(dbSet, new[] { entity });

                await _centralDb.SaveChangesAsync();

                // Get the ID of the newly created entity
                var idProperty = type.GetProperty("Id") ?? type.GetProperty($"{type.Name}Id");
                var remoteId = idProperty.GetValue(entity);

                Console.WriteLine($"Successfully created entity with ID: {remoteId}");
                return Ok(new { Id = remoteId });
            }
            catch (Exception ex)
            {
                // Log the full exception details
                Console.WriteLine($"Error creating entity: {ex.Message}");
                Console.WriteLine($"Stack trace: {ex.StackTrace}");
                if (ex.InnerException != null)
                {
                    Console.WriteLine($"Inner exception: {ex.InnerException.Message}");
                    Console.WriteLine($"Inner stack trace: {ex.InnerException.StackTrace}");
                }
                
                return StatusCode(500, $"Error creating entity: {ex.Message}");
            }
        }

        [HttpPut("entities/{entityType}/{id}")]
        public async Task<IActionResult> UpdateEntity(string entityType, int id, [FromBody] object entityData)
        {
            try
            {
                // Handle special case for Void entity
                string actualEntityType = entityType;
                if (entityType == "Void" || entityType == "Voids")
                {
                    actualEntityType = "Void";
                }

                // Get the entity type from the assembly
                var type = AppDomain.CurrentDomain.GetAssemblies()
                    .SelectMany(a => a.GetTypes())
                    .FirstOrDefault(t => t.Name == actualEntityType && t.Namespace == "POS.Core.Models");

                if (type == null)
                    return NotFound($"Entity type '{entityType}' not found");

                // Get the DbSet
                var dbSetPropertyName = GetDbSetPropertyName(entityType);
                var dbSetProperty = _centralDb.GetType().GetProperty(dbSetPropertyName);
                
                if (dbSetProperty == null)
                    return NotFound($"DbSet for entity type '{entityType}' not found");

                var dbSet = dbSetProperty.GetValue(_centralDb);

                // Find the entity
                var findMethod = dbSet.GetType().GetMethod("Find", new[] { typeof(object[]) });
                var entity = findMethod.Invoke(dbSet, new object[] { new object[] { id } });

                if (entity == null)
                    return NotFound($"Entity of type '{entityType}' with ID {id} not found");

                // Convert the JSON data with proper options
                var jsonString = System.Text.Json.JsonSerializer.Serialize(entityData);
                Console.WriteLine($"Received JSON for update: {jsonString}"); // Debug logging
                
                var options = new System.Text.Json.JsonSerializerOptions
                {
                    PropertyNameCaseInsensitive = true,
                    AllowTrailingCommas = true,
                    ReadCommentHandling = System.Text.Json.JsonCommentHandling.Skip,
                    NumberHandling = System.Text.Json.Serialization.JsonNumberHandling.AllowReadingFromString
                };
                
                // Deserialize to a temporary object
                var tempEntity = System.Text.Json.JsonSerializer.Deserialize(jsonString, type, options);
                
                // Update the entity properties
                var properties = type.GetProperties().Where(p => p.CanWrite && p.Name != "Id" && p.Name != $"{type.Name}Id");
                
                foreach (var prop in properties)
                {
                    var value = prop.GetValue(tempEntity);
                    prop.SetValue(entity, value);
                    Console.WriteLine($"Updated property {prop.Name} = {value}");
                }

                await _centralDb.SaveChangesAsync();
                return Ok();
            }
            catch (Exception ex)
            {
                // Log the full exception details
                Console.WriteLine($"Error updating entity: {ex.Message}");
                Console.WriteLine($"Stack trace: {ex.StackTrace}");
                if (ex.InnerException != null)
                {
                    Console.WriteLine($"Inner exception: {ex.InnerException.Message}");
                    Console.WriteLine($"Inner stack trace: {ex.InnerException.StackTrace}");
                }
                
                return StatusCode(500, $"Error updating entity: {ex.Message}");
            }
        }

        [HttpDelete("entities/{entityType}/{id}")]
        public async Task<IActionResult> DeleteEntity(string entityType, int id)
        {
            try
            {
                // Handle special case for Void entity
                string actualEntityType = entityType;
                if (entityType == "Void" || entityType == "Voids")
                {
                    actualEntityType = "Void";
                }

                // Get the entity type from the assembly
                var type = AppDomain.CurrentDomain.GetAssemblies()
                    .SelectMany(a => a.GetTypes())
                    .FirstOrDefault(t => t.Name == actualEntityType && t.Namespace == "POS.Core.Models");

                if (type == null)
                    return NotFound($"Entity type '{entityType}' not found");

                // Get the DbSet
                var dbSetPropertyName = GetDbSetPropertyName(entityType);
                var dbSetProperty = _centralDb.GetType().GetProperty(dbSetPropertyName);
                
                if (dbSetProperty == null)
                    return NotFound($"DbSet for entity type '{entityType}' not found");

                var dbSet = dbSetProperty.GetValue(_centralDb);

                // Find the entity
                var findMethod = dbSet.GetType().GetMethod("Find", new[] { typeof(object[]) });
                var entity = findMethod.Invoke(dbSet, new object[] { new object[] { id } });

                if (entity == null)
                    return NotFound($"Entity of type '{entityType}' with ID {id} not found");

                // Remove the entity
                var removeMethod = dbSet.GetType().GetMethod("Remove");
                removeMethod.Invoke(dbSet, new[] { entity });

                await _centralDb.SaveChangesAsync();
                return Ok();
            }
            catch (Exception ex)
            {
                return StatusCode(500, $"Error deleting entity: {ex.Message}");
            }
        }
    }
}


