﻿using Microsoft.EntityFrameworkCore;
using POS.Core.Models;
using System.Text;
using TimeManagement;

namespace BackOffice
{
    public class NightEmailBackgroundService : BackgroundService
    {
        private readonly IServiceProvider _serviceProvider;
        private readonly Data dataContext;
        public NightEmailBackgroundService(IServiceProvider serviceProvider, Data dataContext)
        {
            _serviceProvider = serviceProvider;
            this.dataContext = dataContext;
        }

        protected override async Task ExecuteAsync(CancellationToken stoppingToken)
        {
            while (!stoppingToken.IsCancellationRequested)
            {
                var now = CustomTimeProvider.Now;
                var scheduledTime = now.Date.AddHours(17).AddMinutes(31); // Example: 9:00 AM

                if (now > scheduledTime)
                {
                    scheduledTime = scheduledTime.AddDays(1); // Move to next day
                }

                var delay = scheduledTime - now;
                await Task.Delay(delay, stoppingToken);

                if (!stoppingToken.IsCancellationRequested)
                {
                    await SendEmailAsync();
                }
            }
        }

        private async Task SendEmailAsync()
        {
            using (var scope = _serviceProvider.CreateScope())
            {
                var emailService = scope.ServiceProvider.GetRequiredService<IEmailService>();


                var subscriptions = dataContext.EmailSubscriptions.ToList();

                foreach (var subscription in subscriptions)
                {
                    await emailService.SendEmailAsync(subscription.Email, "Daily Report", "This is the daily email.");

                }

            }
        }

        public string GenerateSalesEmailBody(DateTime startDate, DateTime endDate)
        {
            using (var context = new Data()) // Assuming 'Data' is your DbContext
            {
                var sales = context.Sales
                    .Where(s => s.Date >= startDate && s.Date <= endDate)
                    .Include(s => s.ProductStoreSales)
                    .ThenInclude(pss => pss.ProductStore)
                    .ThenInclude(ps => ps.Product)
                    .ToList();

                if (!sales.Any())
                    return "<p>No sales records found for the selected period.</p>";

                var groupedSales = sales
                    .SelectMany(s => s.ProductStoreSales) // Flatten ProductStoreSales
                    .GroupBy(pss => pss.ProductStore.Product.Division.SubCatagory) // Group by SubCategory
                    .Select(g => new
                    {
                        SubCategory = g.Key,
                        TotalQuantity = g.Sum(pss => pss.Quantity),
                        TotalSales = g.Sum(pss => pss.Total)
                    })
                    .OrderBy(g => g.SubCategory)
                    .ToList();

                decimal grandTotalSales = groupedSales.Sum(g => g.TotalSales);
                int grandTotalQuantity = groupedSales.Sum(g => g.TotalQuantity);

                StringBuilder sb = new StringBuilder();
                sb.Append("<html><head>");
                sb.Append("<style>");
                sb.Append("table { width: 100%; border-collapse: collapse; font-family: Arial, sans-serif; }");
                sb.Append("th, td { border: 1px solid #ddd; padding: 8px; text-align: left; }");
                sb.Append("th { background-color: #f2f2f2; font-weight: bold; }");
                sb.Append("tr:last-child { font-weight: bold; background-color: #e6e6e6; }");
                sb.Append("</style>");
                sb.Append("</head><body>");
                sb.AppendFormat("<h2>Sales Report ({0} - {1})</h2>", startDate.ToShortDateString(), endDate.ToShortDateString());
                sb.Append("<table>");
                sb.Append("<tr><th>Sub Category</th><th>Quantity</th><th>Sale Amount</th></tr>");

                foreach (var subCategorySales in groupedSales)
                {
                    sb.AppendFormat("<tr><td>{0}</td><td>{1}</td><td>{2:C}</td></tr>",
                        subCategorySales.SubCategory, subCategorySales.TotalQuantity, subCategorySales.TotalSales);
                }

                // Total Row
                sb.AppendFormat("<tr><td>Total</td><td>{0}</td><td>{1:C}</td></tr>", grandTotalQuantity, grandTotalSales);

                sb.Append("</table>");
                sb.Append("</body></html>");

                return sb.ToString();
            }
        }


    }
}
