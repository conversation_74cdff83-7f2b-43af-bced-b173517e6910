﻿using BackOffice.ViewModels;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Mvc.Rendering;
using Microsoft.EntityFrameworkCore;
using POS.Core.Models;
using System.Linq;
using System.Threading.Tasks;
using TimeManagement;

namespace BackOffice.Controllers
{
    /// <summary>
    /// Controller responsible for managing system users including Admins, Accountants, and BackOffice users.
    /// Requires Admin role for all operations.
    /// </summary>
    [Authorize(Roles = "Admin")]
    public class AdminUserController : Controller
    {
        private readonly Data _context;

        /// <summary>
        /// Initializes a new instance of the AdminUserController.
        /// </summary>
        /// <param name="context">The database context for user management operations.</param>
        public AdminUserController(Data context)
        {
            _context = context;
        }

        /// <summary>
        /// Displays a list of all users in the system.
        /// Includes IP address information for Accountant and BackOffice users.
        /// </summary>
        /// <returns>View containing list of all users with their related data.</returns>
        public async Task<IActionResult> Index()
        {
            var users = await _context.Users.Where(u => u.Role != "Cashier")
                .Include(u => (u as Accountant).IpAddresses)
                .Include(u => (u as POS.Core.Models.BackOffice).IpAddresses)
                .ToListAsync();
            return View(users);
        }

        /// <summary>
        /// Displays the user creation form.
        /// Populates store dropdown for BackOffice user creation.
        /// </summary>
        /// <returns>View with UserCreateViewModel and store list in ViewBag.</returns>
        [HttpGet]
        public async Task<IActionResult> Create()
        {
            var model = new UserCreateViewModel();
            ViewBag.Stores = await _context.Stores.Select(s => new SelectListItem
            {
                Value = s.StoreId.ToString(),
                Text = s.StoreName
            }).ToListAsync();
            return View(model);
        }

        /// <summary>
        /// Processes the user creation form submission.
        /// Creates different types of users based on selected role:
        /// - Admin: Basic user with administrative privileges
        /// - Accountant: User with IP address restrictions
        /// - BackOffice: Store-specific user with IP address restrictions
        /// </summary>
        /// <param name="model">The user creation view model containing new user details.</param>
        /// <returns>
        /// - On success: Redirects to Index
        /// - On validation failure: Returns to Create view with error messages
        /// </returns>
        [HttpPost]
        public async Task<IActionResult> Create(UserCreateViewModel model)
        {
            if (!ModelState.IsValid)
                return View(model);

            User user;

            switch (model.Role)
            {
                case "Admin":
                    user = new Admin { Name = model.Name, Password = model.Password };
                    break;
                case "Accountant":
                    user = new Accountant { Name = model.Name, Password = model.Password };
                    break;
                case "BackOffice":
                    if (!model.StoreId.HasValue)
                    {
                        ModelState.AddModelError("StoreId", "Store is required for BackOffice users.");
                        ViewBag.Stores = await _context.Stores.Select(s => new SelectListItem
                        {
                            Value = s.StoreId.ToString(),
                            Text = s.StoreName
                        }).ToListAsync();
                        return View(model);
                    }

                    user = new POS.Core.Models.BackOffice
                    {
                        Name = model.Name,
                        Password = model.Password,
                        StoreId = model.StoreId.Value
                    };
                    break;
                default:
                    ModelState.AddModelError("Role", "Invalid role selected.");
                    return View(model);
            }

            user.Role = model.Role;
            _context.Users.Add(user);
            await _context.SaveChangesAsync();

            // Generate formatted user ID
            user.UserId = user.Id.ToString("D4");
            await _context.SaveChangesAsync();

            // Add IP addresses for Accountant and BackOffice users
            if ((user is Accountant || user is POS.Core.Models.BackOffice) && model.IpAddresses != null)
            {
                foreach (var ip in model.IpAddresses.Where(ip => !string.IsNullOrWhiteSpace(ip)).Distinct())
                {
                    _context.IpAddresses.Add(new IpAddress { Address = ip, UserId = user.Id });
                }
                await _context.SaveChangesAsync();
            }

            return RedirectToAction("Index");
        }



        /// <summary>
        /// Displays the user edit form.
        /// Loads user-specific data based on role including IP addresses.
        /// </summary>
        /// <param name="id">The ID of the user to edit.</param>
        /// <returns>
        /// - View with UserEditViewModel containing user data
        /// - NotFound if user doesn't exist
        /// </returns>
        [HttpGet]
        public async Task<IActionResult> Edit(int id)
        {
            User user = null;

            // Load user with role-specific data
            if (_context.Users.Find(id).Role == "Accountant")
            {
                user = _context.Accountants.Find(id);
                (user as Accountant).IpAddresses = _context.IpAddresses
                    .Where(ip => ip.UserId == id).ToList();
            }
            else if (_context.Users.Find(id).Role == "BackOffice")
            {
                user = _context.BackOffices.Find(id);
                (user as POS.Core.Models.BackOffice).IpAddresses = _context.IpAddresses
                    .Where(ip => ip.UserId == id).ToList();
            }
            else
            {
                user = _context.Users.Find(id);
            }

            if (user == null)
                return NotFound();

            var model = new UserEditViewModel
            {
                Id = user.Id,
                Name = user.Name,
                UserId = user.UserId,
                Role = user is Admin ? "Admin" : user is Accountant ? "Accountant" : "BackOffice",
                IpAddresses = (user as Accountant)?.IpAddresses.Select(ip => ip.Address).ToList()
                            ?? (user as POS.Core.Models.BackOffice)?.IpAddresses.Select(ip => ip.Address).ToList()
                            ?? new List<string>()
            };

            return View(model);
        }

        /// <summary>
        /// Processes the user edit form submission.
        /// Updates user information including IP addresses for applicable roles.
        /// </summary>
        /// <param name="model">The user edit view model containing updated user details.</param>
        /// <returns>Redirects to Index on successful update.</returns>
        [HttpPost]
        public async Task<IActionResult> Edit(UserEditViewModel model)
        {
            if (model.Role == "Accountant")
            {
                var accountant = await _context.Accountants
                    .Include(a => a.IpAddresses)
                    .FirstOrDefaultAsync(a => a.Id == model.Id);

                accountant.Name = model.Name;
                accountant.IpAddresses.Clear();
                model.IpAddresses = model.IpAddresses.Where(ip => !string.IsNullOrWhiteSpace(ip)).Distinct().ToList();
                accountant.IpAddresses.AddRange(model.IpAddresses.Select(ip => new IpAddress { Address = ip, UserId = model.Id }));
            }
            else if (model.Role == "BackOffice")
            {
                var backOffice = await _context.BackOffices
                    .Include(b => b.IpAddresses)
                    .FirstOrDefaultAsync(b => b.Id == model.Id);

                backOffice.Name = model.Name;
                backOffice.IpAddresses.Clear();
                model.IpAddresses = model.IpAddresses.Where(ip => !string.IsNullOrWhiteSpace(ip)).Distinct().ToList();
                backOffice.IpAddresses.AddRange(model.IpAddresses.Select(ip => new IpAddress { Address = ip, UserId = model.Id }));
            }
            else
            {
                var user = _context.Users.Find(model.Id);
                user.Name = model.Name;
                user.UserId = model.UserId;
            }

            await _context.SaveChangesAsync();
            return RedirectToAction("Index");
        }

        /// <summary>
        /// Processes user deletion.
        /// Removes user and associated data (IP addresses) from the system.
        /// </summary>
        /// <param name="id">The ID of the user to delete.</param>
        /// <returns>
        /// - Redirects to Index on successful deletion
        /// - NotFound if user doesn't exist
        /// </returns>
        [HttpPost]
        public async Task<IActionResult> Delete(int id)
        {
            var user = await _context.Users
                .FirstOrDefaultAsync(u => u.Id == id);

            if (user == null)
                return NotFound();

            // Clear IP addresses before deletion
            if (user.Role == "Accountant")
            {
                var accountant = _context.Accountants.Find(id);
                accountant.IpAddresses.Clear();
            }
            else if (user.Role == "BackOffice")
            {
                var backOffice = _context.BackOffices.Find(id);
                backOffice.IpAddresses.Clear();
            }

            _context.Users.Remove(user);
            await _context.SaveChangesAsync();

            return RedirectToAction("Index");
        }
    }
}
