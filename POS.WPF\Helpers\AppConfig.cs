﻿using Microsoft.Extensions.Configuration;
using System;
using System.Collections.Generic;
using System.Configuration;
using System.Diagnostics;
using System.IO;
using System.Linq;
using System.Text;
using System.Text.Json;
using System.Threading.Tasks;

namespace POS.WPF.Helpers
{
    public class AppConfig
    {
        private static IConfigurationRoot Configuration;
        static AppConfig()
        {
            try
            {
                string basePath = AppDomain.CurrentDomain.BaseDirectory; // Get application base directory
                Console.WriteLine($"Configuration file base path: {basePath}");

                Configuration = new ConfigurationBuilder()
                    .SetBasePath(basePath)
                    .AddJsonFile("appsettings.json", optional: false, reloadOnChange: true)
                    .Build();
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Error: {ex.Message}");
                throw new InvalidOperationException("Error loading configuration file.", ex);
            }
        }
        public static string GetConfigValue(string key)
        {
            return Configuration[key] ?? throw new KeyNotFoundException($"Configuration key '{key}' not found.");
        }

        public static void UpdateConfig(string key, string value)
        {
            string configPath = Path.Combine(Directory.GetCurrentDirectory(), "appsettings.json");
            var json = File.ReadAllText(configPath);

            // Parse and modify the JSON
            var configData = JsonSerializer.Deserialize<Dictionary<string, string>>(json);
            configData[key] = value;

            // Save the updated JSON back to the file
            var updatedJson = JsonSerializer.Serialize(configData, new JsonSerializerOptions { WriteIndented = true });
            File.WriteAllText(configPath, updatedJson);
        }
    }


}
